-- ----------------------------
-- Table structure for CUS_BUSINESS_TYPE
-- ----------------------------
CREATE TABLE "SHIPPING"."CUS_BUSINESS_TYPE" (
  "CUS_MAIN_ID" VARCHAR2(255 BYTE) VISIBLE NOT NULL,
  "CUS_BUSINESS_TYPE" VARCHAR2(255 BYTE) VISIBLE NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SHIPPING"."CUS_BUSINESS_TYPE"."CUS_MAIN_ID" IS '客户ID';
COMMENT ON COLUMN "SHIPPING"."CUS_BUSINESS_TYPE"."CUS_BUSINESS_TYPE" IS '业务类型';
COMMENT ON TABLE "SHIPPING"."CUS_BUSINESS_TYPE" IS '客户业务类型关联表';

-- ----------------------------
-- Primary Key structure for table CUS_BUSINESS_TYPE
-- ----------------------------
ALTER TABLE "SHIPPING"."CUS_BUSINESS_TYPE" ADD CONSTRAINT "PK_CUS_BUSINESS_TYPE" PRIMARY KEY ("CUS_MAIN_ID", "CUS_BUSINESS_TYPE");

-- ----------------------------
-- Foreign Keys structure for table CUS_BUSINESS_TYPE
-- ----------------------------
ALTER TABLE "SHIPPING"."CUS_BUSINESS_TYPE" ADD CONSTRAINT "FK_CUS_BUSINESS_TYPE_CUS_MAIN" FOREIGN KEY ("CUS_MAIN_ID") REFERENCES "SHIPPING"."CUS_MAIN" ("CUS_ID") ON DELETE CASCADE; 