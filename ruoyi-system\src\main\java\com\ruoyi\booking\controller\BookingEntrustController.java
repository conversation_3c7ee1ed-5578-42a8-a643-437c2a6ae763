package com.ruoyi.booking.controller;

import com.ruoyi.booking.domain.dto.BatchDeleteRequestDTO;
import com.ruoyi.booking.domain.dto.BookingEntrustDTO;
import com.ruoyi.booking.domain.vo.BatchDeleteResultVO;
import com.ruoyi.booking.service.IBookingEntrustService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 订舱委托控制器
 * 单证员工作台V2后端API
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/booking/entrust")
public class BookingEntrustController extends BaseController {

    @Autowired
    private IBookingEntrustService bookingEntrustService;


    /**
     * 根据订舱ID获取委托详情
     *
     * @param bookingId 订舱ID (booking_id)，注意：此参数实际为booking_main表的主键ID，不是order_main的ID
     * @return 委托详情DTO，包含三层架构数据：orderMain → bookingMain → logisticsMainList
     * @apiNote 该接口通过booking_id查询完整的委托数据，用于侧边栏查看/编辑模式
     * @since V2.0 单证员工作台查看模式
     */
    @PreAuthorize("@ss.hasPermi('booking:entrust:query')")
    @GetMapping(value = "/{bookingId}")
    public AjaxResult getInfo(@PathVariable("bookingId") String bookingId) {
        BookingEntrustDTO dto = bookingEntrustService.selectEntrustById(bookingId);
        if (dto == null) {
            return error("委托不存在");
        }
        return success(dto);
    }

    /**
     * 新增订舱委托
     *
     * @param dto 订舱委托数据传输对象，包含订单主表、订舱主表、物流主表列表等信息
     * @return 操作结果，包含成功/失败状态和相应消息
     * @throws Exception 当数据验证失败、业务逻辑错误或数据库操作失败时抛出
     * @apiNote 核心接口3：新增订舱委托
     * 功能特性：
     * 1. 自动生成委托编号（WT+yyyyMMdd+6位序号）和订舱号（BK+yyyyMMdd+6位序号）
     * 2. 支持批量保存物流明细、箱量信息、中转港信息
     * 3. 为每个物流段自动创建对应的水路运输记录（LOGISTIC_WATERWAY）
     * 4. 完整的事务回滚保障和乐观锁版本控制
     * 5. 自动填充托运单位信息（从bookingMain复制到orderMain）
     * @since V2.0 单证员工作台新增模式
     */
    @PreAuthorize("@ss.hasPermi('booking:entrust:add')")
    @Log(title = "订舱委托", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody BookingEntrustDTO dto) {
        try {
            log.info("[新增订舱委托] 开始处理新增请求, 客户: {}, 装货码头: {}, 卸货码头: {}",
                    dto.getBookingMain() != null ? dto.getBookingMain().getShipperName() : "未知",
                    dto.getBookingMain() != null ? dto.getBookingMain().getLoadingTerminalName() : "未知",
                    dto.getBookingMain() != null ? dto.getBookingMain().getUnloadingTerminalName() : "未知");

            boolean result = bookingEntrustService.insertEntrust(dto);

            if (result) {
                log.info("[新增订舱委托] 操作成功完成, 订单号: {}, 订舱号: {}",
                        dto.getOrderMain().getOrderNo(), dto.getBookingMain().getBookingNumber());
                return success("新增成功");
            } else {
                log.warn("[新增订舱委托] 操作失败, 服务层返回false");
                return error("新增失败");
            }
        } catch (Exception e) {
            log.error("[新增订舱委托] 操作异常, 错误信息: {}", e.getMessage(), e);
            return error("新增失败：" + e.getMessage());
        }
    }


    /**
     * 修改订舱委托（智能对比策略）
     *
     * @param dto 订舱委托数据传输对象，包含订单主表、订舱主表、物流主表列表等完整信息
     * @return 操作结果，包含成功/失败状态和相应消息
     * @throws Exception 当数据验证失败、业务逻辑错误或数据库操作失败时抛出
     * @apiNote 核心接口：修改订舱委托（基于智能对比策略）
     * 功能特性：
     * 1. 智能对比策略：基于用户实际操作进行精确的增删改，避免全量删除重建
     * 2. 三层数据更新：ORDER_MAIN → BOOKING_MAIN → 子表（运输环节、柜量信息、中转港）
     * 3. MyBatis-Flex IService：充分利用批量方法和条件构造器
     * 4. 乐观锁版本控制：防止并发修改冲突，确保数据一致性
     * 5. BaseEntity审计：自动填充updateBy、updateTime等审计字段
     * 6. 事务完整性：任何环节失败都会回滚所有操作
     * 7. 详细日志记录：记录每个子表的删除、新增、更新操作数量
     * @since V2.0 单证员工作台编辑模式
     */
    @PreAuthorize("@ss.hasPermi('booking:entrust:edit')")
    @Log(title = "订舱委托", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody BookingEntrustDTO dto) {
        try {
            log.info("[修改订舱委托] 开始处理修改请求, 操作用户: {}, 订舱ID: {}, 客户: {}",
                    getUsername(),
                    dto.getBookingMain() != null ? dto.getBookingMain().getId() : "未知",
                    dto.getBookingMain() != null ? dto.getBookingMain().getShipperName() : "未知");

            boolean result = bookingEntrustService.updateEntrust(dto);

            if (result) {
                log.info("[修改订舱委托] 操作成功完成, 操作用户: {}, 订单号: {}, 订舱号: {}",
                        getUsername(),
                        dto.getOrderMain() != null ? dto.getOrderMain().getOrderNo() : "未知",
                        dto.getBookingMain() != null ? dto.getBookingMain().getBookingNumber() : "未知");
                return success("修改成功");
            } else {
                log.warn("[修改订舱委托] 操作失败, 操作用户: {}, 服务层返回false", getUsername());
                return error("修改失败");
            }
        } catch (Exception e) {
            log.error("[修改订舱委托] 操作异常, 操作用户: {}, 订舱ID: {}, 错误信息: {}",
                    getUsername(),
                    dto.getBookingMain() != null ? dto.getBookingMain().getId() : "未知",
                    e.getMessage(), e);
            return error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除订舱委托（新版本智能级联删除）
     *
     * @param request 批量删除请求DTO，包含bookingIds数组
     * @return 删除操作的详细统计结果
     * @apiNote 新版本删除接口特性：
     * 1. 智能级联删除：自动删除所有子表记录和无关联的订单记录
     * 2. 批量操作优化：使用MyBatis-Flex批量删除提升性能
     * 3. 详细统计反馈：返回删除的订舱数量、订单数量和子表清理统计
     * 4. 完整事务保障：任何阶段失败都会回滚所有操作
     * 5. 优雅错误处理：提供用户友好的错误信息和操作建议
     * 6. 请求体参数：通过JSON格式接收参数，避免URL长度限制
     * @since V2.0 单证员工作台批量删除功能
     */
    @PreAuthorize("@ss.hasPermi('booking:entrust:remove')")
    @Log(title = "订舱委托批量删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult deleteBatch(@Valid @RequestBody BatchDeleteRequestDTO request) {
        try {
            String[] bookingIds = request.getBookingIds().toArray(new String[0]);

            log.info("[deleteBatch] 接收批量删除请求, 操作用户: {}, 删除数量: {}, IDs: {}",
                    getUsername(), bookingIds.length, Arrays.toString(bookingIds));

            BatchDeleteResultVO result = bookingEntrustService.deleteBatchBookingEntrusts(bookingIds);

            log.info("[deleteBatch] 批量删除操作完成, 操作用户: {}, 结果: {}",
                    getUsername(), result.getOperationSummary());

            return success(result.getOperationSummary()).put("data", result);

        } catch (Exception e) {
            String[] bookingIds = request.getBookingIds() != null ?
                    request.getBookingIds().toArray(new String[0]) : new String[0];
            log.error("[deleteBatch] 批量删除订舱委托异常, 操作用户: {}, IDs: {}, 错误信息: {}",
                    getUsername(), Arrays.toString(bookingIds), e.getMessage(), e);
            return error("删除失败：" + e.getMessage());
        }
    }


}