-- ====================================================
-- 订舱委托统一视图DDL脚本
-- 用途: 替代复杂的三层架构查询，提供统一的列表数据视图
-- 版本: v1.0
-- 创建时间: 2025-07-28
-- 
-- 说明: 整合ORDER_MAIN、BOOKING_MAIN、BOOKING_CNTR_NUM三张表的数据
--       为单证员工作台提供高性能的列表查询支持
-- ====================================================

-- 删除已存在的视图
BEGIN EXECUTE IMMEDIATE 'DROP VIEW v_booking_entrust'; EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- ====================================================
-- 创建订舱委托统一视图
-- ====================================================
CREATE VIEW v_booking_entrust AS
SELECT 
    -- 委托主表字段 (ORDER_MAIN)
    om.id as order_id,                                    -- 委托主表ID
    om.order_no,                                          -- 委托编号
    om.shipper_name as customer_name,                     -- 客户名称（托运单位）
    om.status as order_status,                            -- 委托状态
    om.order_date,                                        -- 委托日期
    om.total_containers,                                  -- 总箱量
    om.business_type as order_business_type,              -- 委托业务类型
    om.trade_type as order_trade_type,                    -- 委托贸易类型
    om.consortium,                                        -- 所属共同体
    om.customer_agreement,                                -- 客户协议
    om.settlement_method as order_settlement_method,      -- 委托结算方式
    
    -- 订舱主表字段 (BOOKING_MAIN)
    bm.id as booking_id,                                  -- 订舱主表ID
    bm.booking_number as entrust_no,                      -- 订舱号（委托号）
    bm.origin_port_name as pol,                           -- 起运港
    bm.destination_port_name as pod,                      -- 目的港
    bm.loading_terminal_name as loading_terminal,         -- 装货码头
    bm.unloading_terminal_name as unloading_terminal,     -- 卸货码头
    bm.loading_agent_name,                                -- 装货代理
    bm.unloading_agent_name,                              -- 卸货代理
    bm.trade_type as booking_trade_type,                  -- 订舱贸易类型
    bm.transport_mode,                                    -- 运输模式
    bm.customs_type,                                      -- 报关类型
    bm.settlement_method as booking_settlement_method,    -- 订舱结算方式
    bm.consignment_source,                                -- 委托来源
    bm.status as entrust_status,                          -- 订舱状态
    bm.booking_date,                                      -- 订舱日期
    bm.departure_date as etd,                             -- 启运日期（预计开船时间）
    bm.delivery_date as eta,                              -- 交货日期（预计到港时间）
    
    -- 柜量统计字段（从BOOKING_CNTR_NUM聚合）
    NVL(bcn_summary.container_count, 0) as container_count,           -- 总柜数
    NVL(bcn_summary.container_summary, '-') as container_summary,     -- 柜量汇总描述
    NVL(bcn_summary.total_weight, 0) as total_weight,                 -- 总重量
    NVL(bcn_summary.dangerous_count, 0) as dangerous_count,           -- 危险品柜数
    NVL(bcn_summary.refrigerated_count, 0) as refrigerated_count,     -- 冷藏柜数
    NVL(bcn_summary.oversize_count, 0) as oversize_count,             -- 超限柜数
    
    -- 业务状态判断字段
    CASE 
        WHEN om.status = 'DRAFT' OR bm.status = 'DRAFT' THEN 'DRAFT'
        WHEN om.status = 'SUBMITTED' OR bm.status = 'SUBMITTED' THEN 'SUBMITTED'  
        WHEN om.status = 'CONFIRMED' AND bm.status = 'CONFIRMED' THEN 'CONFIRMED'
        WHEN om.status = 'CANCELLED' OR bm.status = 'CANCELLED' THEN 'CANCELLED'
        WHEN bm.status = 'REJECTED' THEN 'REJECTED'
        ELSE 'UNKNOWN'
    END as overall_status,                                            -- 整体状态
    
    -- 优先级判断字段
    CASE 
        WHEN UPPER(NVL(om.remark, '')) LIKE '%紧急%' 
          OR UPPER(NVL(om.remark, '')) LIKE '%优先%' 
          OR UPPER(NVL(om.remark, '')) LIKE '%加急%'
          OR UPPER(NVL(bm.remark, '')) LIKE '%紧急%'
          OR UPPER(NVL(bm.remark, '')) LIKE '%优先%'
          OR UPPER(NVL(bm.remark, '')) LIKE '%加急%'
        THEN 'HIGH'
        ELSE 'NORMAL'
    END as priority_level,                                            -- 优先级
    
    -- 来源标识字段  
    CASE 
        WHEN bm.consignment_source = 'ONLINE' THEN 'customer'
        WHEN bm.consignment_source IN ('PHONE', 'ONSITE', 'EMAIL') THEN 'clerk'
        ELSE 'clerk'
    END as source_type,                                               -- 来源类型
    
    -- 备注信息
    CASE 
        WHEN om.remark IS NOT NULL AND bm.remark IS NOT NULL 
        THEN om.remark || ' | ' || bm.remark
        WHEN om.remark IS NOT NULL THEN om.remark
        WHEN bm.remark IS NOT NULL THEN bm.remark
        ELSE NULL
    END as combined_remark,                                           -- 合并备注
    
    -- 系统字段
    om.create_time,                                       -- 创建时间（以委托创建时间为准）
    GREATEST(om.update_time, bm.update_time) as update_time,          -- 更新时间（取最新）
    om.create_by,                                         -- 创建人
    CASE 
        WHEN bm.update_time > om.update_time THEN bm.update_by
        ELSE om.update_by
    END as update_by,                                                 -- 更新人（取最新更新者）
    
    -- 删除标志（两表都正常才显示）
    CASE 
        WHEN om.del_flag = '0' AND bm.del_flag = '0' THEN '0' 
        ELSE '2' 
    END as del_flag,                                                  -- 删除标志
    
    -- 版本字段（用于乐观锁，取较大值）
    GREATEST(NVL(om.version, 1), NVL(bm.version, 1)) as version       -- 版本号

FROM order_main om
INNER JOIN booking_main bm ON om.id = bm.order_id

-- 左连接柜量汇总子查询
LEFT JOIN (
    SELECT 
        booking_id,
        COUNT(*) as container_count,
        -- 柜量描述：尺寸+箱型×数量，多个用逗号分隔
        LISTAGG(
            container_size || container_type || 
            CASE WHEN is_empty = '1' THEN '(吉)' ELSE '' END || 
            '×' || quantity, 
            ', '
        ) WITHIN GROUP (ORDER BY container_size, container_type) as container_summary,
        
        -- 总重量（所有柜子的总重量之和）
        SUM(NVL(total_weight, 0)) as total_weight,
        
        -- 危险品柜数
        SUM(CASE WHEN is_dangerous = '1' THEN quantity ELSE 0 END) as dangerous_count,
        
        -- 冷藏柜数  
        SUM(CASE WHEN is_refrigerated = '1' THEN quantity ELSE 0 END) as refrigerated_count,
        
        -- 超限柜数
        SUM(CASE WHEN is_oversize = '1' THEN quantity ELSE 0 END) as oversize_count
        
    FROM booking_cntr_num 
    WHERE del_flag = '0'
    GROUP BY booking_id
) bcn_summary ON bm.id = bcn_summary.booking_id

-- 过滤条件：只显示未删除的记录
WHERE om.del_flag = '0' 
  AND bm.del_flag = '0';

-- ====================================================
-- 为视图添加注释
-- ====================================================
COMMENT ON TABLE v_booking_entrust IS '订舱委托统一视图 - 整合三层架构数据，为单证员工作台提供高性能列表查询';

-- ====================================================
-- 创建视图相关索引（提升查询性能）
-- ====================================================

-- 由于是视图，无法直接创建索引，但确保基础表有相应索引
-- 以下索引应在基础表上创建（如果尚未创建）

-- ORDER_MAIN表索引
-- CREATE INDEX idx_order_main_status ON order_main(status) WHERE del_flag = '0';
-- CREATE INDEX idx_order_main_date ON order_main(order_date) WHERE del_flag = '0';
-- CREATE INDEX idx_order_main_shipper ON order_main(shipper_name) WHERE del_flag = '0';

-- BOOKING_MAIN表索引  
-- CREATE INDEX idx_booking_main_status ON booking_main(status) WHERE del_flag = '0';
-- CREATE INDEX idx_booking_main_date ON booking_main(booking_date) WHERE del_flag = '0';
-- CREATE INDEX idx_booking_main_number ON booking_main(booking_number) WHERE del_flag = '0';
-- CREATE INDEX idx_booking_main_ports ON booking_main(origin_port_name, destination_port_name) WHERE del_flag = '0';

-- BOOKING_CNTR_NUM表索引
-- CREATE INDEX idx_booking_cntr_booking_id ON booking_cntr_num(booking_id) WHERE del_flag = '0';

-- ====================================================
-- 测试查询（验证视图是否工作正常）
-- ====================================================

-- 基础查询测试
-- SELECT COUNT(*) as total_records FROM v_booking_entrust;

-- 分页查询测试  
-- SELECT * FROM v_booking_entrust 
-- WHERE ROWNUM <= 10
-- ORDER BY create_time DESC;

-- 状态筛选测试
-- SELECT overall_status, COUNT(*) as count 
-- FROM v_booking_entrust 
-- GROUP BY overall_status;

-- 客户名称模糊查询测试
-- SELECT customer_name, entrust_no, overall_status
-- FROM v_booking_entrust 
-- WHERE UPPER(customer_name) LIKE '%测试%'
-- ORDER BY create_time DESC;

-- ====================================================
-- 完成提示
-- ====================================================
SELECT '订舱委托统一视图创建完成!' as message FROM DUAL;

-- ====================================================
-- 使用说明:
-- 1. 该视图整合了ORDER_MAIN、BOOKING_MAIN、BOOKING_CNTR_NUM三表数据
-- 2. 提供了丰富的业务字段，支持复杂的查询和排序需求
-- 3. 柜量信息已聚合为字符串格式，便于前端显示
-- 4. 状态字段已标准化，支持统一的状态管理
-- 5. 优先级和来源类型字段便于业务筛选
-- 6. 性能优化：建议在基础表相关字段上创建索引
-- ====================================================