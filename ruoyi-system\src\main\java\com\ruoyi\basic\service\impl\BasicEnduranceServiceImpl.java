package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicEndurance;
import com.ruoyi.basic.mapper.BasicEnduranceMapper;
import com.ruoyi.basic.service.IBasicEnduranceService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicEnduranceServiceImpl extends ServiceImpl<BasicEnduranceMapper, BasicEndurance> implements IBasicEnduranceService {
    @Autowired
    private BasicEnduranceMapper basicEnduranceMapper;

    /**
     * 查询航时信息
     *
     * @param id 航时信息主键
     * @return 航时信息
     */
    @Override
    public BasicEndurance selectBasicEnduranceById(String id)
    {
        return basicEnduranceMapper.selectOneById(id);
    }

    /**
     * 查询航时信息列表
     *
     * @param basicEndurance 航时信息
     * @return 航时信息
     */
    @Override
    public List<BasicEndurance> selectBasicEnduranceList(BasicEndurance basicEndurance)
    {
        return basicEnduranceMapper.selectListByQuery(QueryWrapper.create());
    }

    /**
     * 新增航时信息
     *
     * @param basicEndurance 航时信息
     * @return 结果
     */
    @Override
    public int insertBasicEndurance(BasicEndurance basicEndurance) throws Exception {
        checkEndurance(basicEndurance);
        return basicEnduranceMapper.insert(basicEndurance);
    }

    /**
     * 修改航时信息
     *
     * @param basicEndurance 航时信息
     * @return 结果
     */
    @Override
    public int updateBasicEndurance(BasicEndurance basicEndurance) throws Exception {
        checkEndurance(basicEndurance);
        return basicEnduranceMapper.update(basicEndurance);
    }

    public void checkEndurance(BasicEndurance basicEndurance) throws Exception {
        if(StringUtils.isEmpty(basicEndurance.getStartTerminalId())){
            throw new Exception("起始码头不能为空");
        }

        if(StringUtils.isEmpty(basicEndurance.getEndTerminalId())){
            throw new Exception("目的码头不能为空");
        }

        if(StringUtils.isNull(basicEndurance.getVoyageTime())){
            throw new Exception("航线时间不能为空");
        }

        if(StringUtils.isEmpty(basicEndurance.getIsSame())){
            throw new Exception("往返时间是否一致不能为空");
        }

        if("Y".equals(basicEndurance.getIsSame())){

            //校验是否存在 起始码头=返回码头 返回码头=起始码头的数据
            long checkIsSame = super.count(QueryWrapper.create()
                    .eq("start_terminal_id", basicEndurance.getEndTerminalId())
                    .eq("end_terminal_id", basicEndurance.getStartTerminalId())
            );

            if(checkIsSame > 0 ){
                throw new Exception("存在起始码头=返回码头，返回码头=起始码头的数据");
            }

        }else {

            //校验是否存在 起始码头=返回码头 返回码头=起始码头的数据
            long checkIsSame = super.count(QueryWrapper.create()
                    .eq("start_terminal_id", basicEndurance.getEndTerminalId())
                    .eq("end_terminal_id", basicEndurance.getStartTerminalId())
                    .eq("is_same", "Y")
            );

            if(checkIsSame > 0 ){
                throw new Exception("存在起始码头=返回码头，返回码头=起始码头的数据");
            }

        }

        //校验是否存在起始码头 返回码头相同的数据
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("start_terminal_id", basicEndurance.getStartTerminalId())
                .eq("end_terminal_id", basicEndurance.getEndTerminalId());
        if (StringUtils.isNotEmpty(basicEndurance.getId())) {
            queryWrapper.ne("id", basicEndurance.getId());
        }
        long check = super.count(queryWrapper);

        if(check > 0){
            throw new Exception("存在起始码头和目的码头相同的数据");
        }
    }

    /**
     * 批量删除航时信息
     *
     * @param ids 需要删除的航时信息主键
     * @return 结果
     */
    @Override
    public int deleteBasicEnduranceByIds(List<String> ids)
    {
        return basicEnduranceMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除航时信息信息
     *
     * @param id 航时信息主键
     * @return 结果
     */
    @Override
    public int deleteBasicEnduranceById(String id)
    {
        return basicEnduranceMapper.deleteById(id);
    }
}
