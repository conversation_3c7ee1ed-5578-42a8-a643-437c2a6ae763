package com.ruoyi.route.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.route.domain.RouteBranch;
import com.ruoyi.route.mapper.RouteRelationMapper;
import com.ruoyi.route.service.IRouteBranchService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 航线支线Controller
 */
@Slf4j
@RestController
@RequestMapping("/route/branch")
public class RouteBranchController extends BaseController {
    @Autowired
    private IRouteBranchService routeBranchService;

    @Autowired
    private RouteRelationMapper routeRelationMapper;

    /**
     * 分页查询航线支线
     */
    @PreAuthorize("@ss.hasPermi('route:branch:list')")
    @GetMapping("/page")
    public AjaxResult page(@RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            RouteBranch routeBranch) {
        log.debug("分页查询航线支线 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, routeBranch);
        Page<RouteBranch> page = routeBranchService.selectRouteBranchPage(pageNumber, pageSize, routeBranch);
        log.debug("分页查询航线支线完成 - 总记录数: {}", page.getTotalRow());
        return AjaxResult.success(page);
    }

    /**
     * 导出航线支线列表
     */
    @PreAuthorize("@ss.hasPermi('route:branch:export')")
    @Log(title = "航线支线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RouteBranch routeBranch) {
        log.debug("导出航线支线数据 - 查询条件: {}", routeBranch);
        List<RouteBranch> list = routeBranchService.selectRouteBranchPage(1, Integer.MAX_VALUE, routeBranch).getRecords();
        log.debug("导出航线支线数据 - 导出记录数: {}", list.size());
        ExcelUtil<RouteBranch> util = new ExcelUtil<RouteBranch>(RouteBranch.class);
        util.exportExcel(response, list, "航线支线数据");
        log.debug("导出航线支线数据完成");
    }

    /**
     * 获取航线支线详细信息
     */
    @PreAuthorize("@ss.hasPermi('route:branch:query')")
    @GetMapping(value = "/{branchId}")
    public AjaxResult getInfo(@PathVariable("branchId") String branchId) {
        log.debug("获取航线支线详细信息 - ID: {}", branchId);
        RouteBranch routeBranch = routeBranchService.getById(branchId);
        log.debug("获取航线支线详细信息完成 - 结果: {}", routeBranch);
        return success(routeBranch);
    }

    /**
     * 新增航线支线
     */
    @PreAuthorize("@ss.hasPermi('route:branch:add')")
    @Log(title = "航线支线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RouteBranch routeBranch) {
        log.debug("新增航线支线 - 数据: {}", routeBranch);
        if (routeBranchService.existsBranchName(routeBranch.getBranchName(), routeBranch.getMainId(), null)) {
            return AjaxResult.error("该主线下支线名称已存在，请勿重复！");
        }
        boolean success = routeBranchService.save(routeBranch);
        log.debug("新增航线支线完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 修改航线支线
     */
    @PreAuthorize("@ss.hasPermi('route:branch:edit')")
    @Log(title = "航线支线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RouteBranch routeBranch) {
        log.debug("修改航线支线 - 数据: {}", routeBranch);
        if (routeBranchService.existsBranchName(routeBranch.getBranchName(), routeBranch.getMainId(), routeBranch.getBranchId())) {
            return AjaxResult.error("该主线下支线名称已存在，请勿重复！");
        }
        boolean success = routeBranchService.updateById(routeBranch);
        log.debug("修改航线支线完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 删除航线支线
     */
    @PreAuthorize("@ss.hasPermi('route:branch:remove')")
    @Log(title = "航线支线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchIds}")
    public AjaxResult remove(@PathVariable String[] branchIds) {
        log.debug("删除航线支线 - ID数组: {}", Arrays.toString(branchIds));
        // 检查是否存在关联的码头连接关系
        for (String branchId : branchIds) {
            // 查询关联的码头连接关系数量
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .select()
                    .from("ROUTE_RELATION")
                    .where("BRANCH_ID = ?", branchId);
            int relationCount = (int) routeRelationMapper.selectCountByQuery(queryWrapper);
            if (relationCount > 0) {
                return AjaxResult.error("删除失败，该支线下存在" + relationCount + "条码头连接关系，请先删除码头连接关系");
            }
        }
        int rows = routeBranchService.deleteRouteBranchByIds(branchIds);
        log.debug("删除航线支线完成 - 结果: {}", rows);
        return toAjax(rows);
    }
} 