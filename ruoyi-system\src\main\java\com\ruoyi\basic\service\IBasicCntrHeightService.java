package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCntrHeight;

import java.util.List;

public interface IBasicCntrHeightService extends IService<BasicCntrHeight> {


    BasicCntrHeight selectBasicCntrHeightById(String id);

    List<BasicCntrHeight> selectBasicCntrHeightList(BasicCntrHeight basicCntrHeight);

    int insertBasicCntrHeight(BasicCntrHeight basicCntrHeight);

    int updateBasicCntrHeight(BasicCntrHeight basicCntrHeight);

    int deleteBasicCntrHeightByIds(List<String> ids);

    int deleteBasicCntrHeightById(String id);
}
