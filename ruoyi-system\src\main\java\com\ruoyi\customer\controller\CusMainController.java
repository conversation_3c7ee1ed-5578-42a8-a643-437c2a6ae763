package com.ruoyi.customer.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.customer.domain.CusMain;
import com.ruoyi.customer.service.impl.CusMainServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025/4/27 16:34
 */
@RestController
@RequestMapping("/customer/cusMain")
public class CusMainController extends BaseController {

    @Autowired
    CusMainServiceImpl cusMainService;

    /**
     * @param
     * @return tableDataInfo
     * @description list
     * <AUTHOR>
     * @date 2023/8/21 15:45
     */
    @GetMapping("list")
    public Page<CusMain> list(CusMain cusMain, Integer pageNum, Integer pageSize) {
//        startPage();
//        return getDataTable(cusMainService.selectCusMainList(cusMain));
        return cusMainService.selectCusMainList(cusMain, pageNum, pageSize);
    }

    /**
     * 远程搜索客户数据接口
     * 支持根据客户名称、简称、代码进行模糊搜索
     *
     * @param searchKey 搜索关键词
     * @return 符合条件的客户列表
     */
    @GetMapping("remote/search")
    public TableDataInfo remoteSearch(String searchKey) {
        return getDataTable(cusMainService.remoteSearchCusData(searchKey));
    }

    /**
     * @param
     * @return
     * @description add
     * @date 2023/8/21 15:45
     */
    @PostMapping("add")
    public AjaxResult add(@RequestBody CusMain cusMain) {
        return cusMainService.insertCusMain(cusMain);
    }

    /**
     * @param
     * @return
     * @description edit
     * @date 2023/8/21 15:45
     */
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody CusMain cusMain) {
        return cusMainService.updateCusMain(cusMain);
    }

    /**
     * @param
     * @return
     * @description delete
     * @date 2023/8/21 15:45
     */
    @DeleteMapping("delete/{cusId}")
    public AjaxResult delete(@PathVariable String cusId){
        return cusMainService.deleteCusMain(cusId);
    }

    /**
     * @param
     * @return
     * @description get
     * @date 2023/8/21 15:45
     */
    @GetMapping("get/{cusId}")
    public AjaxResult get(@PathVariable String cusId){
        return cusMainService.getCusMainById(cusId);
    }

    @GetMapping("label")
    public AjaxResult label(CusMain cusMain){

        QueryWrapper queryWrapper = QueryWrapper.create()
                .like(CusMain::getCusName,cusMain.getCusName());

        return success(cusMainService.list(queryWrapper));

    }

}
