import { defineStore } from 'pinia'
import {
  mockPendingCargo,
  mockBufferZones,
  mockVesselManifest,
  updateBufferStatistics,
  generateVoyageNo
} from '@/views/stowage/mockData.js'
// 定义集装箱类型与 TEU 的映射
export const containerTypes = {
  '20Empty': { label: '20尺空', teu: 1 },
  '20Full': { label: '20尺重', teu: 1 },
  '40Empty': { label: '40尺空', teu: 2 },
  '40Full': { label: '40尺重', teu: 2 },
  'other': { label: '其他箱', teu: 1 }
};
export const useStowageStore = defineStore('stowage', {
  state: () => ({
    // 待运输货物列表
    pendingCargoList: [],
    // 缓冲区数据
    bufferZones: [],
    // 船舶清单
    vesselManifest: [],
    // 选择的货物
    selectedCargo: [],
    // 选择的船舶
    selectedVessel: null,
    // 界面布局设置
    layout: {
      leftWidth: 50,
      centerWidth: 20,
      rightWidth: 30,
      isCollapsed: {
        left: false,
        center: false,
        right: false
      }
    },
    // 操作历史
    operationHistory: [],
    // 界面状态
    ui: {
      loading: false,
      dragActive: false,
      activeBuffer: null
    }
  }),

  getters: {
    // 获取未分配的货物
    unallocatedCargo: (state) => {
      return state.pendingCargoList.filter(cargo => cargo.status === 'pending')
    },

    // 获取缓冲区总数量
    bufferTotalItems: (state) => {
      return state.bufferZones.reduce((total, zone) => total + zone.items.length, 0)
    },

    // 获取缓冲区总TEU
    bufferTotalTEU() {
      return this.bufferZones.reduce((total, zone) => {
        return total + zone.items.reduce((zoneTotal, item) => {
          return zoneTotal + this.calculateTEU(item.containers)
        }, 0)
      }, 0)
    },


    // 获取船舶使用率统计
    vesselUtilization() {
      return this.vesselManifest.map(vessel => {
        const totalCapacity = this.calculateTEU(vessel.totalCapacity)
        const usedCapacity = vessel.voyages.reduce((total, voyage) => {
          return total + this.calculateTEU(voyage.containers)
        }, 0)
        return {
          vesselId: vessel.id,
          vesselName: vessel.vesselName,
          totalCapacity,
          usedCapacity,
          utilization: totalCapacity > 0 ? (usedCapacity / totalCapacity) * 100 : 0
        }
      })
    },

    // 获取可用船舶
    availableVessels() {
      return this.vesselManifest.filter(vessel => {
        const totalCapacity = this.calculateTEU(vessel.totalCapacity)
        const usedCapacity = vessel.voyages.reduce((total, voyage) => {
          return total + this.calculateTEU(voyage.containers)
        }, 0)
        return usedCapacity < totalCapacity
      })
    },

    // 获取已配载货物数量
    allocatedItemsCount: (state) => {
      return state.vesselManifest.reduce((total, vessel) => {
        return total + vessel.voyages.reduce((voyageTotal, voyage) => {
          return voyageTotal + (voyage.cargoItems ? voyage.cargoItems.length : 0)
        }, 0)
      }, 0)
    }
  },

  actions: {
    // 初始化数据
    async initializeData() {
      this.ui.loading = true
      try {
        this.pendingCargoList = mockPendingCargo()
        this.bufferZones = mockBufferZones()
        this.vesselManifest = mockVesselManifest()
        this.selectedCargo = []
        this.selectedVessel = null
        this.operationHistory = []
      } catch (error) {
        console.error('初始化数据失败:', error)
      } finally {
        this.ui.loading = false
      }
    },

    // 选择货物
    selectCargo(cargo) {
      const index = this.selectedCargo.findIndex(item => item.id === cargo.id)
      if (index > -1) {
        this.selectedCargo.splice(index, 1)
      } else {
        this.selectedCargo.push(cargo)
      }
    },
    /**
       * 计算单行货物的 TEU
       * @param {Object} containerDetailsParsed - 集装箱数量对象
       * @returns {number} TEU 总数
       */
    calculateTEU(containerDetailsParsed) {
      if (!containerDetailsParsed) return 0;

      let totalTEU = 0;
      // 只处理已知的集装箱类型，排除 otherDetail 等非数值字段
      Object.keys(containerDetailsParsed).forEach((type) => {
        // 只处理在 containerTypes 中定义的类型
        if (containerTypes.hasOwnProperty(type)) {
          const quantity = containerDetailsParsed[type] || 0;
          totalTEU += quantity * (containerTypes[type]?.teu || 1);
        }
      });
      return totalTEU;
    },
    // 清空选择
    clearSelection() {
      this.selectedCargo = []
    },

    // 全选货物
    selectAllCargo() {
      this.selectedCargo = this.unallocatedCargo.slice()
    },

    // 选择船舶
    selectVessel(vessel) {
      this.selectedVessel = vessel
    },

    // 添加货物到缓冲区
    addToBuffer(cargoItems, bufferZoneId) {
      const bufferZone = this.bufferZones.find(zone => zone.id === bufferZoneId)
      if (!bufferZone) return false

      // 记录操作历史
      this.operationHistory.push({
        type: 'add_to_buffer',
        timestamp: new Date().toISOString(),
        data: {
          cargoItems: cargoItems.map(item => ({ ...item })),
          bufferZoneId
        }
      })

      // 更新货物状态
      cargoItems.forEach(item => {
        const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === item.id)
        if (cargoIndex !== -1) {
          this.pendingCargoList[cargoIndex].status = 'allocated'
          this.pendingCargoList[cargoIndex].bufferZone = bufferZoneId
        }

        // 添加到缓冲区
        bufferZone.items.push({
          ...item,
          bufferZoneId
        })
      })

      // 更新统计
      bufferZone.statistics = updateBufferStatistics(bufferZone)

      // 清空选择
      this.clearSelection()

      return true
    },

    // 从缓冲区移除货物
    removeFromBuffer(itemId, bufferZoneId) {
      const bufferZone = this.bufferZones.find(zone => zone.id === bufferZoneId)
      if (!bufferZone) return false

      const itemIndex = bufferZone.items.findIndex(item => item.id === itemId)
      if (itemIndex === -1) return false

      const item = bufferZone.items[itemIndex]

      // 记录操作历史
      this.operationHistory.push({
        type: 'remove_from_buffer',
        timestamp: new Date().toISOString(),
        data: {
          item: { ...item },
          bufferZoneId
        }
      })

      // 从缓冲区移除
      bufferZone.items.splice(itemIndex, 1)

      // 恢复货物状态
      const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === item.id)
      if (cargoIndex !== -1) {
        this.pendingCargoList[cargoIndex].status = 'PENDING' // 应该是 PENDING 而不是 pending
        this.pendingCargoList[cargoIndex].bufferZone = null
        this.pendingCargoList[cargoIndex].bufferZoneId = null // 确保 bufferZoneId 也被清除
      }

      // 更新统计
      bufferZone.statistics = updateBufferStatistics(bufferZone)

      // 清空选择
      this.clearSelection()

      return true
    },

    // 清空缓冲区
    clearBuffer(bufferZoneId) {
      const bufferZone = this.bufferZones.find(zone => zone.id === bufferZoneId)
      if (!bufferZone) return false

      // 记录操作历史
      this.operationHistory.push({
        type: 'clear_buffer',
        timestamp: new Date().toISOString(),
        data: {
          items: bufferZone.items.map(item => ({ ...item })),
          bufferZoneId
        }
      })

      // 恢复所有货物状态
      bufferZone.items.forEach(item => {
        const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === item.id)
        if (cargoIndex !== -1) {
          this.pendingCargoList[cargoIndex].status = 'PENDING' // 注意这里应该是 PENDING 而不是 pending
          this.pendingCargoList[cargoIndex].bufferZone = null
          this.pendingCargoList[cargoIndex].bufferZoneId = null // 确保 bufferZoneId 也被清除
        }
      })

      // 清空缓冲区
      bufferZone.items = []
      bufferZone.statistics = updateBufferStatistics(bufferZone)

      // 清空选择
      this.clearSelection()

      return true
    },

    // 将缓冲区绑定到船舶
    bindBufferToVessel(bufferZoneId, vesselId) {
      const bufferZone = this.bufferZones.find(zone => zone.id === bufferZoneId)
      const vessel = this.vesselManifest.find(v => v.id === vesselId)

      if (!bufferZone || !vessel || bufferZone.items.length === 0) {
        return false
      }

      // 计算总箱量
      const totalContainers = {
        '20Empty': 0,
        '20Full': 0,
        '40Empty': 0,
        '40Full': 0,
        'other': 0
      }

      bufferZone.items.forEach(item => {
        Object.keys(item.containers).forEach(type => {
          totalContainers[type] += item.containers[type]
        })
      })

      // 生成新航次
      const firstCargo = bufferZone.items[0]
      const newVoyage = {
        id: `VOYAGE_${Date.now()}`,
        voyageNo: generateVoyageNo(),
        containers: totalContainers,
        status: 'planned',
        bufferSource: bufferZoneId,
        cargoItems: [...bufferZone.items],
        createTime: new Date().toISOString(),
        loadingTerminal: firstCargo ? firstCargo.loadingTerminal : '',
        unloadingTerminal: firstCargo ? firstCargo.unloadingTerminal : '',
        estimatedArrival: firstCargo ? (firstCargo.estimatedArrival || new Date().toISOString()) : new Date().toISOString()
      }

      // 记录操作历史
      this.operationHistory.push({
        type: 'bind_to_vessel',
        timestamp: new Date().toISOString(),
        data: {
          voyage: { ...newVoyage },
          vesselId,
          bufferZoneId
        }
      })

      // 添加到船舶
      vessel.voyages.push(newVoyage)

      // 清空缓冲区
      bufferZone.items = []
      bufferZone.statistics = updateBufferStatistics(bufferZone)

      return newVoyage
    },

    // 拆解货物
    splitCargo(cargoId, splitRecords) {
      const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === cargoId)
      if (cargoIndex === -1) return false

      const originalCargo = this.pendingCargoList[cargoIndex]
      const newCargoItems = this.createSplitCargoItems(originalCargo, splitRecords)

      // 记录操作历史
      this.operationHistory.push({
        type: 'split_cargo',
        timestamp: new Date().toISOString(),
        data: {
          originalCargo: { ...originalCargo },
          splitRecords,
          newCargoItems
        }
      })

      // 删除原始记录
      this.pendingCargoList.splice(cargoIndex, 1)

      // 添加拆分后的新记录
      this.pendingCargoList.push(...newCargoItems)

      return true
    },

    // 创建拆解后的货物项
    createSplitCargoItems(originalCargo, splitRecords) {
      const newCargoItems = []
      let itemId = 1

      splitRecords.forEach((record, index) => {
        // 检查这个记录是否有任何集装箱数量
        const hasContainers = Object.values(record.containers).some(quantity => quantity > 0)
        if (!hasContainers) return

        const newCargo = {
          id: `${originalCargo.id}_split_${itemId++}`,
          bookingNo: `${originalCargo.bookingNo}-${index + 1}`,
          originalBookingNo: originalCargo.bookingNo, // 保存原始订舱号
          year: originalCargo.year,
          month: originalCargo.month,
          day: originalCargo.day,
          loadingTerminal: originalCargo.loadingTerminal,
          unloadingTerminal: originalCargo.unloadingTerminal,
          customer: originalCargo.customer,
          tradeType: originalCargo.tradeType,
          project: originalCargo.project,
          route: originalCargo.route,
          containers: { ...record.containers },
          remark: `${originalCargo.remark} - 拆解项${index + 1}`,
          status: 'pending',
          bufferZone: null,
          children: []
        }

        newCargoItems.push(newCargo)
      })

      return newCargoItems
    },

    // // 合并货物
    // mergeCargo(cargoItems) {
    //   if (cargoItems.length < 2) return false

    //   // 检查是否可以合并
    //   if (!this.canMergeCargo(cargoItems)) return false

    //   // 记录操作历史
    //   this.operationHistory.push({
    //     type: 'merge_cargo',
    //     timestamp: new Date().toISOString(),
    //     data: {
    //       originalItems: cargoItems.map(item => ({ ...item })),
    //       mergedCargo: null // 将在下面创建
    //     }
    //   })

    //   // 创建合并后的货物
    //   const mergedCargo = this.createMergedCargo(cargoItems)

    //   // 更新操作历史中的合并货物
    //   this.operationHistory[this.operationHistory.length - 1].data.mergedCargo = { ...mergedCargo }

    //   // 删除原始记录
    //   cargoItems.forEach(item => {
    //     const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === item.id)
    //     if (cargoIndex !== -1) {
    //       this.pendingCargoList.splice(cargoIndex, 1)
    //     }
    //   })

    //   // 添加合并后的记录
    //   this.pendingCargoList.push(mergedCargo)

    //   return true
    // },

    // 检查是否可以合并
    canMergeCargo(cargoItems) {
      if (cargoItems.length < 2) return false;

      const baselogisticsMainId = cargoItems[0].logisticsMainId;

      return cargoItems.every(
        (item) => item.logisticsMainId === baselogisticsMainId
      );
    },

    // 获取基础订舱号（去掉拆解后缀）
    getBaseBookingNo(bookingNo) {
      // 如果包含拆解后缀（如 -1, -2），则返回基础订舱号
      const match = bookingNo.match(/^(.+?)-\d+$/)
      return match ? match[1] : bookingNo
    },

    // // 创建合并后的货物
    // createMergedCargo(cargoItems) {
    //   const firstItem = cargoItems[0]
    //   const baseBookingNo = this.getBaseBookingNo(firstItem.bookingNo)

    //   // 合并集装箱数量
    //   const mergedContainers = {
    //     '20Empty': 0,
    //     '20Full': 0,
    //     '40Empty': 0,
    //     '40Full': 0,
    //     'other': 0
    //   }

    //   cargoItems.forEach(item => {
    //     Object.keys(mergedContainers).forEach(containerType => {
    //       mergedContainers[containerType] += (item.containers[containerType] || 0)
    //     })
    //   })

    //   // 合并备注
    //   const remarks = cargoItems.map(item => item.remark).filter(remark => remark)
    //   const mergedRemark = remarks.length > 0 ? remarks.join('; ') : firstItem.remark

    //   return {
    //     id: `merged_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    //     bookingNo: baseBookingNo,
    //     year: firstItem.year,
    //     month: firstItem.month,
    //     day: firstItem.day,
    //     loadingTerminal: firstItem.loadingTerminal,
    //     unloadingTerminal: firstItem.unloadingTerminal,
    //     customer: firstItem.customer,
    //     tradeType: firstItem.tradeType,
    //     project: firstItem.project,
    //     route: firstItem.route,
    //     containers: mergedContainers,
    //     remark: mergedRemark,
    //     status: 'pending',
    //     bufferZone: null,
    //     children: []
    //   }
    // },

    // 撤销操作
    undoLastOperation() {
      if (this.operationHistory.length === 0) return false

      const lastOperation = this.operationHistory.pop()

      switch (lastOperation.type) {
        case 'add_to_buffer':
          this.undoAddToBuffer(lastOperation.data)
          break
        case 'remove_from_buffer':
          this.undoRemoveFromBuffer(lastOperation.data)
          break
        case 'clear_buffer':
          this.undoClearBuffer(lastOperation.data)
          break
        case 'bind_to_vessel':
          this.undoBindToVessel(lastOperation.data)
          break
        case 'split_cargo':
          this.undoSplitCargo(lastOperation.data)
          break
        case 'merge_cargo':
          this.undoMergeCargo(lastOperation.data)
          break
        case 'move_between_buffers':
          this.undoMoveeBetweenBuffers(lastOperation.data)
          break
      }

      return true
    },

    // 撤销添加到缓冲区
    undoAddToBuffer(data) {
      const bufferZone = this.bufferZones.find(zone => zone.id === data.bufferZoneId)
      if (!bufferZone) return

      data.cargoItems.forEach(item => {
        const itemIndex = bufferZone.items.findIndex(bufferItem => bufferItem.id === item.id)
        if (itemIndex !== -1) {
          bufferZone.items.splice(itemIndex, 1)
        }

        const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === item.id)
        if (cargoIndex !== -1) {
          this.pendingCargoList[cargoIndex].status = 'pending'
          this.pendingCargoList[cargoIndex].bufferZone = null
        }
      })

      bufferZone.statistics = updateBufferStatistics(bufferZone)
    },

    // 撤销从缓冲区移除
    undoRemoveFromBuffer(data) {
      const bufferZone = this.bufferZones.find(zone => zone.id === data.bufferZoneId)
      if (!bufferZone) return

      bufferZone.items.push(data.item)

      const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === data.item.id)
      if (cargoIndex !== -1) {
        this.pendingCargoList[cargoIndex].status = 'allocated'
        this.pendingCargoList[cargoIndex].bufferZone = data.bufferZoneId
      }

      bufferZone.statistics = updateBufferStatistics(bufferZone)
    },

    // 撤销清空缓冲区
    undoClearBuffer(data) {
      const bufferZone = this.bufferZones.find(zone => zone.id === data.bufferZoneId)
      if (!bufferZone) return

      bufferZone.items = [...data.items]

      data.items.forEach(item => {
        const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === item.id)
        if (cargoIndex !== -1) {
          this.pendingCargoList[cargoIndex].status = 'allocated'
          this.pendingCargoList[cargoIndex].bufferZone = data.bufferZoneId
        }
      })

      bufferZone.statistics = updateBufferStatistics(bufferZone)
    },

    // 撤销绑定到船舶
    undoBindToVessel(data) {
      const vessel = this.vesselManifest.find(v => v.id === data.vesselId)
      const bufferZone = this.bufferZones.find(zone => zone.id === data.bufferZoneId)

      if (!vessel || !bufferZone) return

      // 从船舶移除航次
      const voyageIndex = vessel.voyages.findIndex(v => v.id === data.voyage.id)
      if (voyageIndex !== -1) {
        vessel.voyages.splice(voyageIndex, 1)
      }

      // 恢复缓冲区数据
      bufferZone.items = [...data.voyage.cargoItems]
      bufferZone.statistics = updateBufferStatistics(bufferZone)
    },

    // 撤销拆解货物
    undoSplitCargo(data) {
      // 删除拆分后的记录
      data.newCargoItems.forEach(newCargo => {
        const cargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === newCargo.id)
        if (cargoIndex !== -1) {
          this.pendingCargoList.splice(cargoIndex, 1)
        }
      })

      // 恢复原始记录
      this.pendingCargoList.push({ ...data.originalCargo })
    },

    // 撤销合并货物
    undoMergeCargo(data) {
      // 删除合并后的记录
      const mergedCargoIndex = this.pendingCargoList.findIndex(cargo => cargo.id === data.mergedCargo.id)
      if (mergedCargoIndex !== -1) {
        this.pendingCargoList.splice(mergedCargoIndex, 1)
      }

      // 恢复原始记录
      data.originalItems.forEach(item => {
        this.pendingCargoList.push({ ...item })
      })
    },

    // 撤销缓冲区间移动
    undoMoveeBetweenBuffers(data) {
      const fromZone = this.bufferZones.find(zone => zone.id === data.fromZoneId)
      const toZone = this.bufferZones.find(zone => zone.id === data.toZoneId)

      if (!fromZone || !toZone) return

      const itemIndex = toZone.items.findIndex(item => item.id === data.item.id)
      if (itemIndex !== -1) {
        // 从目标区移除
        toZone.items.splice(itemIndex, 1)

        // 恢复到原区域
        data.item.bufferZoneId = data.fromZoneId
        fromZone.items.push(data.item)

        // 更新统计
        fromZone.statistics = updateBufferStatistics(fromZone)
        toZone.statistics = updateBufferStatistics(toZone)
      }
    },

    // 更新界面布局
    updateLayout(layoutConfig) {
      this.layout = { ...this.layout, ...layoutConfig }
    },

    // 设置拖拽状态
    setDragState(active, bufferId = null) {
      this.ui.dragActive = active
      this.ui.activeBuffer = bufferId
    },

    // 智能配载
    smartAllocate() {
      if (this.selectedCargo.length === 0) {
        return { success: false, message: '请先选择货物' }
      }

      let allocatedCount = 0
      const cargoByRoute = this.groupCargoByRoute(this.selectedCargo)

      // 按航线分组配载
      Object.keys(cargoByRoute).forEach((route, index) => {
        const routeCargo = cargoByRoute[route]
        const bufferZoneId = (index % 3) + 1 // 轮流分配到三个缓冲区

        if (this.addToBuffer(routeCargo, bufferZoneId)) {
          allocatedCount += routeCargo.length
        }
      })

      return {
        success: true,
        allocatedCount,
        message: `已分配 ${allocatedCount} 项货物`
      }
    },

    // 按航线分组货物
    groupCargoByRoute(cargo) {
      const grouped = {}
      cargo.forEach(item => {
        const route = `${item.loadingTerminal}-${item.unloadingTerminal}`
        if (!grouped[route]) {
          grouped[route] = []
        }
        grouped[route].push(item)
      })
      return grouped
    },

    // 清空所有缓冲区
    clearAllBuffers() {
      this.bufferZones.forEach(zone => {
        if (zone.items.length > 0) {
          this.clearBuffer(zone.id)
        }
      })
      return true
    },

    // 在缓冲区之间移动货物
    moveItemBetweenBuffers(itemId, fromZoneId, toZoneId) {
      const fromZone = this.bufferZones.find(zone => zone.id === fromZoneId)
      const toZone = this.bufferZones.find(zone => zone.id === toZoneId)

      if (!fromZone || !toZone) return false

      const itemIndex = fromZone.items.findIndex(item => item.id === itemId)
      if (itemIndex === -1) return false

      const item = fromZone.items[itemIndex]

      // 记录操作历史
      this.operationHistory.push({
        type: 'move_between_buffers',
        timestamp: new Date().toISOString(),
        data: {
          item: { ...item },
          fromZoneId,
          toZoneId
        }
      })

      // 移除原位置
      fromZone.items.splice(itemIndex, 1)

      // 添加到新位置
      item.bufferZoneId = toZoneId
      toZone.items.push(item)

      // 更新统计
      fromZone.statistics = updateBufferStatistics(fromZone)
      toZone.statistics = updateBufferStatistics(toZone)

      return true
    },

    // 删除航次并还原货物到待运输列表
    deleteVoyageAndRestoreCargo(vesselId, voyageId) {
      const vessel = this.vesselManifest.find(v => v.id === vesselId)
      if (!vessel) return false

      const voyageIndex = vessel.voyages.findIndex(v => v.id === voyageId)
      if (voyageIndex === -1) return false

      const voyage = vessel.voyages[voyageIndex]

      // 记录操作历史
      this.operationHistory.push({
        type: 'delete_voyage',
        timestamp: new Date().toISOString(),
        data: {
          voyage: { ...voyage },
          vesselId
        }
      })

      // 还原货物到待运输列表
      if (voyage.cargoItems && voyage.cargoItems.length > 0) {
        voyage.cargoItems.forEach(cargoItem => {
          // 重置货物状态
          const restoredCargo = {
            ...cargoItem,
            status: 'pending',
            bufferZone: null,
            bufferZoneId: null
          }

          // 检查是否已存在相同的货物（避免重复）
          const existingIndex = this.pendingCargoList.findIndex(cargo => cargo.id === cargoItem.id)
          if (existingIndex === -1) {
            this.pendingCargoList.push(restoredCargo)
          } else {
            // 如果已存在，更新状态
            this.pendingCargoList[existingIndex] = restoredCargo
          }
        })
      }

      // 删除航次
      vessel.voyages.splice(voyageIndex, 1)

      return true
    }
  }
})