package com.ruoyi.customer.service.impl;

import com.alibaba.fastjson2.JSON;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.customer.domain.CusHistory;
import com.ruoyi.customer.domain.CusMain;
import com.ruoyi.customer.mapper.CusHistoryMapper;
import com.ruoyi.customer.mapper.CusMainMapper;
import com.ruoyi.customer.service.CusHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description CUS_HISTORY(客户变更历史表)Service实现
 * @createDate 2025-06-18 10:26:15
 */
@Slf4j
@Service
public class CusHistoryServiceImpl extends ServiceImpl<CusHistoryMapper, CusHistory>
        implements CusHistoryService {

    @Autowired
    private CusMainMapper cusMainMapper;
    @Autowired
    private CusHistoryMapper cusHistoryMapper;

    @Override
    public Page<CusHistory> selectCusHistoryList(String cusId, Integer pageNum, Integer pageSize) {
        Page<CusHistory> page = new Page<>(pageNum,pageSize);

        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper
                .select(
                        CusHistory::getCusMainId,
                        CusHistory::getCusHistoryId,
                        CusHistory::getCreateTime
                )
                .eq(CusHistory::getCusMainId, cusId)
                .from(CusHistory.class)
                .orderBy(CusHistory::getCreateTime,false);

        Page<CusHistory> result = mapper.paginate(page,queryWrapper);
        return result;
    }

    @Override
    public AjaxResult getCusHistoryById(String id) {
        CusHistory cusHistory = mapper.selectOneById(id);
        if(cusHistory == null){
            return AjaxResult.error("查询时间变更记录失败");
        }
        CusMain cusMain = JSON.parseObject(cusHistory.getContent(), CusMain.class);

        return AjaxResult.success(cusMain);
    }

    @Override
    public void saveCusHistory(CusMain cusMain) {
        if(cusMain==null){
            log.error("保存客户时间变更失败");
            return ;
        }
        CusHistory cusHistory = new CusHistory();
        String json = JSON.toJSONString(cusMain);
        cusHistory.setContent(json);
        cusHistory.setCusMainId(cusMain.getCusId());

        int insert = cusHistoryMapper.insert(cusHistory);
        if(insert>0){
            log.error("保存客户时间变更成功, 客户id：{}, 变更时间：{}",cusHistory.getCusMainId(),cusHistory.getCreateTime());
            return ;
        }
        log.error("保存客户时间变更失败");
    }
}




