<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.customer.mapper.CusHistoryMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.customer.domain.CusHistory">
            <id property="cusHistoryId" column="CUS_CONTACT_ID" />
            <result property="cusMainId" column="CUS_MAIN_ID" />
            <result property="createBy" column="CREATE_BY" />
            <result property="createTime" column="CREATE_TIME" />
            <result property="updateBy" column="UPDATE_BY" />
            <result property="updateTime" column="UPDATE_TIME" />
            <result property="remark" column="REMARK" />
            <result property="delFlag" column="DEL_FLAG" />
    </resultMap>

    <sql id="Base_Column_List">
        CUS_CONTACT_ID,CUS_MAIN_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,
        REMARK,DEL_FLAG,CONTENT
    </sql>
</mapper>
