package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicAncBerth;
import com.ruoyi.basic.domain.table.BasicAncBerthTableDef;
import com.ruoyi.basic.service.IBasicAncBerthService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicAncBerthTableDef.BASIC_ANC_BERTH;

@RequestMapping("/system/ancBerth")
@RestController
public class BasicAncBerthController extends BaseController {

    @Autowired
    IBasicAncBerthService basicAncBerthService;

    /**
     * 查询锚位管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:berth:list')")
    @PostMapping("/list")
    public Page<BasicAncBerth> list(@RequestBody BasicAncBerth basicAncBerth)
    {
        // startPage();
        // System.out.println(basicAncBerth);
        // List<BasicAncBerth> list = basicAncBerthService.selectBasicAncBerthList(basicAncBerth);
        // return getDataTable(list);

        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.select()
        .where(BASIC_ANC_BERTH.TERMINAL_ID.in(basicAncBerth.getTerminalIds()))
        .and(BASIC_ANC_BERTH.ANB_CODE.like(basicAncBerth.getAnbCode()).when(StringUtils.isNotEmpty(basicAncBerth.getAnbCode())))
        .and(BASIC_ANC_BERTH.ANB_CNAME.like(basicAncBerth.getAnbCname()).when(StringUtils.isNotEmpty(basicAncBerth.getAnbCname())))
        .orderBy(BASIC_ANC_BERTH.CREATE_TIME.asc());
        return basicAncBerthService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出锚位管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:berth:export')")
    @Log(title = "锚位管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicAncBerth basicAncBerth)
    {
        List<BasicAncBerth> list = basicAncBerthService.selectBasicAncBerthList(basicAncBerth);
        ExcelUtil<BasicAncBerth> util = new ExcelUtil<BasicAncBerth>(BasicAncBerth.class);
        util.exportExcel(response, list, "锚位管理数据");
    }

    /**
     * 获取锚位管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:berth:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicAncBerthService.selectBasicAncBerthById(id));
    }

    /**
     * 新增锚位管理
     */
    @PreAuthorize("@ss.hasPermi('system:berth:add')")
    @Log(title = "锚位管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicAncBerth basicAncBerth)
    {
        return toAjax(basicAncBerthService.insertBasicAncBerth(basicAncBerth));
    }

    /**
     * 修改锚位管理
     */
    @PreAuthorize("@ss.hasPermi('system:berth:edit')")
    @Log(title = "锚位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicAncBerth basicAncBerth)
    {
        return toAjax(basicAncBerthService.updateBasicAncBerth(basicAncBerth));
    }

    /**
     * 删除锚位管理
     */
    @PreAuthorize("@ss.hasPermi('system:berth:remove')")
    @Log(title = "锚位管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicAncBerthService.deleteBasicAncBerthByIds(ids));
    }

}
