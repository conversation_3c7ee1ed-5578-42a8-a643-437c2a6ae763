package com.ruoyi.route.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.route.domain.RouteMain;
import com.ruoyi.route.service.IRouteMainService;
import com.ruoyi.route.service.IRouteBranchService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 航线主线Controller
 */
@Slf4j
@RestController
@RequestMapping("/route/main")
public class RouteMainController extends BaseController {
    @Autowired
    private IRouteMainService routeMainService;

    @Autowired
    private IRouteBranchService routeBranchService;

    /**
     * 分页查询航线主线
     */
    @PreAuthorize("@ss.hasPermi('route:main:list')")
    @GetMapping("/page")
    public AjaxResult page(@RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            RouteMain routeMain) {
        log.debug("分页查询航线主线 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, routeMain);
        Page<RouteMain> page = routeMainService.selectRouteMainPage(pageNumber, pageSize, routeMain);
        log.debug("分页查询航线主线完成 - 总记录数: {}", page.getTotalRow());
        return AjaxResult.success(page);
    }

    /**
     * 航线主表增加下拉框方法
     */
    @GetMapping("/label")
    public AjaxResult label(String query) {
        return AjaxResult.success(routeMainService.list(QueryWrapper.create()
                .like(RouteMain::getMainName,query)
        ));
    }

    /**
     * 导出航线主线列表
     */
    @PreAuthorize("@ss.hasPermi('route:main:export')")
    @Log(title = "航线主线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RouteMain routeMain) {
        log.debug("导出航线主线数据 - 查询条件: {}", routeMain);
        Page<RouteMain> page = routeMainService.selectRouteMainPage(1, Integer.MAX_VALUE, routeMain);
        ExcelUtil<RouteMain> util = new ExcelUtil<RouteMain>(RouteMain.class);
        util.exportExcel(response, page.getRecords(), "航线主线数据");
        log.debug("导出航线主线数据完成 - 导出记录数: {}", page.getRecords().size());
    }

    /**
     * 获取航线主线详细信息
     */
    @PreAuthorize("@ss.hasPermi('route:main:query')")
    @GetMapping(value = "/{mainId}")
    public AjaxResult getInfo(@PathVariable("mainId") String mainId) {
        log.debug("获取航线主线详细信息 - ID: {}", mainId);
        RouteMain routeMain = routeMainService.getById(mainId);
        log.debug("获取航线主线详细信息完成 - 结果: {}", routeMain);
        return success(routeMain);
    }

    /**
     * 新增航线主线
     */
    @PreAuthorize("@ss.hasPermi('route:main:add')")
    @Log(title = "航线主线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RouteMain routeMain) {
        log.debug("新增航线主线 - 数据: {}", routeMain);
        if (routeMainService.existsMainName(routeMain.getMainName(), null)) {
            return AjaxResult.error("主线名称已存在，请勿重复！");
        }
        boolean success = routeMainService.save(routeMain);
        log.debug("新增航线主线完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 修改航线主线
     */
    @PreAuthorize("@ss.hasPermi('route:main:edit')")
    @Log(title = "航线主线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RouteMain routeMain) {
        log.debug("修改航线主线 - 数据: {}", routeMain);
        if (routeMainService.existsMainName(routeMain.getMainName(), routeMain.getMainId())) {
            return AjaxResult.error("主线名称已存在，请勿重复！");
        }
        boolean success = routeMainService.updateById(routeMain);
        log.debug("修改航线主线完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 删除航线主线
     */
    @PreAuthorize("@ss.hasPermi('route:main:remove')")
    @Log(title = "航线主线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{mainIds}")
    public AjaxResult remove(@PathVariable String[] mainIds) {
        log.debug("删除航线主线 - ID数组: {}", Arrays.toString(mainIds));
        // 检查是否存在关联的支线
        for (String mainId : mainIds) {
            // 查询关联的支线数量
            int branchCount = routeBranchService.countByMainId(mainId);
            if (branchCount > 0) {
                return AjaxResult.error("删除失败，该主线下存在" + branchCount + "条支线数据，请先删除支线数据");
            }
        }
        boolean success = routeMainService.removeByIds(Arrays.asList(mainIds));
        log.debug("删除航线主线完成 - 结果: {}", success);
        return toAjax(success);
    }
} 