package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicArea;
import com.ruoyi.basic.domain.BasicCountry;
import com.ruoyi.basic.mapper.BasicCountryMapper;
import com.ruoyi.basic.service.IBasicAreaService;
import com.ruoyi.basic.service.IBasicCountryService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCountryTableDef.BASIC_COUNTRY;
import static com.ruoyi.basic.domain.table.BasicAreaTableDef.BASIC_AREA;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicCountryServiceImpl extends ServiceImpl<BasicCountryMapper, BasicCountry> implements IBasicCountryService {

    @Autowired
    IBasicAreaService areaService;

    @Autowired
    private BasicCountryMapper basicCountryMapper;

    @Override
    public BasicCountryMapper getMapper() {
        return basicCountryMapper;
    }

    /**
     * 查询国家
     *
     * @param id 国家主键
     * @return 国家
     */
    @Override
    public BasicCountry selectBasicCountryById(String id)
    {
        return basicCountryMapper.selectOneById(id);
    }

    /**
     * 查询国家列表
     *
     * @param basicCountry 国家
     * @return 国家
     */
    @Override
    public List<BasicCountry> selectBasicCountryList(BasicCountry basicCountry)
    {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_COUNTRY)
            .where(BASIC_COUNTRY.BC_COUNTRY_CODE.like(basicCountry.getBcCountryCode()))
            .and(BASIC_COUNTRY.BC_CHINESE_NAME.like(basicCountry.getBcChineseName()))
            .and(BASIC_COUNTRY.BC_ENGLISH_NAME.like(basicCountry.getBcEnglishName()));
        return this.list(queryWrapper);
    }

    /**
     * 新增国家
     *
     * @param basicCountry 国家
     * @return 结果
     */
    @Override
    public int insertBasicCountry(BasicCountry basicCountry)
    {
        return basicCountryMapper.insert(basicCountry);
    }

    /**
     * 修改国家
     *
     * @param basicCountry 国家
     * @return 结果
     */
    @Override
    public int updateBasicCountry(BasicCountry basicCountry)
    {
        return basicCountryMapper.update(basicCountry);
    }

    /**
     * 批量删除国家
     *
     * @param ids 需要删除的国家主键集合
     * @return 结果
     */
    @Override
    public int deleteBasicCountryByIds(List<String> ids)
    {
        return basicCountryMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除国家信息
     *
     * @param id 国家主键
     * @return 结果
     */
    @Override
    public int deleteBasicCountryById(String id)
    {
        return basicCountryMapper.deleteById(id);
    }

    public void checkUseById(String id) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_AREA)
            .where(BASIC_AREA.COU_ID.eq(id));
        List<BasicArea> checkArea = areaService.list(queryWrapper);
        if(!checkArea.isEmpty()){
            throw new Exception("选中的国家正在被使用");
        }
    }

    public void checkUseByIds(List<String> ids) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_AREA)
            .where(BASIC_AREA.COU_ID.in(ids));
        List<BasicArea> checkArea = areaService.list(queryWrapper);
        if(!checkArea.isEmpty()){
            throw new Exception("选中的国家正在被使用");
        }
    }

    public void checkValid(BasicCountry basicCountry) throws Exception {

        if(StringUtils.isEmpty(basicCountry.getBcCountryCode())){
            throw new Exception("国家/地区代码不能为空");
        }

        if(StringUtils.isEmpty(basicCountry.getBcChineseName())){
            throw new Exception("中文名称不能为空");
        }

        if(StringUtils.isEmpty(basicCountry.getBcEnglishName())){
            throw new Exception("英文名称不能为空");
        }

    }

    public void checkUniqueCountry(BasicCountry basicCountry) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_COUNTRY)
            .where(BASIC_COUNTRY.BC_COUNTRY_CODE.eq(basicCountry.getBcCountryCode()));
        if (basicCountry.getId() != null) {
            queryWrapper.and(BASIC_COUNTRY.ID.ne(basicCountry.getId()));
        }
        List<BasicCountry> checkCode = this.list(queryWrapper);
        if(!checkCode.isEmpty()){
            throw new Exception("国家/地区代码重复");
        }

        queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_COUNTRY)
            .where(BASIC_COUNTRY.BC_CHINESE_NAME.eq(basicCountry.getBcChineseName()));
        if (basicCountry.getId() != null) {
            queryWrapper.and(BASIC_COUNTRY.ID.ne(basicCountry.getId()));
        }
        List<BasicCountry> checkCnName = this.list(queryWrapper);
        if(!checkCnName.isEmpty()){
            throw new Exception("中文名称重复");
        }

        queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_COUNTRY)
            .where(BASIC_COUNTRY.BC_ENGLISH_NAME.eq(basicCountry.getBcEnglishName()));
        if (basicCountry.getId() != null) {
            queryWrapper.and(BASIC_COUNTRY.ID.ne(basicCountry.getId()));
        }
        List<BasicCountry> checkEnName = this.list(queryWrapper);
        if(!checkEnName.isEmpty()){
            throw new Exception("英文名称重复");
        }
    }

}
