<template>
  <div class="entrust-side-panel" :class="{ 'panel-visible': props.visible }" :style="{ width: panelWidth }">
    <!-- 拖拽手柄 -->
    <div v-if="props.visible" 
         class="resize-handle" 
         @mousedown="handleMouseDown"
         @dblclick="handleDoubleClick"
         :class="{ 'is-dragging': isDragging }">
      <div class="resize-handle-line"></div>
    </div>
    
    <!-- 关闭按钮 -->
    <div v-if="props.visible" class="close-panel-button" @click="handleClose" title="关闭面板">
      <el-icon>
        <Close />
      </el-icon>
    </div>
    
    
    <!-- 侧边面板主体 -->
    <div class="panel-container" v-loading="loading" 
         element-loading-background="rgba(255, 255, 255, 0.1)"
         element-loading-text="">
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-info">
          <div class="entrust-title">
            <el-icon class="title-icon"><Document /></el-icon>
            <span class="title-text">
              {{ props.mode === 'add' ? '新增委托' : props.mode === 'edit' ? '编辑委托' : '委托详情' }}
            </span>
            <el-tag v-if="props.mode !== 'add' && entrustData?.orderMain?.status" :type="getStatusType(entrustData.orderMain.status)" size="small">
              {{ getStatusText(entrustData.orderMain.status) }}
            </el-tag>
            <el-tag v-if="props.mode === 'add'" type="warning" size="small">
              新增模式
            </el-tag>
          </div>
          <div class="entrust-info" v-if="props.mode !== 'add' && entrustData?.bookingMain">
            <span class="info-item">
              <el-icon><User /></el-icon>
              {{ entrustData.bookingMain.shipperName || '未指定客户' }}
            </span>
            <span class="info-item">
              <el-icon><Calendar /></el-icon>
              {{ entrustData.bookingMain.bookingDate || '未设定日期' }}
            </span>
          </div>
          <div class="entrust-info" v-if="props.mode === 'add'">
            <span class="info-item">
              <el-icon><User /></el-icon>
              {{ entrustData.bookingMain.shipperName || '请选择托运单位' }}
            </span>
            <span class="info-item">
              <el-icon><Calendar /></el-icon>
              {{ entrustData.bookingMain.bookingDate || '今日新增' }}
            </span>
          </div>
        </div>
        <div class="header-actions">
          <el-button link @click="handleClose" class="close-btn">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </div>

      <!-- 面板主要内容 -->
      <div class="panel-main">
        <!-- 左侧Tab导航 -->
        <div class="tab-navigation">
          <div class="nav-menu">
            <div 
              v-for="tab in tabList" 
              :key="tab.name"
              :class="['nav-item', { active: activeTabName === tab.name }]"
              @click="switchTab(tab.name)"
            >
              <el-icon class="nav-icon">
                <component :is="tab.icon" />
              </el-icon>
              <span class="nav-label">{{ tab.label }}</span>
              <el-badge 
                v-if="tab.badge && tab.badge > 0" 
                :value="tab.badge" 
                class="nav-badge"
              />
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="tab-content-wrapper">
          <div class="content-header">
            <div class="content-title">
              <el-icon><component :is="currentTab.icon" /></el-icon>
              <span>{{ currentTab.label }}</span>
            </div>
            <div class="content-actions" v-if="props.mode === 'add' || props.mode === 'edit'">
              <el-button size="small" @click="handleReset" :disabled="loading">
                <el-icon><Refresh /></el-icon>
                重置当前
              </el-button>
            </div>
          </div>

          <div class="content-body">
            <!-- 订舱信息 -->
            <div v-show="activeTabName === 'booking'" class="tab-panel">
              <el-form :model="entrustData" :rules="formRules" ref="entrustFormBooking" label-width="100px" class="panel-form">
                <BookingPanel 
                  :form-data="entrustData"
                  :mode="props.mode"
                  @container-change="calculateTotalWeight"
                />
              </el-form>
            </div>

            <!-- 码头装卸补料 -->
            <div v-show="activeTabName === 'terminal'" class="tab-panel">
              <el-form :model="entrustData" ref="entrustFormTerminal" label-width="100px" class="panel-form">
                <TerminalPanel 
                  :logistics-main-list="entrustData.logisticsMainList" 
                  :mode="props.mode" 
                />
              </el-form>
            </div>

            <!-- 预配 -->
            <div v-show="activeTabName === 'pre-allocation'" class="tab-panel">
              <el-form :model="entrustData" ref="entrustFormPreAllocation" label-width="100px" class="panel-form">
                <PreAllocationPanel 
                  :entrust-data="entrustData" 
                  :mode="props.mode" 
                />
              </el-form>
            </div>

            <!-- 提单发货 -->
            <div v-show="activeTabName === 'shipping'" class="tab-panel">
              <el-form :model="entrustData" ref="entrustFormShipping" label-width="100px" class="panel-form">
                <ShippingPanel 
                  :entrust-data="entrustData" 
                  :mode="props.mode" 
                />
              </el-form>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="panel-footer">
        <div class="footer-left">
          <el-button @click="handleClose">
            <el-icon><Close /></el-icon>
            关闭面板
          </el-button>
          <el-button @click="resetWidth" class="reset-width-btn">
            默认宽度
          </el-button>
        </div>
        <div class="footer-right" v-if="props.mode === 'add' || props.mode === 'edit'">
          <el-button @click="handleReset" :disabled="loading">
            <el-icon><Refresh /></el-icon>
            重置表单
          </el-button>
          <el-button type="success" @click="handleSaveAsDraft" :loading="submitting">
            <el-icon><Document /></el-icon>
            保存草稿
          </el-button>
          <el-button type="primary" @click="handleSave" :loading="submitting">
            <el-icon><Check /></el-icon>
            {{ props.mode === 'add' ? '提交委托' : '更新委托' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 开发者调试面板 -->
    <div v-if="isDevMode && props.visible" class="dev-panel">
      <el-collapse v-model="devActiveNames">
        <el-collapse-item name="panelData">
          <template #title>
            <div class="dev-panel-title">
              <el-icon><Setting /></el-icon>
              <span>侧边栏表单数据 (EntrustDTO)</span>
              <el-tag type="success" size="small">{{ props.mode }}模式</el-tag>
            </div>
          </template>
          <JsonViewer 
            :data="entrustData" 
            title="🗂️ 侧边栏表单数据 (EntrustDTO)"
            :expand-level="1"
          />
          <div class="dev-data-description">
            <el-alert
              title="数据说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="data-description-content">
                  <p><strong>当前模式：</strong>{{ props.mode === 'add' ? '新增委托' : props.mode === 'view' ? '查看委托' : '编辑委托' }}</p>
                  <p><strong>数据结构：</strong>三层架构 DTO 数据，用于侧边栏表单编辑</p>
                  <p><strong>数据来源：</strong>{{ props.mode === 'add' ? '初始化默认数据' : `通过 getEntrust(${props.entrustId}) 获取` }}</p>
                  <p><strong>主要字段：</strong>orderMain（委托）→ bookingMain（订舱）→ logisticsMain（物流）</p>
                </div>
              </template>
            </el-alert>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getEntrust, addEntrust, updateEntrust } from '@/api/booking/entrust.js'
import BookingPanel from './BookingPanel.vue'
import TerminalPanel from './TerminalPanel.vue'
import PreAllocationPanel from './PreAllocationPanel.vue'
import ShippingPanel from './ShippingPanel.vue'
import { useDevMode } from '@/devtools/useDevMode'
import JsonViewer from '@/devtools/JsonViewer.vue'
import { useResizablePanel } from '@/composables/useResizablePanel'
import { 
  InfoFilled, 
  Document, 
  Check, 
  Refresh, 
  Box, 
  Van,
  Ship,
  Close,
  User,
  Calendar,
  Setting
} from '@element-plus/icons-vue'

// 定义组件props
const props = defineProps({
  /** 面板是否可见 */
  visible: {
    type: Boolean,
    default: false
  },
  /** 操作模式：add-新增, view-查看, edit-编辑 */
  mode: {
    type: String,
    default: 'view',
    validator: (value) => ['add', 'view', 'edit'].includes(value)
  },
  /** 委托ID */
  entrustId: {
    type: [Number, String],
    default: null
  }
})

// 定义组件事件
const emit = defineEmits([
  'update:visible', 
  'close', 
  'save-success', 
  'entrust-change'
])

// 使用开发模式工具
const { isDevMode } = useDevMode()

// 使用可拖拽面板功能
const {
  panelWidth,
  isDragging,
  handleMouseDown,
  handleDoubleClick,
  resetWidth
} = useResizablePanel({
  defaultWidth: '65%',
  minWidth: 400,
  maxWidthPercent: 80,
  storageKey: 'entrust-side-panel-width'
})

/**
 * 获取初始化数据结构
 * @returns {Object} 委托初始数据
 */
const getInitialData = () => ({
  // ORDER_MAIN - 委托主表（第一层）
  orderMain: {
    id: null,
    orderNo: '',
    shipperId: null,
    shipperName: '',
    orderDate: new Date().toISOString().split('T')[0],
    businessType: 'WATERWAY',
    tradeType: 'DOMESTIC',
    consortium: '',
    customerAgreement: '',
    settlementMethod: 'MONTHLY',
    totalContainers: 0,
    totalWeight: 0,
    status: 'DRAFT',
    remark: '',
    createBy: '',
    createTime: null,
    updateBy: '',
    updateTime: null,
    delFlag: '0',
    version: 1
  },
  // BOOKING_MAIN - 订舱主表（第二层）
  bookingMain: {
    id: null,
    orderId: null,
    bookingNumber: '',
    status: 'DRAFT',
    shipperId: null,
    shipperName: '',
    originLocationId: null,
    originLocationName: '',
    destinationLocationId: null,
    destinationLocationName: '',
    bookingDate: new Date().toISOString().split('T')[0],
    departureDate: null,
    deliveryDate: null,
    loadingTerminalId: null,
    loadingTerminalName: '',
    unloadingTerminalId: null,
    unloadingTerminalName: '',
    loadingAgentId: null,
    loadingAgentName: '',
    unloadingAgentId: null,
    unloadingAgentName: '',
    tradeType: 'DOMESTIC',
    transportMode: '',
    customsType: 'GENERAL',
    settlementMethod: 'MONTHLY',
    consortium: '',
    customerAgreement: '',
    consignmentSource: 'STAFF_ENTRY',
    remark: '',
    createBy: '',
    createTime: null,
    updateBy: '',
    updateTime: null,
    delFlag: '0',
    version: 1
  },
  // BOOKING_CNTR_NUM - 柜量信息表（关联订舱）
  bookingCntrNumList: [{
    id: null,
    bookingId: null,
    containerSize: '20',
    containerType: 'GP',
    isEmpty: '0',
    quantity: 1,
    isDangerous: '0',
    dangerousLevel: '',
    isRefrigerated: '0',
    cargoType: '',
    singleWeight: 0,
    totalWeight: 0,
    isOversize: '0',
    oversizeDimensions: '',
    remark: '',
    createBy: '',
    createTime: null,
    updateBy: '',
    updateTime: null,
    delFlag: '0',
    version: 1
  }],
  // BOOKING_TRANSIT_PORT - 中转港表（关联订舱）
  bookingTransitPortList: [],
  // LOGISTICS_MAIN - 物流信息列表（第三层）
  logisticsMainList: [{
    id: null,
    bookingId: null,
    businessType: 'WATERWAY',
    supplierId: null,
    supplierName: '',
    originLocationId: null,
    originLocationName: '',
    destinationLocationId: null,
    destinationLocationName: '',
    containerDetails: '',
    termsConditions: '',
    requiredDepartureDate: null,
    requiredArrivalDate: null,
    createBy: '',
    createTime: null,
    updateBy: '',
    updateTime: null,
    version: 1
  }]
})

// 响应式数据
/** 当前激活的Tab页签 */
const activeTabName = ref('booking')

/** 委托数据 */
const entrustData = reactive(getInitialData())

/** 加载状态 */
const loading = ref(false)

/** 提交状态 */
const submitting = ref(false)

/** 开发者面板展开状态 */
const devActiveNames = ref([])

/** 表单引用 */
const entrustFormBooking = ref(null)
const entrustFormTerminal = ref(null)
const entrustFormPreAllocation = ref(null)
const entrustFormShipping = ref(null)

/** 表单验证规则 */
const formRules = {
  'bookingMain.shipperId': [
    { required: true, message: '请选择托运单位', trigger: 'change' }
  ],
  'bookingMain.originLocationId': [
    { required: true, message: '请选择起运地', trigger: 'change' }
  ],
  'bookingMain.destinationLocationId': [
    { required: true, message: '请选择目的地', trigger: 'change' }
  ],
  'bookingMain.bookingDate': [
    { required: true, message: '请选择订舱日期', trigger: 'change' }
  ]
}

/** Tab页签配置 */
const tabList = [
  {
    name: 'booking',
    label: '订舱信息',
    icon: InfoFilled,
    badge: 0
  },
  {
    name: 'terminal',
    label: '码头装卸',
    icon: Van,
    badge: 0
  },
  {
    name: 'pre-allocation',
    label: '预配',
    icon: Box,
    badge: 0
  },
  {
    name: 'shipping',
    label: '提单发货',
    icon: Ship,
    badge: 0
  }
]

// 计算属性
/** 当前激活的Tab信息 */
const currentTab = computed(() => {
  return tabList.find(tab => tab.name === activeTabName.value) || tabList[0]
})

// 侦听器
/** 监听面板可见性变化 */
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      loadData()
      // 重置到第一个Tab
      activeTabName.value = 'booking'
    }
  }
)

/** 监听委托ID变化 */
watch(
  () => props.entrustId,
  (newVal) => {
    if (newVal && props.visible) {
      loadData()
    }
  }
)

/** 监听模式变化 */
watch(
  () => props.mode,
  (newVal) => {
    // 当模式切换到新增时，只清空系统生成的字段，保留用户输入的字段
    if (newVal === 'add' && props.visible) {
      clearSystemGeneratedFields()
      console.log('Mode switched to add: Cleared system-generated fields only')
    }
  }
)

// 方法定义
/**
 * 清空系统生成的字段（用于新增模式）
 */
const clearSystemGeneratedFields = () => {
  entrustData.orderMain.id = null
  entrustData.orderMain.orderNo = ''
  entrustData.bookingMain.id = null
  entrustData.bookingMain.bookingNumber = ''
  entrustData.orderMain.status = 'DRAFT'
  entrustData.bookingMain.status = 'DRAFT'
  
  // 清空所有子表的ID
  if (entrustData.bookingCntrNumList) {
    entrustData.bookingCntrNumList.forEach(item => {
      item.id = null
      item.bookingId = null
    })
  }
  if (entrustData.bookingTransitPortList) {
    entrustData.bookingTransitPortList.forEach(item => {
      item.id = null
      item.bookingId = null
    })
  }
  if (entrustData.logisticsMainList) {
    entrustData.logisticsMainList.forEach(item => {
      item.id = null
      item.bookingId = null
    })
  }
}

/**
 * 加载委托数据
 */
const loadData = async () => {
  // 如果是新增模式且没有entrustId，使用初始数据
  if (props.mode === 'add' && !props.entrustId) {
    Object.assign(entrustData, getInitialData())
    console.log('Side Panel Add mode: Using initial data')
    return
  }
  
  // 如果没有entrustId，使用初始数据
  if (!props.entrustId) {
    Object.assign(entrustData, getInitialData())
    return
  }
  
  loading.value = true
  console.log(`Side Panel Loading data for entrust ID: ${props.entrustId}, mode: ${props.mode}`)
  
  try {
    // 调用真实API获取委托数据
    const response = await getEntrust(props.entrustId)
    const apiData = response.data || response
    
    if (apiData) {
      // API返回了完整的三层架构数据，直接使用
      Object.assign(entrustData, apiData)
      console.log('Side Panel Loaded entrust data from API:', entrustData)
      emit('entrust-change', entrustData)
    } else {
      ElMessage.warning(`委托ID: ${props.entrustId} 未找到数据`)
      Object.assign(entrustData, getInitialData())
    }
  } catch (error) {
    console.error('Side Panel 数据加载失败:', error)
    ElMessage.error('数据加载失败')
    Object.assign(entrustData, getInitialData())
  } finally {
    loading.value = false
  }
}

/**
 * 切换Tab页签
 * @param {string} tabName - Tab名称
 */
const switchTab = (tabName) => {
  activeTabName.value = tabName
}

/**
 * 计算总重量和总箱数
 */
const calculateTotalWeight = () => {
  const totalContainers = entrustData.bookingCntrNumList.reduce((sum, item) => sum + (item.quantity || 0), 0)
  const totalWeight = entrustData.bookingCntrNumList.reduce((sum, item) => sum + (item.totalWeight || 0), 0)
  
  entrustData.orderMain.totalContainers = totalContainers
  entrustData.orderMain.totalWeight = totalWeight
  
  // 同步更新物流主表列表的箱量明细
  if (entrustData.logisticsMainList && entrustData.logisticsMainList.length > 0) {
    entrustData.logisticsMainList[0].containerDetails = JSON.stringify(
      entrustData.bookingCntrNumList.map(item => ({
        containerSize: item.containerSize,
        containerType: item.containerType,
        quantity: item.quantity,
        totalWeight: item.totalWeight
      }))
    )
  }
}

/**
 * 重置表单
 */
const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置表单吗？所有已填写的信息将丢失。',
      '确认重置',
      { type: 'warning' }
    )
    
    if (props.mode === 'add') {
      // 新增模式：重置为初始数据
      Object.assign(entrustData, getInitialData())
      console.log('Add mode: Reset to initial data')
    } else {
      // 编辑/查看模式：重新加载原始数据
      await loadData()
    }
    ElMessage.success('表单已重置')
  } catch {
    // 用户取消重置
  }
}

/**
 * 保存为草稿
 */
const handleSaveAsDraft = async () => {
  submitting.value = true
  
  try {
    // 设置草稿状态
    entrustData.orderMain.status = 'DRAFT'
    entrustData.bookingMain.status = 'DRAFT'
    
    console.log('Side Panel 草稿数据:', entrustData)
    
    // 调用真实API保存草稿
    if (props.mode === 'add') {
      const response = await addEntrust(entrustData)
      console.log('新增草稿响应:', response)
      ElMessage.success('草稿保存成功!')
    } else if (props.mode === 'edit') {
      const response = await updateEntrust(entrustData)
      console.log('更新草稿响应:', response)
      ElMessage.success('草稿更新成功!')
    }
    
    emit('save-success')
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败: ' + (error.message || '网络错误'))
  } finally {
    submitting.value = false
  }
}

/**
 * 保存提交
 */
const handleSave = () => {
  // 获取当前活跃的form引用
  const tabName = activeTabName.value.charAt(0).toUpperCase() + 
                  activeTabName.value.slice(1).replace('-', '')
  const currentFormName = `entrustForm${tabName}`
  
  const formRefMap = {
    entrustFormBooking: entrustFormBooking.value,
    entrustFormTerminal: entrustFormTerminal.value,
    entrustFormPreAllocation: entrustFormPreAllocation.value,
    entrustFormShipping: entrustFormShipping.value
  }
  
  const currentForm = formRefMap[currentFormName]
  
  if (currentForm && currentForm.validate) {
    currentForm.validate(async (valid) => {
      if (valid) {
        await performSave()
      } else {
        ElMessage.error('表单校验失败，请检查输入项!')
        return false
      }
    })
  } else {
    // 如果没有找到对应的form，尝试直接保存
    performSave()
  }
}

/**
 * 执行保存操作
 */
const performSave = async () => {
  submitting.value = true
  
  try {
    // 设置提交状态
    entrustData.orderMain.status = 'SUBMITTED'
    entrustData.bookingMain.status = 'SUBMITTED'
    
    console.log('Side Panel 提交数据:', entrustData)
    
    // 调用真实API保存数据
    if (props.mode === 'add') {
      const response = await addEntrust(entrustData)
      console.log('新增委托响应:', response)
      ElMessage.success('委托提交成功!')
    } else if (props.mode === 'edit') {
      const response = await updateEntrust(entrustData)
      console.log('更新委托响应:', response)
      ElMessage.success('委托更新成功!')
    }
    
    emit('save-success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败: ' + (error.message || '网络错误'))
  } finally {
    submitting.value = false
  }
}

/**
 * 关闭面板
 */
const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

/**
 * 获取状态对应的标签类型
 * @param {string} status - 状态值
 * @returns {string} Element Plus 标签类型
 */
const getStatusType = (status) => {
  const types = {
    'DRAFT': 'info',
    'SUBMITTED': 'warning', 
    'CONFIRMED': 'success',
    'REJECTED': 'danger',
    'PARTIAL_CONFIRMED': 'primary'
  }
  return types[status] || 'info'
}

/**
 * 获取状态显示文本
 * @param {string} status - 状态值
 * @returns {string} 状态显示文本
 */
const getStatusText = (status) => {
  const texts = {
    'DRAFT': '草稿',
    'SUBMITTED': '待审核',
    'CONFIRMED': '已确认',
    'REJECTED': '已拒绝',
    'PARTIAL_CONFIRMED': '部分确认'
  }
  return texts[status] || status
}

// 向父组件暴露的方法和属性（如果需要的话）
defineExpose({
  loadData,
  handleReset,
  calculateTotalWeight
})
</script>

<style scoped>
.entrust-side-panel {
  position: fixed;
  top: 0;
  right: -65%;
  height: 100vh;
  z-index: 1500;
  transition: right 0.25s ease;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.1);
}

.entrust-side-panel.panel-visible {
  right: 0;
}

/* 拖拽手柄样式 */
.resize-handle {
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  cursor: col-resize;
  z-index: 1600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.resize-handle.is-dragging {
  background-color: rgba(64, 158, 255, 0.2);
}

.resize-handle-line {
  width: 2px;
  height: 40px;
  background-color: #dcdfe6;
  border-radius: 1px;
  transition: all 0.2s ease;
}

.resize-handle:hover .resize-handle-line {
  background-color: #409eff;
  height: 60px;
}

.resize-handle.is-dragging .resize-handle-line {
  background-color: #409eff;
  height: 60px;
}

/* 关闭面板按钮 */
.close-panel-button {
  position: absolute;
  top: 50%;
  left: -24px;
  transform: translateY(-50%);
  width: 24px;
  height: 80px; /* 调整高度 */
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(3px);
  border: 1px solid #e4e7ed;
  border-right: none; /* 吸附在侧边栏，移除右边框 */
  border-radius: 12px 0 0 12px; /* 左侧圆角，形成半月牙效果 */
  z-index: 1600;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266; /* 图标颜色 */
  cursor: pointer;
  transition: all 0.25s ease;
  box-shadow: -1px 1px 6px rgba(0, 0, 0, 0.08);
}

.close-panel-button:hover {
  background: #f56c6c;
  color: white;
  box-shadow: -2px 2px 10px rgba(245, 108, 108, 0.3);
  transform: translateY(-50%) translateX(2px);
}

.close-panel-button .el-icon {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.close-panel-button:hover .el-icon {
  transform: scale(1.1);
}

/* 重置宽度按钮 */
.reset-width-btn {
  font-size: 12px;
  padding: 8px 16px;
  margin-left: 8px;
  background: #f0f9ff;
  border: 1px solid #d1ecf1;
  color: #0c5460;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.reset-width-btn:hover {
  background: #bee5eb;
  border-color: #bee5eb;
  color: #0c5460;
}

/* 折叠状态下宽度通过内联样式控制 */

.panel-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* 面板头部 */
.panel-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-info {
  flex: 1;
}

.entrust-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.title-icon {
  font-size: 18px;
  color: #409eff;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.entrust-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #606266;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.close-btn {
  padding: 8px 12px;
  font-size: 12px;
}

/* 面板主要内容 */
.panel-main {
  flex: 1;
  display: flex;
  min-height: 0;
}

/* 左侧导航区域 */
.tab-navigation {
  width: 120px;
  background: #ffffff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.nav-menu {
  flex: 1;
  padding: 12px 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  position: relative;
  margin: 0 8px 6px 8px;
  border-radius: 8px;
  background: transparent;
}

.nav-item:hover {
  background: #f0f2f5;
}

.nav-item.active {
  background: #409eff;
  color: white;
}


.nav-icon {
  font-size: 14px;
  margin-right: 6px;
  flex-shrink: 0;
}

.nav-label {
  flex: 1;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-badge {
  margin-left: 6px;
}

/* 右侧内容区域 */
.tab-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; 
}

.content-header {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.content-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.content-actions {
  display: flex;
  gap: 8px;
}

.content-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.tab-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: #f5f7fa;
  min-height: 0;
}

.panel-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  min-height: 0;
}

/* 优化表单内部元素布局 */
.panel-form :deep(.el-form-item) {
  margin-bottom: 12px;
}

.panel-form :deep(.el-form-item__label) {
  line-height: 28px;
  font-size: 12px;
  min-width: 90px;
}

.panel-form :deep(.el-form-item__content) {
  line-height: 28px;
}

.panel-form :deep(.el-input),
.panel-form :deep(.el-select),
.panel-form :deep(.el-date-picker) {
  width: 100%;
}

.panel-form :deep(.el-input__inner) {
  padding: 0 8px;
  font-size: 12px;
  height: 28px;
}

.panel-form :deep(.el-select .el-input__inner) {
  padding-right: 30px;
}

/* 底部操作栏 */
.panel-footer {
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.footer-left,
.footer-right {
  display: flex;
  gap: 8px;
}

.footer-right .el-button {
  font-size: 12px;
  padding: 8px 16px;
}

/* 开发者面板 - 优化后的样式 */
.dev-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 600px;
  max-height: 600px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 2000;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.dev-panel :deep(.el-collapse) {
  border: none;
}

.dev-panel :deep(.el-collapse-item) {
  border: none;
}

.dev-panel :deep(.el-collapse-item__header) {
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
  border: none;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.dev-panel :deep(.el-collapse-item__header:hover) {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.dev-panel :deep(.el-collapse-item__header.is-active) {
  border-radius: 12px 12px 0 0;
}

.dev-panel :deep(.el-collapse-item__arrow) {
  color: white;
  font-size: 16px;
  margin-right: 8px;
}

.dev-panel :deep(.el-collapse-item__content) {
  max-height: 520px;
  overflow: hidden;
  padding: 0;
  border: none;
}

.dev-panel :deep(.json-viewer) {
  margin: 0;
  border: none;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

.dev-panel :deep(.json-viewer-header) {
  background: #f8f9fa;
  border-radius: 0;
  padding: 12px 16px;
  border: none;
  border-bottom: 1px solid #e4e7ed;
}

.dev-panel :deep(.json-viewer-content) {
  max-height: 420px;
  overflow-y: auto;
  padding: 16px;
  background: #fafbfc;
}

.dev-panel :deep(.json-viewer-content pre) {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  font-family: 'SF Mono', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #2d3748;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 滚动条样式优化 */
.dev-panel :deep(.json-viewer-content)::-webkit-scrollbar {
  width: 8px;
}

.dev-panel :deep(.json-viewer-content)::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.dev-panel :deep(.json-viewer-content)::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.dev-panel :deep(.json-viewer-content)::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dev-panel {
    width: calc(100vw - 40px);
    max-width: 600px;
    right: 20px;
    left: 20px;
  }
}

/* 数据高亮样式 */
.dev-panel :deep(.json-viewer-content pre) {
  /* JSON 语法高亮效果 */
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

/* 添加拖拽提示 */
.dev-panel::before {
  content: '';
  position: absolute;
  top: 8px;
  right: 50%;
  transform: translateX(50%);
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  z-index: 10;
}

/* 开发者面板标题样式 */
.dev-panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
}

.dev-panel-title .el-icon {
  font-size: 16px;
}

/* 开发者数据说明样式 */
.dev-data-description {
  margin-top: 16px;
  padding: 0 16px 16px;
}

.data-description-content p {
  margin: 8px 0;
  font-size: 13px;
  line-height: 1.5;
}

.data-description-content strong {
  color: #409eff;
  font-weight: 600;
}


/* PC端专用样式优化 */

/* 滚动条样式 */
.nav-menu::-webkit-scrollbar,
.tab-panel::-webkit-scrollbar {
  width: 4px;
}

.nav-menu::-webkit-scrollbar-track,
.tab-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.nav-menu::-webkit-scrollbar-thumb,
.tab-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.nav-menu::-webkit-scrollbar-thumb:hover,
.tab-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 滚动平滑 */
.tab-panel {
  scroll-behavior: smooth;
}

/* Loading优化样式 - 保留原生动画 */
.panel-container :deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* 动画优化 - 简化过渡效果 */
.nav-item {
  transition: background-color 0.2s ease;
}

.entrust-side-panel {
  transition: right 0.25s ease;
}
</style>