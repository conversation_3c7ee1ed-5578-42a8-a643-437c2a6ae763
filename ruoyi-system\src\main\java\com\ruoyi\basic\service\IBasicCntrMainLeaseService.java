package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCntrMainLease;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 租赁箱基础信息服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IBasicCntrMainLeaseService extends IService<BasicCntrMainLease> {

    /**
     * 新增租赁箱信息
     * 
     * @param basicCntrMainLease 租赁箱信息
     * @return 结果
     */
    AjaxResult addBasicCntrMainLease(BasicCntrMainLease basicCntrMainLease);

    /**
     * 修改租赁箱信息
     * 
     * @param basicCntrMainLease 租赁箱信息
     * @return 结果
     */
    AjaxResult editBasicCntrMainLease(BasicCntrMainLease basicCntrMainLease);
}
