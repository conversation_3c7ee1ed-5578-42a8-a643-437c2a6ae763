package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicPort;
import com.ruoyi.basic.domain.BasicPortArea;
import com.ruoyi.basic.domain.BasicTerminal;
import com.ruoyi.basic.mapper.BasicTerminalMapper;
import com.ruoyi.basic.service.IBasicPortAreaService;
import com.ruoyi.basic.service.IBasicPortService;
import com.ruoyi.basic.service.IBasicTerminalService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicTerminalServiceImpl extends ServiceImpl<BasicTerminalMapper, BasicTerminal> implements IBasicTerminalService {

    @Autowired
    private BasicTerminalMapper basicTerminalMapper;

    @Autowired
    IBasicPortService portService;

    @Autowired
    IBasicPortAreaService portAreaService;

    /**
     * 查询码头管理
     *
     * @param id 码头管理主键
     * @return 码头管理
     */
    @Override
    public BasicTerminal selectBasicTerminalById(String id)
    {
        return basicTerminalMapper.selectOneById(id);
    }

    /**
     * 查询码头管理列表
     *
     * @param basicTerminal 码头管理
     * @return 码头管理
     */
    @Override
    public List<BasicTerminal> selectBasicTerminalList(BasicTerminal basicTerminal)
    {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicTerminal.getTerminalCode())) {
            queryWrapper.like("terminal_code", basicTerminal.getTerminalCode());
        }
        if (StringUtils.isNotEmpty(basicTerminal.getTerminalName())) {
            queryWrapper.like("terminal_name", basicTerminal.getTerminalName());
        }
        if (StringUtils.isNotEmpty(basicTerminal.getType())) {
            queryWrapper.eq("type", basicTerminal.getType());
        }
        if (StringUtils.isNotEmpty(basicTerminal.getTerType())) {
            queryWrapper.eq("ter_type", basicTerminal.getTerType());
        }
        queryWrapper.orderBy("sort", true).orderBy("create_time", true);
        return basicTerminalMapper.selectListByQuery(queryWrapper);
    }

    /**
     * 新增码头管理
     *
     * @param basicTerminal 码头管理
     * @return 结果
     */
    @Override
    public int insertBasicTerminal(BasicTerminal basicTerminal) throws Exception {
        checkValid(basicTerminal);
        return basicTerminalMapper.insert(basicTerminal);
    }

    /**
     * 修改码头管理
     *
     * @param basicTerminal 码头管理
     * @return 结果
     */
    @Override
    public int updateBasicTerminal(BasicTerminal basicTerminal) throws Exception {
        checkValid(basicTerminal);
        return basicTerminalMapper.update(basicTerminal);
    }

    /**
     * 批量删除码头管理
     *
     * @param ids 需要删除的码头管理主键
     * @return 结果
     */
    @Override
    public int deleteBasicTerminalByIds(List<String> ids)
    {
        return basicTerminalMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除码头管理信息
     *
     * @param id 码头管理主键
     * @return 结果
     */
    @Override
    public int deleteBasicTerminalById(String id)
    {
        return basicTerminalMapper.deleteById(id);
    }

    public void checkValid(BasicTerminal basicTerminal) throws Exception {

        if(StringUtils.isEmpty(basicTerminal.getTerminalCode())){
            throw new Exception("码头代码不能为空");
        }

        if(StringUtils.isEmpty(basicTerminal.getTerminalName())){
            throw new Exception("码头名称不能为空");
        }

        if(StringUtils.isNull(basicTerminal.getSort())){
            throw new Exception("排序不能为空");
        }

        if(StringUtils.isEmpty(basicTerminal.getTerType())){
            throw new Exception("类型不能为空");
        }

        if(StringUtils.isEmpty(basicTerminal.getAreaId())){
            throw new Exception("区域不能为空");
        }

        //如果 港口 不为空 校验所选港口是否在所选区域
        if(StringUtils.isNotEmpty(basicTerminal.getPortId())){

            BasicPort basicPort = portService.getById(basicTerminal.getPortId());

            String areaId = basicPort.getAreaId();

            if(!basicTerminal.getAreaId().equals(areaId)){
                throw new Exception("所选港口不在所选区域");
            }

        }

        if(StringUtils.isNotEmpty(basicTerminal.getPortAreaId()) && StringUtils.isEmpty(basicTerminal.getPortId())){
            throw new Exception("请选中港口后再选择港区");
        }

        if(StringUtils.isNotEmpty(basicTerminal.getPortAreaId()) && StringUtils.isNotEmpty(basicTerminal.getPortId())){

            BasicPortArea basicPortArea = portAreaService.getById(basicTerminal.getPortAreaId());

            String portId = basicPortArea.getPortId();

            if(!basicTerminal.getPortId().equals(portId)){
                throw new Exception("所选港区不在选中港口");
            }

        }

    }

}
