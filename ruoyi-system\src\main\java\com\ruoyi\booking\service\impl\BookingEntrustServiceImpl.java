package com.ruoyi.booking.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.*;
import com.ruoyi.booking.domain.dto.BookingEntrustDTO;
import com.ruoyi.booking.domain.vo.BatchDeleteResultVO;
import com.ruoyi.booking.mapper.BookingMainMapper;
import com.ruoyi.booking.service.*;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.order.domain.OrderMain;
import com.ruoyi.order.service.IOrderMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.booking.domain.table.BookingCntrNumTableDef.BOOKING_CNTR_NUM;
import static com.ruoyi.booking.domain.table.BookingTransitPortTableDef.BOOKING_TRANSIT_PORT;
import static com.ruoyi.booking.domain.table.LogisticsMainTableDef.LOGISTICS_MAIN;

/**
 * 订舱委托服务实现类
 * 基于MyBatis-Flex框架，充分利用QueryWrapper和IService方法
 */
@Slf4j
@Service
public class BookingEntrustServiceImpl extends ServiceImpl<BookingMainMapper, BookingMain> implements IBookingEntrustService {

    @Autowired
    private IOrderMainService orderMainService;
    
    @Autowired
    private ILogisticsMainService logisticsMainService;
    
    @Autowired
    private IBookingCntrNumService bookingCntrNumService;
    
    @Autowired
    private IBookingTransitPortService bookingTransitPortService;
    
    @Autowired
    private ILogisticWaterwayService logisticWaterwayService;
    
    @Autowired
    private IBookingCntrNumSplitService bookingCntrNumSplitService;

    /**
     * 新增订舱委托核心实现
     * 
     * @param dto 订舱委托数据传输对象
     * @return 操作成功返回true，失败抛出异常
     * @throws ServiceException 当业务逻辑错误或数据库操作失败时抛出
     * @apiNote 核心业务流程：
     *          1. 自动填充托运单位信息（从bookingMain复制到orderMain）
     *          2. 保存订单主表并生成委托编号（WT+yyyyMMdd+6位序号）
     *          3. 保存订舱主表并生成订舱号（BK+yyyyMMdd+6位序号）
     *          4. 批量保存物流明细并自动创建水路运输记录
     *          5. 批量保存箱量信息，包含默认值设置
     *          6. 批量保存中转港信息，自动过滤空数据
     * @since V2.0 增加自动创建LOGISTIC_WATERWAY记录功能
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertEntrust(BookingEntrustDTO dto) {
        try {
            log.info("[insertEntrust] 开始新增订舱委托, 客户: {}", 
                dto.getBookingMain() != null ? dto.getBookingMain().getShipperName() : "未知");
            
            // 安全性处理：清除所有实体ID，防止前端传入恶意ID
            dto.getOrderMain().setId(null);
            dto.getBookingMain().setId(null);
            if (!CollectionUtils.isEmpty(dto.getLogisticsMainList())) {
                dto.getLogisticsMainList().forEach(item -> item.setId(null));
            }
            if (!CollectionUtils.isEmpty(dto.getBookingCntrNumList())) {
                dto.getBookingCntrNumList().forEach(item -> item.setId(null));
            }
            if (!CollectionUtils.isEmpty(dto.getBookingTransitPortList())) {
                dto.getBookingTransitPortList().forEach(item -> item.setId(null));
            }
            
            // 1. 处理托运单位信息（自动填充）从bookingMain中获取托运单位信息，填充到orderMain中。
            OrderMain orderMain = dto.getOrderMain();
            BookingMain bookingMain = dto.getBookingMain();
            if (orderMain.getShipperId() == null || "".equals(orderMain.getShipperId())) {
                orderMain.setShipperId(bookingMain.getShipperId());
                log.debug("[insertEntrust] orderMain.shipperId 自动填充为: {}", bookingMain.getShipperId());
            }
            if (orderMain.getShipperName() == null || "".equals(orderMain.getShipperName())) {
                orderMain.setShipperName(bookingMain.getShipperName());
                log.debug("[insertEntrust] orderMain.shipperName 自动填充为: {}", bookingMain.getShipperName());
            }

            // 2. 保存订单主表 orderMain
            orderMain.setId(null); // 主键由MyBatis-Flex自动生成
            orderMain.setOrderNo(generateOrderNo()); // 委托编号自动生成
            orderMain.setTotalContainers(0); // 初始箱量为0
            orderMain.setTotalWeight(new java.math.BigDecimal("0")); // 初始重量为0
            orderMain.setVersion(1); // 新增时版本固定为1
            orderMain.setDelFlag("0"); // 逻辑删除标志
            orderMainService.save(orderMain);
            log.debug("[insertEntrust] 订单主表(ORDER_MAIN)已保存, orderId={}", orderMain.getId());

            // 3. 保存订舱主表 bookingMain
            bookingMain.setId(null); // 主键由MyBatis-Flex自动生成
            bookingMain.setOrderId(orderMain.getId()); // 关联订单主表ID
            bookingMain.setBookingNumber(generateBookingNo()); // 订舱号自动生成
            bookingMain.setVersion(1); // 新增时版本固定为1
            bookingMain.setDelFlag("0"); // 逻辑删除标志
            this.save(bookingMain);
            log.debug("[insertEntrust] 订舱主表(BOOKING_MAIN)已保存, bookingId={}", bookingMain.getId());
            List<LogisticWaterway> waterwayList = null;
            // 4. 批量保存物流明细 logisticsMain 并自动创建对应的水路运输记录
            if (!CollectionUtils.isEmpty(dto.getLogisticsMainList())) {
                dto.getLogisticsMainList().forEach(logistics -> {
                    logistics.setId(null);
                    logistics.setBookingId(bookingMain.getId());
                    logistics.setVersion(1);
                    logistics.setDelFlag("0");
                });
                logisticsMainService.saveBatch(dto.getLogisticsMainList());
                log.debug("[insertEntrust] 物流明细(LOGISTICS_MAIN)已批量保存, count={}", dto.getLogisticsMainList().size());
                
                // 为水路运输环节创建对应的水路运输记录
                waterwayList = createWaterwayForLogistics(dto.getLogisticsMainList());
            }

            // 5. 批量保存箱量信息 bookingCntrNum
            if (!CollectionUtils.isEmpty(dto.getBookingCntrNumList())) {
                dto.getBookingCntrNumList().forEach(container -> {
                    container.setId(null);
                    container.setBookingId(bookingMain.getId());
                    container.setVersion(1);
                    container.setDelFlag("0");
                    if (container.getQuantity() == null) container.setQuantity(0);
                    if (container.getSingleWeight() == null) container.setSingleWeight(new java.math.BigDecimal("0"));
                    if (container.getTotalWeight() == null) container.setTotalWeight(new java.math.BigDecimal("0"));
                    if (container.getIsEmpty() == null) container.setIsEmpty("0");
                    if (container.getIsDangerous() == null) container.setIsDangerous("0");
                    if (container.getIsRefrigerated() == null) container.setIsRefrigerated("0");
                    if (container.getIsOversize() == null) container.setIsOversize("0");
                });
                bookingCntrNumService.saveBatch(dto.getBookingCntrNumList());
                log.debug("[insertEntrust] 箱量信息(BOOKING_CNTR_NUM)已批量保存, count={}", dto.getBookingCntrNumList().size());
                
                // 为柜量信息按水路运输数量创建拆分记录
                if (!CollectionUtils.isEmpty(waterwayList)) {
                    createSplitRecordsForContainers(dto.getBookingCntrNumList(), waterwayList);
                }
            }

            // 6. 批量保存中转港信息 bookingTransitPort
            if (!CollectionUtils.isEmpty(dto.getBookingTransitPortList())) {
                List<BookingTransitPort> validTransitPorts = dto.getBookingTransitPortList().stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getTransitPortId()))
                    .peek(transitPort -> { // 设置每个中转港记录的默认字段值
                        transitPort.setId(null);
                        transitPort.setBookingId(bookingMain.getId());
                        transitPort.setVersion(1);
                        transitPort.setDelFlag("0");
                        if (transitPort.getSequenceNo() == null) transitPort.setSequenceNo(1);
                    })
                    .collect(Collectors.toList());
                if (!validTransitPorts.isEmpty()) {
                    bookingTransitPortService.saveBatch(validTransitPorts);
                    log.debug("[insertEntrust] 中转港信息(BOOKING_TRANSIT_PORT)已批量保存, count={}", validTransitPorts.size());
                }
            }

            int logisticsCount = dto.getLogisticsMainList() != null ? dto.getLogisticsMainList().size() : 0;
            int waterwayCount = waterwayList != null ? waterwayList.size() : 0;
            int containerCount = dto.getBookingCntrNumList() != null ? dto.getBookingCntrNumList().size() : 0;
            int splitRecordsCount = waterwayCount * containerCount;
            log.info("[insertEntrust] 新增订舱委托成功 - 订单ID: {}, 订舱ID: {}, 委托编号: {}, 订舱号: {}, 物流段数: {}, 水路运输数: {}, 柜量数: {}, 拆分记录数: {}", 
                orderMain.getId(), bookingMain.getId(), orderMain.getOrderNo(), bookingMain.getBookingNumber(),
                logisticsCount, waterwayCount, containerCount, splitRecordsCount);
            return true;
        } catch (Exception e) {
            log.error("[insertEntrust] 新增订舱委托失败 - 客户: {}, 错误信息: {}", 
                dto.getBookingMain() != null ? dto.getBookingMain().getShipperName() : "未知", e.getMessage(), e);
            throw new ServiceException("新增订舱委托失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) 
    public boolean updateEntrust(BookingEntrustDTO dto) {
        try {
            log.info("[updateEntrust] 开始更新订舱委托, 订单ID: {}, 订舱ID: {}, 客户: {}", 
                dto.getOrderMain().getId(), dto.getBookingMain().getId(),
                dto.getBookingMain() != null ? dto.getBookingMain().getShipperName() : "未知");
            
            // 1. 乐观锁版本检查 - MyBatis-Flex自动处理，但我们提前验证
            OrderMain existOrder = orderMainService.getById(dto.getOrderMain().getId());
            if (existOrder == null) {
                throw new ServiceException("订舱委托不存在");
            }
            if (!existOrder.getVersion().equals(dto.getOrderMain().getVersion())) {
                throw new ServiceException("数据已被其他用户修改，请刷新后重试");
            }
            
            // 2. 主表更新 - 利用IService.updateById()，BaseEntity自动处理updateBy, updateTime
            orderMainService.updateById(dto.getOrderMain());
            this.updateById(dto.getBookingMain());
            log.debug("[updateEntrust] 主表更新完成");
            
            String bookingId = dto.getBookingMain().getId();
            
            // 3. 子表智能处理 - 充分利用IService批量方法
            handleLogisticsUpdate(bookingId, dto.getLogisticsMainList());
            handleContainerUpdate(bookingId, dto.getBookingCntrNumList());
            handleTransitPortUpdate(bookingId, dto.getBookingTransitPortList());
            
            log.info("[updateEntrust] 订舱委托更新成功 - 订单ID: {}, 订舱ID: {}", 
                dto.getOrderMain().getId(), dto.getBookingMain().getId());
            return true;
        } catch (Exception e) {
            log.error("[updateEntrust] 更新订舱委托失败, 订单ID: {}, 错误信息: {}", 
                dto.getOrderMain() != null ? dto.getOrderMain().getId() : "未知", e.getMessage(), e);
            throw new ServiceException("更新订舱委托失败: " + e.getMessage());
        }
    }

    /**
     * 根据订舱ID查询委托详情
     *
     * @param bookingId 订舱ID (booking_id)，注意：此参数为booking_main表的主键ID，不是order_main的ID
     * @return 委托详情DTO，包含三层架构数据：orderMain → bookingMain → logisticsMainList
     * @apiNote 该方法通过booking_id查询完整的委托数据，支持单证员工作台查看/编辑模式
     */
    @Override
    public BookingEntrustDTO selectEntrustById(String bookingId) {
        // 1. 查询订舱主表信息 (BOOKING_MAIN)
        BookingMain bookingMain = this.getById(bookingId);
        if (bookingMain == null || "1".equals(bookingMain.getDelFlag())) {
            return null;
        }
        
        // 2. 构建DTO并设置订舱主表
        BookingEntrustDTO dto = new BookingEntrustDTO();
        dto.setBookingMain(bookingMain);
        
        // 3. 查询关联的订单主表 (ORDER_MAIN)
        OrderMain orderMain = orderMainService.getById(bookingMain.getOrderId());
        dto.setOrderMain(orderMain);
        
        // 4. 查询物流明细 (LOGISTICS_MAIN) - 使用QueryWrapper构建查询条件
        QueryWrapper logisticsQuery = QueryWrapper.create()
            .where("BOOKING_ID = ?", bookingId)
            .and("DEL_FLAG = '0'")
            .orderBy("ID ASC");
        List<LogisticsMain> logisticsList = logisticsMainService.list(logisticsQuery);
        dto.setLogisticsMainList(logisticsList);
        
        // 5. 查询箱量信息 (BOOKING_CNTR_NUM) - 使用QueryWrapper构建查询条件
        QueryWrapper containerQuery = QueryWrapper.create()
            .where("BOOKING_ID = ?", bookingId)
            .and("DEL_FLAG = '0'")
            .orderBy("ID ASC");
        List<BookingCntrNum> containerList = bookingCntrNumService.list(containerQuery);
        dto.setBookingCntrNumList(containerList);
        
        // 6. 查询中转港信息 (BOOKING_TRANSIT_PORT) - 使用QueryWrapper构建查询条件
        QueryWrapper transitQuery = QueryWrapper.create()
            .where("BOOKING_ID = ?", bookingId)
            .and("DEL_FLAG = '0'")
            .orderBy("SEQUENCE_NO ASC");
        List<BookingTransitPort> transitPortList = bookingTransitPortService.list(transitQuery);
        dto.setBookingTransitPortList(transitPortList);
        
        return dto;
    }

    /**
     * 生成委托编号
     * 格式：WT+日期+6位序号
     * 使用Oracle序列确保唯一性和并发安全
     */
    private String generateOrderNo() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 直接执行SQL获取Oracle序列的下一个值
        try {
            // 构建原生SQL查询序列
            QueryWrapper queryWrapper = QueryWrapper.create()
                .select("SEQ_ORDER_NO.NEXTVAL as seq_value")
                .from("DUAL");
            
            // 使用原生SQL方式执行
            String sql = "SELECT SEQ_ORDER_NO.NEXTVAL as seq_value FROM DUAL";
            Long nextSeq = 1L;
            
            // 这里使用简化的逻辑，直接通过时间戳生成序号作为临时方案
            // 等序列创建完成后，可以通过JDBC直接执行SQL获取序列值
            long timestamp = System.currentTimeMillis();
            nextSeq = (timestamp % 1000000L);
            
            return String.format("WT%s%06d", dateStr, nextSeq);
            
        } catch (Exception e) {
            log.error("获取订单号序列失败，使用时间戳作为备用方案", e);
            // 备用方案：使用时间戳的后6位
            long timestamp = System.currentTimeMillis();
            int fallbackSeq = (int) (timestamp % 1000000);
            return String.format("WT%s%06d", dateStr, fallbackSeq);
        }
    }

    /**
     * 生成订舱号
     * 格式：BK+日期+6位序号
     * 使用Oracle序列确保唯一性和并发安全
     */
    private String generateBookingNo() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 直接执行SQL获取Oracle序列的下一个值
        try {
            // 构建原生SQL查询序列
            QueryWrapper queryWrapper = QueryWrapper.create()
                .select("SEQ_BOOKING_NO.NEXTVAL as seq_value")
                .from("DUAL");
            
            // 使用原生SQL方式执行
            String sql = "SELECT SEQ_BOOKING_NO.NEXTVAL as seq_value FROM DUAL";
            Long nextSeq = 1L;
            
            // 这里使用简化的逻辑，直接通过时间戳生成序号作为临时方案
            // 等序列创建完成后，可以通过JDBC直接执行SQL获取序列值
            long timestamp = System.currentTimeMillis();
            nextSeq = (timestamp % 1000000L);
            
            return String.format("BK%s%06d", dateStr, nextSeq);
            
        } catch (Exception e) {
            log.error("获取订舱号序列失败，使用时间戳作为备用方案", e);
            // 备用方案：使用时间戳的后6位
            long timestamp = System.currentTimeMillis();
            int fallbackSeq = (int) (timestamp % 1000000);
            return String.format("BK%s%06d", dateStr, fallbackSeq);
        }
    }

    /**
     * 为水路运输环节创建对应的水路运输记录
     * 
     * @param logisticsMainList 物流主表列表，里面每个logisticsMain对象都包含id
     * @return 创建的水路运输记录列表
     * @apiNote 根据LOGISTIC_WATERWAY表结构仅为 businessType='WATERWAY' 的运输环节创建水路运输记录
     *          自动填充字段：
     *          1. logisticsMainId: 关联物流主表ID
     *          2. sequenceNo: 运输段序号，默认为1
     *          3. status: 状态，默认为'PENDING'（待处理）
     *          4. delFlag: 删除标志，默认为'0'（未删除）
     *          5. version: 版本号，默认为1
     *          6. 港口冗余字段：从LogisticsMain复制originLocation -> loadingTerminal, destinationLocation -> unloadingTerminal
     *          7. 时间冗余字段：从LogisticsMain复制requiredDepartureDate -> etd, requiredArrivalDate -> eta
     * @since V3.0 只为水路运输环节创建记录功能
     */
    private List<LogisticWaterway> createWaterwayForLogistics(List<LogisticsMain> logisticsMainList) {
        try {
            // 只为 businessType='WATERWAY' 的运输环节创建水路运输记录
            List<LogisticWaterway> waterwayRecords = logisticsMainList.stream()
                .filter(logistics -> "WATERWAY".equals(logistics.getBusinessType()))
                .map(logistics -> {
                    LogisticWaterway waterway = new LogisticWaterway();
                    
                    // 关联物流主表ID
                    waterway.setLogisticsMainId(logistics.getId());
                    
                    // 基础字段设置
                    waterway.setSequenceNo(1); // 默认序号为1
                    waterway.setStatus("PENDING"); // 默认状态为待处理
                    waterway.setDelFlag("0"); // 默认未删除
                    waterway.setVersion(1L); // 默认版本为1
                    
                    // 从物流主表复制港口信息和时间信息（冗余字段）
                    waterway.setLoadingTerminalId(logistics.getOriginLocationId());
                    waterway.setLoadingTerminalName(logistics.getOriginLocationName());
                    waterway.setUnloadingTerminalId(logistics.getDestinationLocationId());
                    waterway.setUnloadingTerminalName(logistics.getDestinationLocationName());
                    
                    // 复制时间信息
                    waterway.setEtd(logistics.getRequiredDepartureDate());
                    waterway.setEta(logistics.getRequiredArrivalDate());
                    
                    log.debug("[createWaterwayForLogistics] 为水路运输环节 {} 创建水路运输记录，装货港: {}, 卸货港: {}, ETD: {}, ETA: {}", 
                        logistics.getId(), 
                        logistics.getOriginLocationName(),
                        logistics.getDestinationLocationName(),
                        logistics.getRequiredDepartureDate(),
                        logistics.getRequiredArrivalDate());
                    return waterway;
                })
                .collect(Collectors.toList());
            
            // 批量保存水路运输记录
            if (!waterwayRecords.isEmpty()) {
                logisticWaterwayService.saveBatch(waterwayRecords);
                log.info("[createWaterwayForLogistics] 成功为 {} 个水路运输环节创建 {} 条水路运输记录", 
                    logisticsMainList.stream().filter(l -> "WATERWAY".equals(l.getBusinessType())).count(), 
                    waterwayRecords.size());
            } else {
                log.info("[createWaterwayForLogistics] 未找到水路运输环节，跳过创建水路运输记录");
            }
            
            return waterwayRecords;
        } catch (Exception e) {
            log.error("[createWaterwayForLogistics] 创建水路运输记录失败", e);
            throw new ServiceException("创建水路运输记录失败: " + e.getMessage());
        }
    }

    /**
     * 为柜量信息按水路运输数量创建拆分记录
     * 
     * @param containerList 柜量信息列表
     * @param waterwayList 水路运输列表
     * @apiNote 核心业务规则：
     *          1. 为每条 BookingCntrNum 创建 N 条 BookingCntrNumSplit（N = 水路运输环节数量）
     *          2. 每条拆分记录除了 relatedWaterwayId 不同外，其他字段完全相同
     *          3. 每条拆分记录的 splitQuantity = 原始 quantity（不进行数量拆分）
     *          4. 每条拆分记录的 splitTotalWeight = 原始 totalWeight
     *          5. 每个 LogisticWaterway 对应一个 BookingCntrNumSplit 记录
     * @since V3.0 柜量信息按水路运输环节自动拆分功能
     */
    private void createSplitRecordsForContainers(List<BookingCntrNum> containerList, List<LogisticWaterway> waterwayList) {
        try {
            if (CollectionUtils.isEmpty(containerList) || CollectionUtils.isEmpty(waterwayList)) {
                log.warn("[createSplitRecordsForContainers] 柜量信息或水路运输列表为空，跳过创建拆分记录");
                return;
            }
            
            List<BookingCntrNumSplit> splitRecords = new ArrayList<>();
            
            // 为每条柜量记录 × 每个水路运输记录创建拆分记录
            for (BookingCntrNum container : containerList) {
                for (LogisticWaterway waterway : waterwayList) {
                    BookingCntrNumSplit split = copyContainerToSplit(container, waterway.getId());
                    splitRecords.add(split);
                }
            }
            
            // 批量保存拆分记录
            if (!splitRecords.isEmpty()) {
                bookingCntrNumSplitService.saveBatch(splitRecords);
                log.info("[createSplitRecordsForContainers] 成功创建 {} 条拆分记录 ({}条柜量 × {}个水路运输环节)", 
                    splitRecords.size(), containerList.size(), waterwayList.size());
            }
            
        } catch (Exception e) {
            log.error("[createSplitRecordsForContainers] 创建柜量拆分记录失败", e);
            throw new ServiceException("创建柜量拆分记录失败: " + e.getMessage());
        }
    }

    /**
     * 复制柜量属性到拆分记录
     * 
     * @param container 原始柜量记录
     * @param waterwayId 关联的水路运输ID
     * @return 拆分记录
     * @apiNote 字段复制一致性：
     *          - 继承所有柜量属性（尺寸、箱型、危险品、货类等）
     *          - splitQuantity = 原始 quantity（不分割数量）
     *          - splitTotalWeight = 原始 totalWeight（不分割重量）
     *          - relatedWaterwayId = 传入的水路运输ID（唯一不同的字段）
     */
    private BookingCntrNumSplit copyContainerToSplit(BookingCntrNum container, String waterwayId) {
        BookingCntrNumSplit split = new BookingCntrNumSplit();
        
        // 关联字段
        split.setBookingCntrNumId(container.getId());
        split.setRelatedWaterwayId(waterwayId);
        
        // 容器尺寸字段组
        split.setContainerSizeId(container.getContainerSizeId());
        split.setContainerSizeCode(container.getContainerSizeCode());
        split.setContainerSizeName(container.getContainerSizeName());
        
        // 容器类型字段组
        split.setContainerTypeId(container.getContainerTypeId());
        split.setContainerTypeCode(container.getContainerTypeCode());
        split.setContainerTypeName(container.getContainerTypeName());
        
        // 基础属性
        split.setIsEmpty(container.getIsEmpty());
        split.setIsDangerous(container.getIsDangerous());
        split.setIsRefrigerated(container.getIsRefrigerated());
        split.setIsOversize(container.getIsOversize());
        split.setOversizeDimensions(container.getOversizeDimensions());
        
        // 危险品等级字段组
        split.setDangerousLevelId(container.getDangerousLevelId());
        split.setDangerousLevelCode(container.getDangerousLevelCode());
        split.setDangerousLevelName(container.getDangerousLevelName());
        
        // 货类字段组
        split.setCargoTypeId(container.getCargoTypeId());
        split.setCargoTypeCode(container.getCargoTypeCode());
        split.setCargoTypeName(container.getCargoTypeName());
        
        // 数量和重量（不进行拆分，完全复制）
        split.setSplitQuantity(container.getQuantity());
        split.setSplitTotalWeight(container.getTotalWeight());
        
        // 备注
        split.setRemark(container.getRemark());
        
        // 系统字段
        split.setDelFlag("0");
        split.setVersion(1L);
        
        return split;
    }

    /**
     * 批量删除订舱委托
     *
     * @param bookingIds 订舱ID数组
     * @return 删除结果统计信息
     * @throws ServiceException 当删除过程中发生错误时抛出
     * @apiNote 核心删除逻辑：
     *          1. 删除关联的子表记录（LOGISTICS_MAIN, BOOKING_CNTR_NUM, BOOKING_TRANSIT_PORT）
     *          2. 删除主表记录（BOOKING_MAIN）
     *          3. 检测关联的ORDER_MAIN是否还有其他BOOKING_MAIN，删除无其他关联的ORDER_MAIN
     *          4. 返回详细的删除统计结果，包括删除的订舱数量、订单数量以及子表清理统计
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchDeleteResultVO deleteBatchBookingEntrusts(String[] bookingIds) {
        try {
            log.info("[deleteBatchBookingEntrusts] 开始批量删除订舱委托, 总数: {}, IDs: {}", 
                bookingIds.length, Arrays.toString(bookingIds));

            if (bookingIds == null || bookingIds.length == 0) {
                log.warn("[deleteBatchBookingEntrusts] 删除参数为空，返回空结果");
                return BatchDeleteResultVO.empty();
            }

            List<String> bookingIdList = Arrays.asList(bookingIds);

            // ================================ 阶段1: 数据预处理 ================================
            // 查询待删除的BOOKING_MAIN记录，获取关联的ORDER_MAIN IDs和业务编号信息
            // 这一步是为了后续的关联检查和结果统计做准备
            List<BookingMain> bookingsToDelete = this.listByIds(bookingIdList);
            if (CollectionUtils.isEmpty(bookingsToDelete)) {
                log.warn("[deleteBatchBookingEntrusts] 未找到待删除的订舱记录");
                return BatchDeleteResultVO.empty();
            }

            // 从查询结果中提取关键信息：
            // 1. 关联的ORDER_MAIN ID列表（用于后续关联检查）
            // 2. 删除的订舱编号列表（用于结果展示）
            List<String> relatedOrderIds = bookingsToDelete.stream()
                .map(BookingMain::getOrderId)
                .distinct()
                .collect(Collectors.toList());
            
            List<String> deletedBookingNos = bookingsToDelete.stream()
                .map(BookingMain::getBookingNumber)
                .collect(Collectors.toList());

            log.info("[deleteBatchBookingEntrusts] 找到 {} 条订舱记录，关联 {} 个订单", 
                bookingsToDelete.size(), relatedOrderIds.size());

            // ================================ 阶段2: 删除前业务检查 ================================
            // TODO: 添加删除前的业务检查逻辑（初期阶段暂不实现）
            // 1. 检查是否存在已分配的BOOKING_CNTR_NUM_SPLIT记录（状态为ALLOCATED）
            // 2. 检查是否存在关联的配载计划或实际运输记录
            // 3. 检查是否存在关联的财务结算记录
            // 4. 检查订舱状态是否允许删除（如已确认的订舱可能不允许删除）
            // 5. 检查是否存在关联的CODECO报文或其他业务单据
            log.debug("[deleteBatchBookingEntrusts] TODO: 删除前业务检查功能待实现");
            
            // ================================ 阶段3: 删除子表记录 ================================
            // 按照数据库外键关系，先删除所有子表记录，避免外键约束冲突
            // 包括：LOGISTICS_MAIN -> LOGISTIC_WATERWAY, BOOKING_CNTR_NUM_SPLIT -> BOOKING_CNTR_NUM, BOOKING_TRANSIT_PORT
            BatchDeleteResultVO.ChildTableDeleteStatistics childStats = deleteChildTablesInBatch(bookingIdList);
            log.info("[deleteBatchBookingEntrusts] 子表删除完成: {}", childStats);

            // ================================ 阶段4: 删除主表记录 ================================
            // 子表记录删除完成后，安全删除BOOKING_MAIN主表记录
            boolean bookingDeleteResult = this.removeByIds(bookingIdList);
            if (!bookingDeleteResult) {
                throw new ServiceException("删除订舱主表记录失败");
            }
            log.info("[deleteBatchBookingEntrusts] 成功删除 {} 条订舱主表记录", bookingsToDelete.size());

            // ================================ 阶段5: 处理关联的ORDER_MAIN记录 ================================
            // 检测哪些ORDER_MAIN记录已无其他BOOKING_MAIN关联，可以安全删除
            // 这一步确保数据的完整性，避免出现孤立的订单记录
            List<String> unlinkedOrderIds = findUnlinkedOrderIds(relatedOrderIds);
            List<String> deletedOrderNos = new ArrayList<>();
            List<String> retainedOrderNos = new ArrayList<>();

            if (!CollectionUtils.isEmpty(unlinkedOrderIds)) {
                // 查询无关联订单的编号
                List<OrderMain> unlinkedOrders = orderMainService.listByIds(unlinkedOrderIds);
                deletedOrderNos = unlinkedOrders.stream()
                    .map(OrderMain::getOrderNo)
                    .collect(Collectors.toList());

                boolean orderDeleteResult = orderMainService.removeByIds(unlinkedOrderIds);
                if (!orderDeleteResult) {
                    throw new ServiceException("删除无关联订单记录失败");
                }
                log.info("[deleteBatchBookingEntrusts] 成功删除 {} 条无关联订单记录: {}", 
                    unlinkedOrderIds.size(), deletedOrderNos);
            }

            // 查询保留的订单编号
            List<String> retainedOrderIds = relatedOrderIds.stream()
                .filter(orderId -> !unlinkedOrderIds.contains(orderId))
                .collect(Collectors.toList());
            
            if (!CollectionUtils.isEmpty(retainedOrderIds)) {
                List<OrderMain> retainedOrders = orderMainService.listByIds(retainedOrderIds);
                retainedOrderNos = retainedOrders.stream()
                    .map(OrderMain::getOrderNo)
                    .collect(Collectors.toList());
                log.info("[deleteBatchBookingEntrusts] 保留 {} 条仍有关联的订单记录: {}", 
                    retainedOrderIds.size(), retainedOrderNos);
            }

            // 构建删除结果
            BatchDeleteResultVO result = BatchDeleteResultVO.success(
                bookingsToDelete.size(),
                unlinkedOrderIds.size(),
                retainedOrderIds.size(),
                childStats,
                deletedBookingNos,
                deletedOrderNos,
                retainedOrderNos
            );

            log.info("[deleteBatchBookingEntrusts] 批量删除操作完成: {}", result.getOperationSummary());
            return result;

        } catch (Exception e) {
            log.error("[deleteBatchBookingEntrusts] 批量删除订舱委托失败, IDs: {}, 错误信息: {}", 
                Arrays.toString(bookingIds), e.getMessage(), e);
            throw new ServiceException("批量删除订舱委托失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除子表记录
     * 
     * 该方法按照以下顺序删除与订舱委托相关的所有子表记录：
     * 
     * 1. LOGISTICS_MAIN (物流主表)
     *    - 直接通过 BOOKING_ID 关联删除
     *    - 存储订舱对应的物流业务基本信息（业务类型、供应商等）
     * 
     * 2. LOGISTIC_WATERWAY (水路运输表)
     *    - 通过 LOGISTICS_ID 关联删除（二级关联）
     *    - 存储具体的运输段信息（航线、船舶、港口、ETD/ETA等）
     *    - 包含配载计划和集装箱分配信息
     * 
     * 3. BOOKING_CNTR_NUM (箱量信息表)
     *    - 直接通过 BOOKING_ID 关联删除
     *    - 存储集装箱的尺寸、类型、数量等信息
     *    - 用于配载计划和费用计算
     * 
     * 4. BOOKING_TRANSIT_PORT (中转港信息表)
     *    - 直接通过 BOOKING_ID 关联删除
     *    - 存储多段运输中的中转港详细信息
     *    - 按 SEQUENCE_NO 排序，支持复杂的多段运输路线
     * 
     * 注意事项：
     * - 删除顺序很重要，先删除子表再删除父表，避免外键约束冲突
     * - LOGISTIC_WATERWAY 需要通过 LOGISTICS_MAIN 的 ID 列表进行二级删除
     * - 所有操作都在事务中执行，确保数据一致性
     * 
     * @param bookingIds 要删除的订舱ID列表（非空）
     * @return 子表删除统计结果，包含每个子表的删除数量和总计
     */
    private BatchDeleteResultVO.ChildTableDeleteStatistics deleteChildTablesInBatch(List<String> bookingIds) {
        log.debug("[deleteChildTablesInBatch] 开始删除子表记录, 订舱数量: {}", bookingIds.size());

        // 阶段1: 删除物流主表(LOGISTICS_MAIN)记录
        // 这是最重要的子表，存储订舱对应的物流业务基本信息
        QueryWrapper logisticsQuery = QueryWrapper.create()
            .in("BOOKING_ID", bookingIds.toArray());
        List<LogisticsMain> logisticsToDelete = logisticsMainService.list(logisticsQuery);
        int logisticsCount = logisticsToDelete.size();
        if (logisticsCount > 0) {
            logisticsMainService.remove(logisticsQuery);
            log.debug("[deleteChildTablesInBatch] 删除物流主表记录: {} 条", logisticsCount);
        }

        // 阶段2: 删除水路运输(LOGISTIC_WATERWAY)记录
        // 注意：这是二级关联删除，需要通过上一步获取的 LOGISTICS_MAIN 的 ID 列表进行关联删除
        int waterwayCount = 0;
        if (!CollectionUtils.isEmpty(logisticsToDelete)) {
            List<String> logisticsIds = logisticsToDelete.stream()
                .map(LogisticsMain::getId)
                .collect(Collectors.toList());
            
            QueryWrapper waterwayQuery = QueryWrapper.create()
                .in("LOGISTICS_MAIN_ID", logisticsIds.toArray());
            waterwayCount = (int) logisticWaterwayService.count(waterwayQuery);
            if (waterwayCount > 0) {
                logisticWaterwayService.remove(waterwayQuery);
                log.debug("[deleteChildTablesInBatch] 删除水路运输记录: {} 条", waterwayCount);
            }
        }

        // 阶段3: 删除箱量拆分明细(BOOKING_CNTR_NUM_SPLIT)记录
        // 注意：这是三级关联删除，需要通过 BOOKING_CNTR_NUM 的 ID 列表进行关联删除
        int splitCount = 0;
        // 先查询要删除的BOOKING_CNTR_NUM记录
        QueryWrapper containerQuery = QueryWrapper.create()
            .in("BOOKING_ID", bookingIds.toArray());
        List<BookingCntrNum> containersToDelete = bookingCntrNumService.list(containerQuery);
        if (!CollectionUtils.isEmpty(containersToDelete)) {
            List<String> containerIds = containersToDelete.stream()
                .map(BookingCntrNum::getId)
                .collect(Collectors.toList());
            
            // 删除拆分明细记录
            QueryWrapper splitQuery = QueryWrapper.create()
                .in("BOOKING_CNTR_NUM_ID", containerIds.toArray());
            splitCount = (int) bookingCntrNumSplitService.count(splitQuery);
            if (splitCount > 0) {
                bookingCntrNumSplitService.remove(splitQuery);
                log.debug("[deleteChildTablesInBatch] 删除箱量拆分明细记录: {} 条", splitCount);
            }
        }
        
        // 阶段4: 删除箱量信息(BOOKING_CNTR_NUM)记录
        // 存储集装箱的尺寸、类型、数量等信息，用于配载计划和费用计算
        int containerCount = (int) bookingCntrNumService.count(containerQuery);
        if (containerCount > 0) {
            bookingCntrNumService.remove(containerQuery);
            log.debug("[deleteChildTablesInBatch] 删除箱量信息记录: {} 条", containerCount);
        }

        // 阶段5: 删除中转港信息(BOOKING_TRANSIT_PORT)记录
        // 存储多段运输中的中转港详细信息，按序列号排序
        QueryWrapper transitQuery = QueryWrapper.create()
            .in("BOOKING_ID", bookingIds.toArray());
        int transitCount = (int) bookingTransitPortService.count(transitQuery);
        if (transitCount > 0) {
            bookingTransitPortService.remove(transitQuery);
            log.debug("[deleteChildTablesInBatch] 删除中转港信息记录: {} 条", transitCount);
        }

        BatchDeleteResultVO.ChildTableDeleteStatistics stats = 
            new BatchDeleteResultVO.ChildTableDeleteStatistics(
                logisticsCount, waterwayCount, splitCount, containerCount, transitCount,
                logisticsCount + waterwayCount + splitCount + containerCount + transitCount
            );

        log.info("[deleteChildTablesInBatch] 子表删除统计完成: 物流{}条, 水路运输{}条, 箱量拆分{}条, 箱量{}条, 中转港{}条, 总计{}条",
            logisticsCount, waterwayCount, splitCount, containerCount, transitCount, stats.getTotalChildRecords());

        return stats;
    }

    /**
     * 查找无关联的订单ID列表
     * 
     * 该方法用于判断哪些ORDER_MAIN记录可以安全删除。
     * 在订舱委托系统中，一个订单(ORDER_MAIN)可能对应多个订舱委托(BOOKING_MAIN)记录。
     * 只有当一个订单下的所有订舱委托都被删除时，该订单才能被安全删除。
     * 
     * 检查逻辑：
     * 1. 对每个订单ID，查询BOOKING_MAIN表中是否还有其他未删除的订舱记录
     * 2. 如果查询结果为0，说明该订单已无其他关联，可以安全删除
     * 3. 如果查询结果>0，说明该订单还有其他订舱关联，应保留
     * 
     * 示例场景：
     * - 订单A 有 3个订舱委托（B1, B2, B3）
     * - 如果只删除 B1，则订单A应保留（因为B2,B3还存在）
     * - 如果删除 B1,B2,B3，则订单A可以安全删除
     * 
     * @param orderIds 待检查的订单ID列表（可为空）
     * @return 无其他BOOKING_MAIN关联的ORDER_MAIN ID列表（可以安全删除的订单列表）
     */
    private List<String> findUnlinkedOrderIds(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }

        log.debug("[findUnlinkedOrderIds] 开始检测无关联订单, 订单数量: {}", orderIds.size());

        List<String> unlinkedOrderIds = new ArrayList<>();

        for (String orderId : orderIds) {
            // 查询该订单是否还有其他未删除的订舱记录
            // 使用 DEL_FLAG = '0' 条件确保只统计未删除的记录
            QueryWrapper bookingQuery = QueryWrapper.create()
                .where("ORDER_ID = ?", orderId)
                .and("DEL_FLAG = '0'");
            
            long remainingBookingCount = this.count(bookingQuery);
            
            if (remainingBookingCount == 0) {
                // 订单下无其他订舱记录，可以安全删除
                unlinkedOrderIds.add(orderId);
                log.debug("[findUnlinkedOrderIds] 发现无关联订单: {}", orderId);
            } else {
                // 订单下还有其他订舱记录，应保留订单
                log.debug("[findUnlinkedOrderIds] 订单仍有关联: {}, 剩余订舱记录: {} 条", orderId, remainingBookingCount);
            }
        }

        log.info("[findUnlinkedOrderIds] 无关联订单检测完成: {} / {} 个订单无其他关联", 
            unlinkedOrderIds.size(), orderIds.size());

        return unlinkedOrderIds;
    }

    /**
     * 运输环节LogisticsMain智能更新 - 充分利用IService特性
     * 
     * @param bookingId 订舱ID
     * @param newList 新的运输环节列表
     */
    private void handleLogisticsUpdate(String bookingId, List<LogisticsMain> newList) {
        try {
            // 1. 查询现有数据 - 使用IService.list()和条件表达式，避免手写QueryWrapper
            List<LogisticsMain> existingList = logisticsMainService.list(
                LOGISTICS_MAIN.BOOKING_ID.eq(bookingId)
            );
            
            // 2. 差异分析
            ListDiffResult<LogisticsMain> diff = compareByIds(existingList, newList, LogisticsMain::getId);
            
            // 3. 批量操作 - 充分利用IService方法
            // 删除操作
            if (!diff.getToDelete().isEmpty()) {
                // 使用IService.removeByIds() - 自动处理逻辑删除
                List<String> deleteIds = diff.getToDelete().stream()
                    .map(LogisticsMain::getId).collect(Collectors.toList());
                logisticsMainService.removeByIds(deleteIds);
                
                // TODO: 运输环节子表删除逻辑（演示阶段标记）
                log.info("TODO: 删除运输环节关联的子表数据, logisticsIds: {}", deleteIds);
                log.debug("[handleLogisticsUpdate] 删除运输环节: {} 条", deleteIds.size());
            }

            // 新增操作
            if (!diff.getToAdd().isEmpty()) {
                // 使用IService.saveBatch() - BaseEntity自动填充createBy
                diff.getToAdd().forEach(item -> {
                    item.setId(null); // 重新生成ID
                    item.setBookingId(bookingId);
                });
                logisticsMainService.saveBatch(diff.getToAdd());
                log.debug("[handleLogisticsUpdate] 新增运输环节: {} 条", diff.getToAdd().size());
            }

            // 更新操作
            if (!diff.getToUpdate().isEmpty()) {
                // 使用IService.updateBatch() - BaseEntity自动填充updateBy
                logisticsMainService.updateBatch(diff.getToUpdate());
                log.debug("[handleLogisticsUpdate] 更新运输环节: {} 条", diff.getToUpdate().size());
            }
            
            log.info("[handleLogisticsUpdate] 运输环节处理完成 - 删除:{}, 新增:{}, 更新:{}", 
                diff.getToDelete().size(), diff.getToAdd().size(), diff.getToUpdate().size());
            
        } catch (Exception e) {
            log.error("[handleLogisticsUpdate] 运输环节更新失败", e);
            throw new ServiceException("运输环节更新失败: " + e.getMessage());
        }
    }

    /**
     * 柜量信息智能更新 - 智能对比策略
     * 
     * @param bookingId 订舱ID  
     * @param newList 新的柜量信息列表
     */
    private void handleContainerUpdate(String bookingId, List<BookingCntrNum> newList) {
        try {
            // 1. 查询现有数据 - 使用IService.list()和条件表达式，避免手写QueryWrapper
            List<BookingCntrNum> existingList = bookingCntrNumService.list(
                BOOKING_CNTR_NUM.BOOKING_ID.eq(bookingId)
            );
            
            // 2. 差异分析
            ListDiffResult<BookingCntrNum> diff = compareByIds(existingList, newList, BookingCntrNum::getId);
            
            // 3. 批量操作 - 充分利用IService方法
            // 删除操作
            if (!diff.getToDelete().isEmpty()) {
                // 使用IService.removeByIds() - 自动处理逻辑删除
                List<String> deleteIds = diff.getToDelete().stream()
                    .map(BookingCntrNum::getId).collect(Collectors.toList());
                bookingCntrNumService.removeByIds(deleteIds);
                log.debug("[handleContainerUpdate] 删除柜量信息: {} 条", deleteIds.size());
            }

            // 新增操作
            if (!diff.getToAdd().isEmpty()) {
                // 预处理新增数据并批量保存
                diff.getToAdd().forEach(item -> {
                    item.setId(null); // 重新生成ID
                    item.setBookingId(bookingId);
                    // 设置默认值
                    if (item.getQuantity() == null) item.setQuantity(0);
                    if (item.getSingleWeight() == null) item.setSingleWeight(new java.math.BigDecimal("0"));
                    if (item.getTotalWeight() == null) item.setTotalWeight(new java.math.BigDecimal("0"));
                    if (item.getIsEmpty() == null) item.setIsEmpty("0");
                    if (item.getIsDangerous() == null) item.setIsDangerous("0");
                    if (item.getIsRefrigerated() == null) item.setIsRefrigerated("0");
                    if (item.getIsOversize() == null) item.setIsOversize("0");
                });
                
                // 使用IService.saveBatch() - BaseEntity自动填充createBy
                bookingCntrNumService.saveBatch(diff.getToAdd());
                log.debug("[handleContainerUpdate] 新增柜量信息: {} 条", diff.getToAdd().size());
            }

            // 更新操作
            if (!diff.getToUpdate().isEmpty()) {
                // 预处理更新数据
                diff.getToUpdate().forEach(item -> {
                    // 确保默认值正确
                    if (item.getQuantity() == null) item.setQuantity(0);
                    if (item.getSingleWeight() == null) item.setSingleWeight(new java.math.BigDecimal("0"));
                    if (item.getTotalWeight() == null) item.setTotalWeight(new java.math.BigDecimal("0"));
                    if (item.getIsEmpty() == null) item.setIsEmpty("0");
                    if (item.getIsDangerous() == null) item.setIsDangerous("0");
                    if (item.getIsRefrigerated() == null) item.setIsRefrigerated("0");
                    if (item.getIsOversize() == null) item.setIsOversize("0");
                });
                
                // 使用IService.updateBatch() - BaseEntity自动填充updateBy
                bookingCntrNumService.updateBatch(diff.getToUpdate());
                log.debug("[handleContainerUpdate] 更新柜量信息: {} 条", diff.getToUpdate().size());
            }
            
            log.info("[handleContainerUpdate] 柜量信息处理完成 - 删除:{}, 新增:{}, 更新:{}", 
                diff.getToDelete().size(), diff.getToAdd().size(), diff.getToUpdate().size());
                
        } catch (Exception e) {
            log.error("[handleContainerUpdate] 柜量信息更新失败", e);
            throw new ServiceException("柜量信息更新失败: " + e.getMessage());
        }
    }

    /**
     * 中转港智能更新（含序号重排）- 智能对比策略
     * 
     * @param bookingId 订舱ID
     * @param newList 新的中转港列表
     */
    private void handleTransitPortUpdate(String bookingId, List<BookingTransitPort> newList) {
        try {
            // 1. 查询现有数据 - 使用IService.list()和条件表达式，避免手写QueryWrapper
            List<BookingTransitPort> existingList = bookingTransitPortService.list(
                BOOKING_TRANSIT_PORT.BOOKING_ID.eq(bookingId)
            );
            
            // 2. 预处理新数据 - 过滤有效数据并重新排序
            List<BookingTransitPort> validNewList = CollectionUtils.isEmpty(newList) ? 
                new ArrayList<>() : 
                newList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getTransitPortId()))
                    .collect(Collectors.toList());
            
            // 3. 序号重排 - 为有效的新数据重新分配序号
            for (int i = 0; i < validNewList.size(); i++) {
                validNewList.get(i).setSequenceNo(i + 1);
                validNewList.get(i).setBookingId(bookingId);
            }
            
            // 4. 差异分析
            ListDiffResult<BookingTransitPort> diff = compareByIds(existingList, validNewList, BookingTransitPort::getId);
            
            // 5. 批量操作 - 充分利用IService方法
            // 删除操作
            if (!diff.getToDelete().isEmpty()) {
                // 使用IService.removeByIds() - 自动处理逻辑删除
                List<String> deleteIds = diff.getToDelete().stream()
                    .map(BookingTransitPort::getId).collect(Collectors.toList());
                bookingTransitPortService.removeByIds(deleteIds);
                log.debug("[handleTransitPortUpdate] 删除中转港: {} 条", deleteIds.size());
            }

            // 新增操作
            if (!diff.getToAdd().isEmpty()) {
                // 新增数据预处理
                diff.getToAdd().forEach(item -> {
                    item.setId(null); // 重新生成ID
                    item.setBookingId(bookingId);
                });
                
                // 使用IService.saveBatch() - BaseEntity自动填充createBy
                bookingTransitPortService.saveBatch(diff.getToAdd());
                log.debug("[handleTransitPortUpdate] 新增中转港: {} 条", diff.getToAdd().size());
            }

            // 更新操作
            if (!diff.getToUpdate().isEmpty()) {
                // 更新数据预处理 - 确保序号正确
                diff.getToUpdate().forEach(item -> {
                    // 序号已在预处理阶段设置，这里可以添加其他必要的处理
                    item.setBookingId(bookingId);
                });
                
                // 使用IService.updateBatch() - BaseEntity自动填充updateBy
                bookingTransitPortService.updateBatch(diff.getToUpdate());
                log.debug("[handleTransitPortUpdate] 更新中转港: {} 条", diff.getToUpdate().size());
            }
            
            log.info("[handleTransitPortUpdate] 中转港处理完成 - 删除:{}, 新增:{}, 更新:{}", 
                diff.getToDelete().size(), diff.getToAdd().size(), diff.getToUpdate().size());
                
        } catch (Exception e) {
            log.error("[handleTransitPortUpdate] 中转港更新失败", e);
            throw new ServiceException("中转港更新失败: " + e.getMessage());
        }
    }

    /**
     * 通用列表差异对比算法 - 减少QueryWrapper使用
     * 
     * @param existingList 现有列表
     * @param newList 新列表
     * @param idExtractor ID提取函数
     * @return 差异对比结果
     */
    private <T> ListDiffResult<T> compareByIds(List<T> existingList, List<T> newList, Function<T, String> idExtractor) {
        ListDiffResult<T> result = new ListDiffResult<>();
        
        if (CollectionUtils.isEmpty(newList)) {
            // 新列表为空，全部删除
            result.setToDelete(new ArrayList<>(existingList));
            return result;
        }
        
        if (CollectionUtils.isEmpty(existingList)) {
            // 现有列表为空，全部新增
            result.setToAdd(new ArrayList<>(newList));
            return result;
        }
        
        // 构建现有数据ID集合
        List<String> existingIds = existingList.stream()
            .map(idExtractor)
            .filter(id -> id != null)
            .collect(Collectors.toList());
        
        // 分类处理
        for (T newItem : newList) {
            String newId = idExtractor.apply(newItem);
            if (newId == null || newId.isEmpty()) {
                // 没有ID，新增
                result.getToAdd().add(newItem);
            } else if (existingIds.contains(newId)) {
                // 有ID且存在，更新
                result.getToUpdate().add(newItem);
            } else {
                // 有ID但不存在，新增
                result.getToAdd().add(newItem);
            }
        }
        
        // 查找需要删除的项目
        List<String> newIds = newList.stream()
            .map(idExtractor)
            .filter(id -> id != null && !id.isEmpty())
            .collect(Collectors.toList());
        
        for (T existingItem : existingList) {
            String existingId = idExtractor.apply(existingItem);
            if (existingId != null && !newIds.contains(existingId)) {
                result.getToDelete().add(existingItem);
            }
        }
        
        return result;
    }

    /**
     * 差异对比结果封装
     */
    public static class ListDiffResult<T> {
        private List<T> toAdd = new ArrayList<>();
        private List<T> toUpdate = new ArrayList<>();  
        private List<T> toDelete = new ArrayList<>();
        
        public List<T> getToAdd() { return toAdd; }
        public void setToAdd(List<T> toAdd) { this.toAdd = toAdd; }
        
        public List<T> getToUpdate() { return toUpdate; }
        public void setToUpdate(List<T> toUpdate) { this.toUpdate = toUpdate; }
        
        public List<T> getToDelete() { return toDelete; }
        public void setToDelete(List<T> toDelete) { this.toDelete = toDelete; }
    }
}