package com.ruoyi.route.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.route.domain.RouteMain;

/**
 * 航线主线Service接口
 */
public interface IRouteMainService extends IService<RouteMain> {

    /**
     * 分页查询航线主线
     */
    public Page<RouteMain> selectRouteMainPage(int pageNumber, int pageSize, RouteMain routeMain);

    /**
     * 判断主线名称是否已存在（全局唯一，排除自身）
     */
    boolean existsMainName(String mainName, String excludeId);
} 