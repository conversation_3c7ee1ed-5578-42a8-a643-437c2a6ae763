package com.ruoyi.booking.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.booking.domain.LogisticWaterway;
import com.ruoyi.booking.service.ILogisticWaterwayService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/booking/logisticWaterway")
public class LogisticWaterwayController extends BaseController {

    @Autowired
    private ILogisticWaterwayService logisticWaterwayService;

    /**
     * 新增物流水运明细
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:add')")
    @Log(title = "物流水运明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogisticWaterway logisticWaterway) {
        log.debug("新增物流水运明细 - 数据: {}", logisticWaterway);
        return toAjax(logisticWaterwayService.save(logisticWaterway));
    }

    /**
     * 修改物流水运明细
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:edit')")
    @Log(title = "物流水运明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogisticWaterway logisticWaterway) {
        log.debug("修改物流水运明细 - 数据: {}", logisticWaterway);
        return toAjax(logisticWaterwayService.updateById(logisticWaterway));
    }

    /**
     * 拆分物流水运明细
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:split')")
    @Log(title = "物流水运明细", businessType = BusinessType.UPDATE)
    @PostMapping("/split")
    public AjaxResult split(@RequestBody JSONObject request) {
        String id = request.getString("id");
        JSONArray splitRecords = request.getJSONArray("splitRecords");

        log.debug("拆分物流水运明细 - ID: {}, 拆分记录数: {}", id, splitRecords.size());
        AjaxResult ajaxResult = toAjax(logisticWaterwayService.splitLogisticWaterway(id, splitRecords));
        ajaxResult.put("success", "true");
        return ajaxResult;
    }

    /**
     * 合并物流水运明细
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:merge')")
    @Log(title = "物流水运明细", businessType = BusinessType.UPDATE)
    @PostMapping("/merge")
    public AjaxResult merge(@RequestBody List<String> ids) {
        log.debug("合并物流水运明细 - 需要合并的ID数量: {}", ids.size());
        AjaxResult ajaxResult = toAjax(logisticWaterwayService.mergeLogisticWaterway(ids));
        ajaxResult.put("success", "true");
        return ajaxResult;
    }

    /**
     * 删除物流水运明细
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:remove')")
    @Log(title = "物流水运明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        log.debug("删除物流水运明细 - ID数组: {}", (Object) ids);
        return toAjax(logisticWaterwayService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 分页查询物流水运明细
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:list')")
    @GetMapping("/list")
    public TableDataInfo page(@RequestParam(defaultValue = "1") int pageNum,
                              @RequestParam(defaultValue = "10") int pageSize,
                              LogisticWaterway logisticWaterway,
                              String searchValue) {
        log.debug("分页查询物流水运明细 - 页码: {}, 每页数量: {}, 查询条件: {}, 搜索值: {}",
                pageNum, pageSize, logisticWaterway, searchValue);
        Page<LogisticWaterway> page = logisticWaterwayService.selectLogisticWaterwayPage(
                pageNum, pageSize, logisticWaterway, searchValue);
        TableDataInfo dataTable = getDataTable(page.getRecords());
        dataTable.setTotal(page.getTotalRow());
        log.debug("分页查询物流水运明细完成 - 总记录数: {}", page.getTotalRow());
        return dataTable;
    }

    /**
     * 导出物流水运明细列表
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:export')")
    @Log(title = "物流水运明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                       LogisticWaterway logisticWaterway,
                       String searchValue) {
        log.debug("导出物流水运明细数据 - 查询条件: {}, 搜索值: {}", logisticWaterway, searchValue);
        List<LogisticWaterway> list = logisticWaterwayService.selectLogisticWaterwayPage(
                1, Integer.MAX_VALUE, logisticWaterway, searchValue).getRecords();
        log.debug("导出物流水运明细数据 - 导出记录数: {}", list.size());
        ExcelUtil<LogisticWaterway> util = new ExcelUtil<>(LogisticWaterway.class);
        util.exportExcel(response, list, "物流水运明细数据");
        log.debug("导出物流水运明细数据完成");
    }

    /**
     * 获取物流水运明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('booking:logisticWaterway:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        log.debug("获取物流水运明细详细信息 - ID: {}", id);
        return AjaxResult.success(logisticWaterwayService.getById(id));
    }
}