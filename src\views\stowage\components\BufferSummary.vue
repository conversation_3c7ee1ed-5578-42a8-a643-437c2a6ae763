<template>
  <div class="buffer-summary">
    <div class="summary-overview">
      <h4>整体统计</h4>
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-title">总货物数</div>
          <div class="card-value">{{ stowageStore.bufferTotalItems }}</div>
        </div>
        <div class="overview-card">
          <div class="card-title">总TEU</div>
          <div class="card-value">{{ stowageStore.bufferTotalTEU }}</div>
        </div>
        <div class="overview-card">
          <div class="card-title">涉及客户</div>
          <div class="card-value">{{ uniqueCustomers.length }}</div>
        </div>
        <div class="overview-card">
          <div class="card-title">涉及航线</div>
          <div class="card-value">{{ uniqueRoutes.length }}</div>
        </div>
      </div>
    </div>

    <div class="buffer-details">
      <div 
        v-for="buffer in stowageStore.bufferZones" 
        :key="buffer.id"
        class="buffer-section"
        :style="{ borderColor: buffer.borderColor }"
      >
        <div class="buffer-header">
          <h4>{{ buffer.name }}</h4>
          <div class="buffer-actions">
            <el-button 
              size="small" 
              type="danger" 
              @click="clearBuffer(buffer.id)"
              :disabled="buffer.items.length === 0"
            >
              清空
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="exportBuffer(buffer.id)"
              :disabled="buffer.items.length === 0"
            >
              导出
            </el-button>
          </div>
        </div>

        <div v-if="buffer.items.length === 0" class="empty-buffer">
          <el-empty description="缓冲区为空" :image-size="60" />
        </div>

        <div v-else class="buffer-content">
          <!-- 缓冲区统计 -->
          <div class="buffer-stats">
            <div class="stat-grid">
              <div class="stat-item">
                <span class="stat-label">货物数量:</span>
                <span class="stat-value">{{ buffer.items.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">总TEU:</span>
                <span class="stat-value">{{ calculateBufferTEU(buffer) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">20尺空:</span>
                <span class="stat-value">{{ buffer.statistics.containers['20Empty'] }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">20尺重:</span>
                <span class="stat-value">{{ buffer.statistics.containers['20Full'] }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">40尺空:</span>
                <span class="stat-value">{{ buffer.statistics.containers['40Empty'] }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">40尺重:</span>
                <span class="stat-value">{{ buffer.statistics.containers['40Full'] }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">其他箱:</span>
                <span class="stat-value">{{ buffer.statistics.containers['other'] }}</span>
              </div>
            </div>
          </div>

          <!-- 港口分布 -->
          <div class="port-distribution">
            <div class="port-section">
              <h5>起运港分布</h5>
              <div class="port-tags">
                <el-tag 
                  v-for="port in Array.from(buffer.statistics.loadPorts)" 
                  :key="port"
                  size="small"
                  type="info"
                >
                  {{ port }}
                </el-tag>
              </div>
            </div>
            <div class="port-section">
              <h5>目的港分布</h5>
              <div class="port-tags">
                <el-tag 
                  v-for="port in Array.from(buffer.statistics.unloadPorts)" 
                  :key="port"
                  size="small"
                  type="warning"
                >
                  {{ port }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 货物明细表 -->
          <div class="cargo-details">
            <h5>货物明细</h5>
            <el-table :data="buffer.items" size="small" max-height="300">
              <el-table-column prop="bookingNo" label="订舱号" width="120" />
              <el-table-column prop="customer" label="客户" width="150" />
              <el-table-column prop="loadPort" label="起运港" width="100" />
              <el-table-column prop="unloadPort" label="目的港" width="100" />
              <el-table-column label="TEU" width="60" align="center">
                <template #default="{ row }">
                  {{ calculateTEU(row.containers) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template #default="{ row }">
                  <el-button 
                    size="small" 
                    type="text" 
                    @click="removeFromBuffer(buffer.id, row.id)"
                    style="color: #f56565;"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="batch-operations" v-if="stowageStore.bufferTotalItems > 0">
      <h4>批量操作</h4>
      <div class="batch-buttons">
        <el-button type="warning" @click="clearAllBuffers">
          清空所有缓冲区
        </el-button>
        <el-button type="primary" @click="exportAllBuffers">
          导出所有缓冲区
        </el-button>
        <el-button type="success" @click="optimizeBuffers">
          优化分配
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useStowageStore } from '@/stores/stowage'
import { calculateTEU } from '../mockData.js'

const stowageStore = useStowageStore()

// 计算唯一客户
const uniqueCustomers = computed(() => {
  const customers = new Set()
  stowageStore.bufferZones.forEach(buffer => {
    buffer.items.forEach(item => {
      customers.add(item.customer)
    })
  })
  return Array.from(customers)
})

// 计算唯一航线
const uniqueRoutes = computed(() => {
  const routes = new Set()
  stowageStore.bufferZones.forEach(buffer => {
    buffer.items.forEach(item => {
      routes.add(`${item.loadPort} → ${item.unloadPort}`)
    })
  })
  return Array.from(routes)
})

// 计算缓冲区TEU
const calculateBufferTEU = (buffer) => {
  return buffer.items.reduce((total, item) => {
    return total + calculateTEU(item.containers)
  }, 0)
}

// 从缓冲区移除
const removeFromBuffer = (bufferId, itemId) => {
  if (stowageStore.removeFromBuffer(itemId, bufferId)) {
    ElMessage.success('已移除货物')
  }
}

// 清空单个缓冲区
const clearBuffer = (bufferId) => {
  const buffer = stowageStore.bufferZones.find(b => b.id === bufferId)
  if (!buffer) return

  ElMessageBox.confirm(
    `确定要清空${buffer.name}吗？这将移除所有 ${buffer.items.length} 项货物。`,
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    if (stowageStore.clearBuffer(bufferId)) {
      ElMessage.success(`${buffer.name}已清空`)
    }
  }).catch(() => {
    // 用户取消
  })
}

// 清空所有缓冲区
const clearAllBuffers = () => {
  ElMessageBox.confirm(
    `确定要清空所有缓冲区吗？这将移除总共 ${stowageStore.bufferTotalItems} 项货物。`,
    '确认清空全部',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    let successCount = 0
    stowageStore.bufferZones.forEach(buffer => {
      if (buffer.items.length > 0 && stowageStore.clearBuffer(buffer.id)) {
        successCount++
      }
    })
    ElMessage.success(`已清空 ${successCount} 个缓冲区`)
  }).catch(() => {
    // 用户取消
  })
}

// 导出缓冲区
const exportBuffer = (bufferId) => {
  const buffer = stowageStore.bufferZones.find(b => b.id === bufferId)
  if (!buffer || buffer.items.length === 0) return

  // 创建CSV数据
  const csvData = [
    ['订舱号', '客户', '起运港', '目的港', '20尺空', '20尺重', '40尺空', '40尺重', '其他箱', 'TEU', '备注'],
    ...buffer.items.map(item => [
      item.bookingNo,
      item.customer,
      item.loadPort,
      item.unloadPort,
      item.containers['20Empty'],
      item.containers['20Full'],
      item.containers['40Empty'],
      item.containers['40Full'],
      item.containers['other'],
      calculateTEU(item.containers),
      item.remark
    ])
  ]

  // 转换为CSV格式
  const csvContent = csvData.map(row => row.join(',')).join('\n')
  
  // 创建下载链接
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${buffer.name}_${new Date().toISOString().slice(0, 10)}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success(`${buffer.name}数据已导出`)
}

// 导出所有缓冲区
const exportAllBuffers = () => {
  stowageStore.bufferZones.forEach(buffer => {
    if (buffer.items.length > 0) {
      exportBuffer(buffer.id)
    }
  })
}

// 优化缓冲区分配
const optimizeBuffers = () => {
  ElMessage.info('优化分配功能开发中...')
  // TODO: 实现基于航线、客户等的自动优化分配逻辑
}
</script>

<style lang="scss" scoped>
.buffer-summary {
  padding: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.summary-overview {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
  }

  .overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;

    .overview-card {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      border: 1px solid #e9ecef;

      .card-title {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }

      .card-value {
        font-size: 24px;
        font-weight: 600;
        color: #333;
      }
    }
  }
}

.buffer-details {
  .buffer-section {
    margin-bottom: 24px;
    border: 2px solid;
    border-radius: 8px;
    overflow: hidden;

    .buffer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: rgba(0, 0, 0, 0.02);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      h4 {
        margin: 0;
        color: #333;
        font-size: 14px;
        font-weight: 600;
      }

      .buffer-actions {
        display: flex;
        gap: 8px;
      }
    }

    .empty-buffer {
      padding: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .buffer-content {
      padding: 20px;

      .buffer-stats {
        margin-bottom: 20px;

        .stat-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 12px;

          .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;

            .stat-label {
              font-size: 12px;
              color: #666;
            }

            .stat-value {
              font-weight: 600;
              color: #333;
            }
          }
        }
      }

      .port-distribution {
        margin-bottom: 20px;

        .port-section {
          margin-bottom: 12px;

          h5 {
            margin: 0 0 8px 0;
            font-size: 13px;
            color: #333;
            font-weight: 500;
          }

          .port-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
          }
        }
      }

      .cargo-details {
        h5 {
          margin: 0 0 12px 0;
          font-size: 13px;
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
}

.batch-operations {
  margin-top: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
  }

  .batch-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

// 滚动条样式
.buffer-summary::-webkit-scrollbar {
  width: 6px;
}

.buffer-summary::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.buffer-summary::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.buffer-summary::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>