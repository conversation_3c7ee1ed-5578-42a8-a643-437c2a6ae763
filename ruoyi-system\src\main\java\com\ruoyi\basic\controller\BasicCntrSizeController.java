package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCntrSize;
import com.ruoyi.basic.service.IBasicCntrSizeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 箱尺寸Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/system/cntrSize")
public class BasicCntrSizeController extends BaseController {

    @Autowired
    private IBasicCntrSizeService basicCntrSizeService;

    /**
     * 查询箱尺寸列表
     */
    @PreAuthorize("@ss.hasPermi('system:size:list')")
    @GetMapping("/list")
    public Page<BasicCntrSize> list(BasicCntrSize basicCntrSize)
    {
        // startPage();
        // List<BasicCntrSize> list = basicCntrSizeService.selectBasicCntrSizeList(basicCntrSize);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create()
        .like(BasicCntrSize::getSizeCode, basicCntrSize.getSizeCode())
        .like(BasicCntrSize::getSizeName, basicCntrSize.getSizeName())
        .orderBy(BasicCntrSize::getCreateTime, true);
        return basicCntrSizeService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出箱尺寸列表
     */
    @PreAuthorize("@ss.hasPermi('system:size:export')")
    @Log(title = "箱尺寸", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicCntrSize basicCntrSize)
    {
        List<BasicCntrSize> list = basicCntrSizeService.selectBasicCntrSizeList(basicCntrSize);
        ExcelUtil<BasicCntrSize> util = new ExcelUtil<BasicCntrSize>(BasicCntrSize.class);
        util.exportExcel(response, list, "箱尺寸数据");
    }

    /**
     * 获取箱尺寸详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:size:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicCntrSizeService.selectBasicCntrSizeById(id));
    }

    /**
     * 新增箱尺寸
     */
    @PreAuthorize("@ss.hasPermi('system:size:add')")
    @Log(title = "箱尺寸", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicCntrSize basicCntrSize)
    {
        return toAjax(basicCntrSizeService.insertBasicCntrSize(basicCntrSize));
    }

    /**
     * 修改箱尺寸
     */
    @PreAuthorize("@ss.hasPermi('system:size:edit')")
    @Log(title = "箱尺寸", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicCntrSize basicCntrSize)
    {
        return toAjax(basicCntrSizeService.updateBasicCntrSize(basicCntrSize));
    }

    /**
     * 删除箱尺寸
     */
    @PreAuthorize("@ss.hasPermi('system:size:remove')")
    @Log(title = "箱尺寸", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicCntrSizeService.deleteBasicCntrSizeByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('system:size:list')")
    @GetMapping("/label")
    public AjaxResult label(){
        return AjaxResult.success(basicCntrSizeService.list(QueryWrapper.create().orderBy(BasicCntrSize::getCreateTime, true)));
    }

}
