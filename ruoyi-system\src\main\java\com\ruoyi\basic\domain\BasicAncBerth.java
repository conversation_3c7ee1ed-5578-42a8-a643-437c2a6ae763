package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_anc_berth")
public class BasicAncBerth extends BaseEntity {
    /** 锚位ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 关联码头ID */
//    @Excel(name = "关联码头ID")
    private String terminalId;

    @Column(ignore = true)
    private List<String> terminalIds;

    /** 锚位编号 */
    @Excel(name = "锚位编号")
    private String anbCode;

    /** 锚位名称 */
    @Excel(name = "锚位名称")
    private String anbCname;

    /** 锚位吨级 */
    @Excel(name = "锚位吨级")
    @Column(jdbcType = JdbcType.NUMERIC)
    private BigDecimal anbTon;

    /** 设计水深 */
    @Excel(name = "设计水深")
    @Column(jdbcType = JdbcType.NUMERIC)
    private BigDecimal anbDepth;

    /** 锚位长度（米) */
    @Excel(name = "锚位长度", readConverterExp = "锚位长度（米)")
    @Column(jdbcType = JdbcType.NUMERIC)
    private BigDecimal anbLength;

    /** 中心位置 */
    @Excel(name = "中心位置")
    private String anbCenter;

    /** 底质 */
    @Excel(name = "底质")
    private String anbMaterial;

    /** 功能 */
    @Excel(name = "功能")
    private String anbFunction;

    /** 乐观锁 */
    @Excel(name = "乐观锁")
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;
}
