package com.ruoyi.customer.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.customer.domain.CusHistory;
import com.ruoyi.customer.domain.CusMain;

/**
 * <AUTHOR>
 * @description 表CUS_HISTORY(客户变更历史表)的Service
 * @createDate 2025-06-18 10:26:15
 */
public interface CusHistoryService extends IService<CusHistory> {

    // 查询客户变更历史列表
    Page<CusHistory> selectCusHistoryList(String cusId, Integer pageNum, Integer pageSize);

    // 查询客户变更历史详情
    AjaxResult getCusHistoryById(String id);

    // 保存客户变更历史
    void saveCusHistory(CusMain cusMain);
}
