package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicMileage;

import java.util.List;

public interface IBasicMileageService extends IService<BasicMileage> {
    /**
     * 查询码头里程
     *
     * @param id 码头里程主键
     * @return 码头里程
     */
    public BasicMileage selectBasicMileageById(String id);

    /**
     * 查询码头里程列表
     *
     * @param basicMileage 码头里程
     * @return 码头里程集合
     */
    public List<BasicMileage> selectBasicMileageList(BasicMileage basicMileage);

    /**
     * 新增码头里程
     *
     * @param basicMileage 码头里程
     * @return 结果
     */
    public int insertBasicMileage(BasicMileage basicMileage) throws Exception;

    /**
     * 修改码头里程
     *
     * @param basicMileage 码头里程
     * @return 结果
     */
    public int updateBasicMileage(BasicMileage basicMileage) throws Exception;

    /**
     * 批量删除码头里程
     *
     * @param ids 需要删除的码头里程主键集合
     * @return 结果
     */
    public int deleteBasicMileageByIds(List<String> ids);

    /**
     * 删除码头里程信息
     *
     * @param id 码头里程主键
     * @return 结果
     */
    public int deleteBasicMileageById(String id);
}
