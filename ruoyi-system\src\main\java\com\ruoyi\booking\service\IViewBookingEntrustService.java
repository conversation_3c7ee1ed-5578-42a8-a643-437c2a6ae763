package com.ruoyi.booking.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.booking.domain.ViewBookingEntrust;
import com.ruoyi.booking.domain.vo.BookingEntrustQueryVO;

/**
 * 订舱委托统一视图服务接口
 * 对应数据库视图: v_booking_entrust
 * 用途: 为单证员工作台提供高性能的列表查询服务
 * 
 * 注意: 视图只支持查询操作，不支持增删改操作
 */
public interface IViewBookingEntrustService extends IService<ViewBookingEntrust> {
    
    /**
     * 分页查询订舱委托视图数据
     * 支持多条件动态查询和排序
     * 
     * @param pageNumber 页码
     * @param pageSize   每页大小
     * @param queryVO    查询条件
     * @return 分页结果
     */
    Page<ViewBookingEntrust> selectViewEntrustPage(int pageNumber, int pageSize, BookingEntrustQueryVO queryVO);
}