// 船舶配载界面模拟数据

// 待运输货物数据
export function mockPendingCargo() {
  return [
    {
      id: 'CARGO001',
      bookingNo: 'BK2025001',
      year: 2025,
      month: 1,
      day: 15,
      loadPort: '南沙一期',
      unloadPort: '黄埔老港',
      customer: '泉州安通物流',
      tradeType: '内贸',
      project: '木材',
      route: '黄埔线',
      containers: {
        '20Empty': 2,
        '20Full': 4,
        '40Empty': 1,
        '40Full': 3,
        'other': 0
      },
      remark: '赶船！15号船期',
      status: 'pending', // pending, split, allocated
      bufferZone: null, // 1, 2, 3
      children: [] // 拆解后的子项
    },
    {
      id: 'CARGO002',
      bookingNo: 'BK2025002',
      year: 2025,
      month: 1,
      day: 16,
      loadPort: '莞睿码头',
      unloadPort: '南沙二期',
      customer: '广州德正国际供应链',
      tradeType: '外贸',
      project: '废纸',
      route: '东莞线',
      containers: {
        '20Empty': 0,
        '20Full': 8,
        '40Empty': 24,
        '40Full': 16,
        'other': 2
      },
      remark: '1*40DG 9/3268',
      status: 'pending',
      bufferZone: null,
      children: []
    },
    {
      id: 'CARGO003',
      bookingNo: 'BK2025003',
      year: 2025,
      month: 1,
      day: 17,
      loadPort: '黄埔老港',
      unloadPort: '集司码头',
      customer: '珠海港物流',
      tradeType: '内贸',
      project: '电子产品',
      route: '珠海线',
      containers: {
        '20Empty': 5,
        '20Full': 15,
        '40Empty': 8,
        '40Full': 12,
        'other': 1
      },
      remark: '10号柜期，需要优先处理',
      status: 'pending',
      bufferZone: null,
      children: []
    },
    {
      id: 'CARGO004',
      bookingNo: 'BK2025004',
      year: 2025,
      month: 1,
      day: 18,
      loadPort: '钦州港',
      unloadPort: '南沙一期',
      customer: '招商港口',
      tradeType: '内贸',
      project: '钢材',
      route: '钦州线',
      containers: {
        '20Empty': 10,
        '20Full': 20,
        '40Empty': 15,
        '40Full': 25,
        'other': 5
      },
      remark: '冷冻柜需要确认温度要求',
      status: 'pending',
      bufferZone: null,
      children: []
    },
    {
      id: 'CARGO005',
      bookingNo: 'BK2025005',
      year: 2025,
      month: 1,
      day: 19,
      loadPort: '南沙二期',
      unloadPort: '九江码头',
      customer: '华南城物流',
      tradeType: '外贸',
      project: '建材',
      route: '佛山线',
      containers: {
        '20Empty': 6,
        '20Full': 18,
        '40Empty': 12,
        '40Full': 22,
        'other': 3
      },
      remark: '无特殊要求',
      status: 'pending',
      bufferZone: null,
      children: []
    },
    {
      id: 'CARGO006',
      bookingNo: 'BK2025006',
      year: 2025,
      month: 1,
      day: 20,
      loadPort: '理文码头',
      unloadPort: '三水码头',
      customer: '中远海运',
      tradeType: '内贸',
      project: '化工原料',
      route: '佛山线',
      containers: {
        '20Empty': 3,
        '20Full': 7,
        '40Empty': 5,
        '40Full': 9,
        'other': 1
      },
      remark: 'DG货物，需要特殊处理',
      status: 'pending',
      bufferZone: null,
      children: []
    }
  ]
}

// 缓冲区数据
export function mockBufferZones() {
  return [
    {
      id: 1,
      name: '缓冲区1',
      color: '#ffebee', // 浅红色
      borderColor: '#f44336',
      items: [],
      statistics: {
        totalItems: 0,
        containers: {
          '20Empty': 0,
          '20Full': 0,
          '40Empty': 0,
          '40Full': 0,
          'other': 0
        },
        loadPorts: new Set(),
        unloadPorts: new Set(),
        loadingTerminals: new Set(),
        unloadingTerminals: new Set()
      }
    },
    {
      id: 2,
      name: '缓冲区2',
      color: '#e8f5e8', // 浅绿色
      borderColor: '#4caf50',
      items: [],
      statistics: {
        totalItems: 0,
        containers: {
          '20Empty': 0,
          '20Full': 0,
          '40Empty': 0,
          '40Full': 0,
          'other': 0
        },
        loadPorts: new Set(),
        unloadPorts: new Set(),
        loadingTerminals: new Set(),
        unloadingTerminals: new Set()
      }
    },
    {
      id: 3,
      name: '缓冲区3',
      color: '#e3f2fd', // 浅蓝色
      borderColor: '#2196f3',
      items: [],
      statistics: {
        totalItems: 0,
        containers: {
          '20Empty': 0,
          '20Full': 0,
          '40Empty': 0,
          '40Full': 0,
          'other': 0
        },
        loadPorts: new Set(),
        unloadPorts: new Set(),
        loadingTerminals: new Set(),
        unloadingTerminals: new Set()
      }
    }
  ]
}

// 船舶清单数据
export function mockVesselManifest() {
  const vessels = []
  const vesselPrefixes = ['飞弘', '穗德洋', '诚祥', '粤翔', '海翔', '华晟', '中海', '中远', '招商', '长荣', '马士基', '地中海', '达飞', '赫伯罗特', 'ONE', '阳明', '万海', '现代', '韩进', '太平']
  const vesselSuffixes = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110', '111', '112', '113', '114', '115', '116', '117', '118', '119', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129', '130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '140', '141', '142', '143', '144', '145', '146', '147', '148', '149', '150', '151', '152', '153', '154', '155', '156', '157', '158', '159', '160', '161', '162', '163', '164', '165', '166', '167', '168', '169', '170', '171', '172', '173', '174', '175', '176', '177', '178', '179', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189', '190', '191', '192', '193', '194', '195', '196', '197', '198', '199', '200']
  
  const ports = ['广州港', '深圳港', '上海港', '宁波港', '青岛港', '天津港', '大连港', '厦门港', '福州港', '温州港', '连云港', '日照港', '烟台港', '威海港', '营口港', '锦州港', '秦皇岛港', '唐山港', '黄骅港', '天津港', '青岛港', '烟台港', '威海港', '日照港', '连云港', '上海港', '宁波港', '舟山港', '台州港', '温州港', '福州港', '厦门港', '泉州港', '汕头港', '广州港', '深圳港', '珠海港', '湛江港', '北海港', '防城港', '海口港']
  
  const statuses = ['loading', 'completed', 'planned', 'sailing']
  
  for (let i = 1; i <= 200; i++) {
    const prefix = vesselPrefixes[Math.floor(Math.random() * vesselPrefixes.length)]
    const suffix = vesselSuffixes[i - 1]
    const vesselName = `${prefix}${suffix}`
    
    // 生成船舶总容量
    const totalCapacity = {
      '20Empty': Math.floor(Math.random() * 100) + 20,
      '20Full': Math.floor(Math.random() * 150) + 30,
      '40Empty': Math.floor(Math.random() * 60) + 15,
      '40Full': Math.floor(Math.random() * 120) + 25,
      'other': Math.floor(Math.random() * 20) + 5
    }
    
    // 生成航次数量（0-3个）
    const voyageCount = Math.floor(Math.random() * 4)
    const voyages = []
    
    for (let j = 0; j < voyageCount; j++) {
      const loadPort = ports[Math.floor(Math.random() * ports.length)]
      const unloadPort = ports[Math.floor(Math.random() * ports.length)]
      
      // 生成航次集装箱数量（不超过总容量）
      const containers = {
        '20Empty': Math.min(Math.floor(Math.random() * totalCapacity['20Empty']), totalCapacity['20Empty']),
        '20Full': Math.min(Math.floor(Math.random() * totalCapacity['20Full']), totalCapacity['20Full']),
        '40Empty': Math.min(Math.floor(Math.random() * totalCapacity['40Empty']), totalCapacity['40Empty']),
        '40Full': Math.min(Math.floor(Math.random() * totalCapacity['40Full']), totalCapacity['40Full']),
        'other': Math.min(Math.floor(Math.random() * totalCapacity['other']), totalCapacity['other'])
      }
      
      // 生成预计抵港时间（未来30天内）
      const estimatedArrival = new Date()
      estimatedArrival.setDate(estimatedArrival.getDate() + Math.floor(Math.random() * 30) + 1)
      
      voyages.push({
        id: `VOYAGE${String(i).padStart(3, '0')}${String(j + 1).padStart(2, '0')}`,
        voyageNo: generateVoyageNo(),
        loadPort: loadPort,
        unloadPort: unloadPort,
        estimatedArrival: estimatedArrival.toISOString().split('T')[0],
        containers: containers,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        bufferSource: Math.random() > 0.7 ? Math.floor(Math.random() * 3) + 1 : null,
        cargoItems: []
      })
    }
    
    vessels.push({
      id: `VESSEL${String(i).padStart(3, '0')}`,
      vesselName: vesselName,
      voyages: voyages,
      totalCapacity: totalCapacity
    })
  }
  
  return vessels
}

// 集装箱类型配置
export const containerTypes = {
  '20Empty': { label: '20尺空', teu: 1 },
  '20Full': { label: '20尺重', teu: 1 },
  '40Empty': { label: '40尺空', teu: 2 },
  '40Full': { label: '40尺重', teu: 2 },
  'other': { label: '其他箱', teu: 1 }
}

// 计算TEU总数
export function calculateTEU(containers) {
  let totalTEU = 0
  Object.keys(containers).forEach(type => {
    totalTEU += containers[type] * containerTypes[type].teu
  })
  return totalTEU
}

// 生成航次号
export function generateVoyageNo() {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `${year}${month}${day}${random}`
}

// 拆解货物项
export function splitCargoItem(item, splitData) {
  const children = []
  let childId = 1
  
  Object.keys(splitData).forEach(containerType => {
    const quantities = splitData[containerType]
    quantities.forEach(quantity => {
      if (quantity > 0) {
        const child = {
          id: `${item.id}_${childId++}`,
          parentId: item.id,
          bookingNo: `${item.bookingNo}-${childId}`,
          year: item.year,
          month: item.month,
          day: item.day,
          loadPort: item.loadPort,
          unloadPort: item.unloadPort,
          customer: item.customer,
          tradeType: item.tradeType,
          project: item.project,
          route: item.route,
          containers: {
            '20Empty': containerType === '20Empty' ? quantity : 0,
            '20Full': containerType === '20Full' ? quantity : 0,
            '40Empty': containerType === '40Empty' ? quantity : 0,
            '40Full': containerType === '40Full' ? quantity : 0,
            'other': containerType === 'other' ? quantity : 0
          },
          remark: `${item.remark} - 拆解项${childId}`,
          status: 'pending',
          bufferZone: null,
          children: []
        }
        children.push(child)
      }
    })
  })
  
  return children
}

// 更新缓冲区统计信息
export function updateBufferStatistics(buffer) {
  const statistics = {
    totalItems: buffer.items.length,
    containers: {
      '20Empty': 0,
      '20Full': 0,
      '40Empty': 0,
      '40Full': 0,
      'other': 0
    },
    loadPorts: new Set(),
    unloadPorts: new Set(),
    loadingTerminals: new Set(),
    unloadingTerminals: new Set()
  }
  
  buffer.items.forEach(item => {
    Object.keys(item.containers).forEach(type => {
      statistics.containers[type] += item.containers[type]
    })
    statistics.loadPorts.add(item.loadPort)
    statistics.unloadPorts.add(item.unloadPort)
    statistics.loadingTerminals.add(item.loadingTerminal)
    statistics.unloadingTerminals.add(item.unloadingTerminal)
  })
  
  return statistics
}