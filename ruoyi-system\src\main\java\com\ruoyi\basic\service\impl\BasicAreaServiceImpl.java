package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicArea;
import com.ruoyi.basic.domain.BasicPort;
import com.ruoyi.basic.mapper.BasicAreaMapper;
import com.ruoyi.basic.service.IBasicAreaService;
import com.ruoyi.basic.service.IBasicPortService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicAreaTableDef.BASIC_AREA;
import static com.ruoyi.basic.domain.table.BasicPortTableDef.BASIC_PORT;

@Service
public class BasicAreaServiceImpl extends ServiceImpl<BasicAreaMapper, BasicArea> implements IBasicAreaService {

    @Autowired
    IBasicPortService portService;

    /**
     * 查询区域管理
     *
     * @param id 区域管理主键
     * @return 区域管理
     */
    @Override
    public BasicArea selectBasicAreaById(String id)
    {
        return super.getById(id);
    }

    /**
     * 查询区域管理列表
     *
     * @param basicArea 区域管理
     * @return 区域管理
     */
    @Override
    public List<BasicArea> selectBasicAreaList(BasicArea basicArea)
    {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_AREA.COU_ID.eq(basicArea.getCouId(), StringUtils.isNotEmpty(basicArea.getCouId())))
                .and(BASIC_AREA.AREA_CODE.like(basicArea.getAreaCode(), StringUtils.isNotEmpty(basicArea.getAreaCode())))
                .and(BASIC_AREA.AREA_NAME.like(basicArea.getAreaName(), StringUtils.isNotEmpty(basicArea.getAreaName())))
                .orderBy(BASIC_AREA.CREATE_TIME.asc());

        return super.list(queryWrapper);
    }

    /**
     * 新增区域管理
     *
     * @param basicArea 区域管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBasicArea(BasicArea basicArea) throws Exception {
        checkVaild(basicArea);
        checkUnique(basicArea);
        return super.save(basicArea) ? 1 : 0;
    }

    /**
     * 修改区域管理
     *
     * @param basicArea 区域管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBasicArea(BasicArea basicArea) throws Exception {
        checkVaild(basicArea);
        checkUnique(basicArea);
        return super.updateById(basicArea) ? 1 : 0;
    }

    /**
     * 批量删除区域管理
     *
     * @param ids 需要删除的区域管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBasicAreaByIds(List<String> ids) throws Exception {
        checkUseByIds(ids);
        return super.removeByIds(ids) ? 1 : 0;
    }

    public void checkUseByIds(List<String> ids) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_PORT.AREA_ID.in(ids));

        List<BasicPort> checkUse = portService.list(queryWrapper);

        if(!checkUse.isEmpty()){
            throw new Exception("选择区域正在被使用");
        }
    }

    /**
     * 删除区域管理信息
     *
     * @param id 区域管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBasicAreaById(String id)
    {
        return super.removeById(id) ? 1 : 0;
    }

    public void checkVaild(BasicArea basicArea) throws Exception {
        if(StringUtils.isEmpty(basicArea.getCouId())){
            throw new Exception("请选择国家/地区");
        }

        if(StringUtils.isEmpty(basicArea.getAreaCode())){
            throw new Exception("区域代码不能为空");
        }

        if(StringUtils.isEmpty(basicArea.getAreaName())){
            throw new Exception("区域名称不能为空");
        }
    }

    public void checkUnique(BasicArea basicArea) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_AREA.ID.ne(basicArea.getId(), StringUtils.isNotEmpty(basicArea.getId())))
                .and(BASIC_AREA.COU_ID.eq(basicArea.getCouId()))
                .and(BASIC_AREA.AREA_CODE.eq(basicArea.getAreaCode()));

        List<BasicArea> checkCode = super.list(queryWrapper);

        if(!checkCode.isEmpty()){
            throw new Exception("国家/地区 和 区域代码 唯一校验失败");
        }

        queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_AREA.ID.ne(basicArea.getId(), StringUtils.isNotEmpty(basicArea.getId())))
                .and(BASIC_AREA.COU_ID.eq(basicArea.getCouId()))
                .and(BASIC_AREA.AREA_NAME.eq(basicArea.getAreaName()));

        List<BasicArea> checkName = super.list(queryWrapper);

        if(!checkName.isEmpty()){
            throw new Exception("国家/地区 和 区域名称 唯一校验失败");
        }
    }
}
