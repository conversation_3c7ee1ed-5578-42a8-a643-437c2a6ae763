package com.ruoyi.contract.controller;

import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.contract.domain.ContractMain;
import com.ruoyi.contract.service.IContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import com.ruoyi.contract.domain.dto.AutoMatchContractRequest;

@RequestMapping("contract")
@RestController
public class ContractController extends BaseController {

    @Autowired
    private IContractService contractService;

    @PostMapping("page")
    public Page<ContractMain> page(@RequestBody ContractMain contract, Integer pageNum, Integer pageSize) {

        System.out.println(pageNum);
        System.out.println(pageSize);
        System.out.println(contract);

        return contractService.selectPage(contract, pageNum, pageSize);

    }

    @PostMapping("save")
    @PreAuthorize("@ss.hasPermi('contract:save')")
    public AjaxResult save(@RequestBody @Validated ContractMain contract) {

        return contractService.saveContract(contract);

    }

    @PutMapping("update")
    @PreAuthorize("@ss.hasPermi('contract:edit')")
    public AjaxResult edit(@RequestBody @Validated ContractMain contract) {
        try {
            return contractService.updateContract(contract);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    @GetMapping("getOne/{id}")
    public AjaxResult getOne(@PathVariable String id) {

        return contractService.getOneById(id);

    }

    @DeleteMapping("delete")
    @PreAuthorize("@ss.hasPermi('contract:remove')")
    public AjaxResult delete(@RequestBody List<String> conIds) {

        try {
            return contractService.delete(conIds);
        } catch (Exception e) {
            return error(e.getMessage());
        }

    }

    // 修改合同生效状态
    @PutMapping("validity")
    @PreAuthorize("@ss.hasPermi('contract:validity')")
    public AjaxResult validity(@RequestBody List<String> conIds, String isValidity) {
        try {
            return AjaxResult.success(contractService.validity(conIds, isValidity));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    // 根据客户ID获取相关合同
    @GetMapping("byCustomer/{customerId}")
    public AjaxResult getContractsByCustomerId(@PathVariable String customerId) {
        try {
            return contractService.getContractsByCustomerId(customerId);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 自动匹配合同（POST方式，兼容前端JSON参数）
     */
    @PostMapping("autoMatch")
    public AjaxResult autoMatchContractPost(@RequestBody AutoMatchContractRequest req) {
        return contractService.autoMatchContract(
                req.getCustomerId(),
                req.getBookingDate(),
                req.getLoadTerminalId(),
                req.getUnloadTerminalId());
    }

}
