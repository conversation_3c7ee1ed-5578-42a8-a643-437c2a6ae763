package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.activerecord.Model;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Table("basic_danger_un_number")
@Data
public class BasicDangerUnNumber extends BaseEntity {
    /** 联合国编号ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 危险品等级ID */
    @Excel(name = "危险品等级ID")
    private String dangerId;

    /** 联合国编号 */
    @Excel(name = "联合国编号")
    private String unNumber;

    /** 联合国编号名称 */
    @Excel(name = "联合国编号名称")
    private String unUnio;

    /** 注意事项 */
    @Excel(name = "注意事项")
    private String attention;

    /** 隔离要求 */
    @Excel(name = "隔离要求")
    private String isolationRequest;

    /** 运输要求 */
    @Excel(name = "运输要求")
    private String transRequest;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    private String delFlag;
}
