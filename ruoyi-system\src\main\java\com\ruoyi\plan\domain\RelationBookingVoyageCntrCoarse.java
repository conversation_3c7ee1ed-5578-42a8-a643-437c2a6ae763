package com.ruoyi.plan.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@Table("RELATION_BOOKING_VOYAGE_CNTR_COARSE")
public class RelationBookingVoyageCntrCoarse extends BaseEntity {
//    ID
//            RELATION_BOOKING_VOYAGE_MAIN_ID
//    CNTR_SIZE
//            FULL_EMPTY_FLAG
//    QUANTITY
//            DANGER_LEVEL
//    IS_REEFER
//            REMARK
//    CREATE_BY
//            CREATE_TIME
//    UPDATE_BY
//            UPDATE_TIME
//    DEL_FLAG
//            VERSION

    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    private String  relationBookingVoyageMainId;

    private Integer cntrSize;


    private String fullEmptyFlag;//FULL_EMPTY_FLAG

    private Integer quantity;

    private String dangerLevel;

    private String isReefer; //IS_REEFER

    /**
     * 逻辑删除标志
     */
    @Column(isLogicDelete = true)
    private String delFlag;

    /**
     * 乐观锁
     */
    @Column("version")
    private Long version;

    private String bookingCntrCoarseId;
}
