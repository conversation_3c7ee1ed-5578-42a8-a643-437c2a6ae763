/**
 * 单证员工作台V2 - 委托数据类型定义
 * 基于设计文档的统一EntrustDTO结构
 */

// 侧边栏模式类型
export type SidePanelMode = 'add' | 'view' | 'edit';

// 侧边栏状态
export interface SidePanelState {
  visible: boolean;        // 是否显示
  mode: SidePanelMode;    // 当前模式
  entrustId: string | null; // 委托ID（新增时为null）
}

// 委托主表数据
export interface OrderMain {
  id?: string;                    // 新增时为空，查看/编辑时存在
  orderNo?: string;               // 系统生成，新增时为空
  shipperId: string;              // 托运单位ID
  shipperName: string;            // 托运单位名称
  orderDate: string;              // 委托创建日期
  businessType: 'WATERWAY' | 'ROADWAY' | 'AIRWAY';
  tradeType: 'DOMESTIC' | 'FOREIGN';
  consortium: string;             // 所属共同体
  customerAgreement: string;      // 客户协议编号
  settlementMethod: 'MONTHLY' | 'PREPAID' | 'COD';
  totalContainers?: number;       // 统计字段，系统计算
  totalWeight?: number;           // 统计字段，系统计算
  status?: string;                // 系统状态字段
  remark: string;                 // 备注信息
  // 系统字段（查看/编辑时存在）
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  version?: number;               // 乐观锁版本号
}

// 订舱主表数据
export interface BookingMain {
  id?: string;                    // 新增时为空
  orderId?: string;               // 关联委托ID
  bookingNumber?: string;         // 系统生成订舱号
  status?: string;                // 订舱状态
  shipperId: string;
  shipperName: string;
  originPortId: string;           // 起运地ID
  originPortName: string;         // 起运地名称
  destinationPortId: string;      // 目的地ID
  destinationPortName: string;    // 目的地名称
  bookingDate: string;            // 订舱日期
  departureDate?: string;         // 启运日期
  deliveryDate?: string;          // 交货日期
  loadingTerminalId?: string;     // 装货码头ID
  loadingTerminalName?: string;   // 装货码头名称
  unloadingTerminalId?: string;   // 卸货码头ID
  unloadingTerminalName?: string; // 卸货码头名称
  loadingAgentId?: string;        // 装货代理ID
  loadingAgentName?: string;      // 装货代理名称
  unloadingAgentId?: string;      // 卸货代理ID
  unloadingAgentName?: string;    // 卸货代理名称
  tradeType: 'DOMESTIC' | 'FOREIGN';
  transportMode?: string;         // 运输模式
  customsType: 'GENERAL' | 'PROCESSING';
  settlementMethod: 'MONTHLY' | 'PREPAID' | 'COD';
  consortium?: string;
  customerAgreement?: string;
  consignmentSource: 'ONLINE' | 'PHONE' | 'ONSITE' | 'EMAIL' | 'STAFF_ENTRY';
  remark?: string;
  // 系统字段
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  version?: number;
}

// 柜量信息
export interface BookingCntrNum {
  id?: string;                    // 新增时为空
  bookingId?: string;            // 关联订舱ID
  
  // 容器尺寸字段组（关联BASIC_CNTR_SIZE表）
  containerSizeId?: string;      // 尺寸ID，关联BASIC_CNTR_SIZE.id
  containerSizeCode?: string;    // 尺寸代码，如"20", "40"
  containerSizeName?: string;    // 尺寸显示名称，如"20'", "40'"
  
  // 容器类型字段组（关联BASIC_CNTR_TYPE表）
  containerTypeId?: string;      // 箱型ID，关联BASIC_CNTR_TYPE.id
  containerTypeCode?: string;    // 箱型代码，如"GP", "HC"
  containerTypeName?: string;    // 箱型显示名称，如"普通柜GP"
  
  isEmpty: '0' | '1';            // 0重箱 1吉箱
  quantity: number;              // 箱量
  isDangerous: '0' | '1';        // 是否危险品
  
  // 危险品等级字段组（关联BASIC_DANGER表）
  dangerousLevelId?: string;     // 危险品等级ID，如"10140"
  dangerousLevelCode?: string;   // 危险品等级代码，映射dangerLevel字段
  dangerousLevelName?: string;   // 危险品等级名称，映射dangerName字段
  
  isRefrigerated: '0' | '1';     // 是否冷藏
  
  // 货类字段组（关联BASIC_CARGO_TYPE表）
  cargoTypeId?: string;          // 货类ID，关联basic_cargo_type表主键
  cargoTypeCode?: string;        // 货类代码，映射cargoCode字段
  cargoTypeName?: string;        // 货类名称，映射cargoName字段
  
  singleWeight?: number;         // 单箱重量(KG)
  totalWeight?: number;          // 总重(KG)
  isOversize: '0' | '1';         // 是否超限
  oversizeDimensions?: string;   // 超限尺寸
  remark?: string;               // 备注
  // 系统字段
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  version?: number;
}

// 中转港信息
export interface BookingTransitPort {
  id?: string;
  bookingId?: string;
  sequenceNo: number;            // 序号
  transitPortId: string;         // 中转港ID
  transitPortName: string;       // 中转港名称
  agentId?: string;              // 代理ID
  agentName?: string;            // 代理名称
  // 系统字段
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  version?: number;
}

// 物流信息
export interface LogisticsMain {
  id?: string;
  bookingId?: string;
  businessType: 'WATERWAY' | 'ROADWAY' | 'AIRWAY';
  supplierId?: string;           // 供应商ID
  supplierName?: string;         // 供应商名称
  originPortId: string;          // 启运地ID
  originPortName: string;        // 启运地名称
  destinationPortId: string;     // 目的地ID
  destinationPortName: string;   // 目的地名称
  containerDetails?: string;      // 箱量明细（JSON格式）
  termsConditions?: string;       // 条款
  requiredDepartureDate?: string; // 要求启运日期
  requiredArrivalDate?: string;   // 要求到达日期
  // 系统字段
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  version?: number;
}

// 统一的EntrustDTO（完整的委托数据）
export interface EntrustDTO {
  // 委托主表数据
  orderMain: OrderMain;
  // 订舱主表数据
  bookingMain: BookingMain;
  // 柜量信息列表
  bookingCntrNumList: BookingCntrNum[];
  // 中转港信息列表
  bookingTransitPortList: BookingTransitPort[];
  // 物流信息列表
  logisticsMainList: LogisticsMain[];
}

// 表格行数据（VO - 简化的显示数据）
export interface EntrustRowVO {
  id: string;
  customer: string;
  status: string;
  source: 'clerk' | 'customer';
  createdAt: string;
  vesselName?: string;
  voyageNo?: string;
  supplements?: any[];
  mainInfo?: {
    orderNo?: string;
    bookingNo?: string;
    originPort?: string;
    destinationPort?: string;
    loadingTerminal?: string;
    cargo?: string;
    containerType?: string;
    containerQuantity?: number;
    shipDate?: string;
    remark?: string;
  };
}