package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicPort;

import java.util.List;

public interface IBasicPortService extends IService<BasicPort> {
    /**
     * 查询港口管理
     *
     * @param id 港口管理主键
     * @return 港口管理
     */
    public BasicPort selectBasicPortById(String id);

    /**
     * 查询港口管理列表
     *
     * @param basicPort 港口管理
     * @return 港口管理集合
     */
    public List<BasicPort> selectBasicPortList(BasicPort basicPort);

    /**
     * 新增港口管理
     *
     * @param basicPort 港口管理
     * @return 结果
     */
    public int insertBasicPort(BasicPort basicPort) throws Exception;

    /**
     * 修改港口管理
     *
     * @param basicPort 港口管理
     * @return 结果
     */
    public int updateBasicPort(BasicPort basicPort) throws Exception;

    /**
     * 批量删除港口管理
     *
     * @param ids 需要删除的港口管理主键集合
     * @return 结果
     */
    public int deleteBasicPortByIds(List<String> ids) throws Exception;

    /**
     * 删除港口管理信息
     *
     * @param id 港口管理主键
     * @return 结果
     */
    public int deleteBasicPortById(String id);
}
