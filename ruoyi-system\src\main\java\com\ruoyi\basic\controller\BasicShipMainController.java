package com.ruoyi.basic.controller;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicShipMain;
import com.ruoyi.basic.service.IBasicShipMainService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicShipMainTableDef.BASIC_SHIP_MAIN;

@RestController
@RequestMapping("/system/shipMain")
public class BasicShipMainController extends BaseController {

    @Autowired
    private IBasicShipMainService basicShipMainService;

    @PostMapping("/addBasicShipMain")
    public AjaxResult addBasicShipMain(@RequestBody BasicShipMain basicShipMain) {
        return basicShipMainService.addBasicShipMain(basicShipMain);
    }

    @GetMapping("/list")
    public Page<BasicShipMain> list(BasicShipMain basicShipMain) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_SHIP_MAIN.SHIP_CHINESE_NAME.like(basicShipMain.getShipChineseName(), StringUtils.isNotEmpty(basicShipMain.getShipChineseName())))
                .and(BASIC_SHIP_MAIN.SHIP_ENGLISH_NAME.like(basicShipMain.getShipEnglishName(), StringUtils.isNotEmpty(basicShipMain.getShipEnglishName())))
                .and(BASIC_SHIP_MAIN.SHIP_CODE.like(basicShipMain.getShipCode(), StringUtils.isNotEmpty(basicShipMain.getShipCode())))
                .and(BASIC_SHIP_MAIN.MMSI.like(basicShipMain.getMmsi(), StringUtils.isNotEmpty(basicShipMain.getMmsi())))
                .orderBy(BASIC_SHIP_MAIN.CREATE_TIME.asc());

        var pageDomain = TableSupport.buildPageRequest();

        return basicShipMainService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    @PostMapping("/labelForDify")
    public List<BasicShipMain> label(BasicShipMain basicShipMain) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(BASIC_SHIP_MAIN)
                .where(BASIC_SHIP_MAIN.SHIP_CHINESE_NAME.like(basicShipMain.getShipChineseName(), StringUtils.isNotEmpty(basicShipMain.getShipChineseName())))
                .and(BASIC_SHIP_MAIN.SHIP_ENGLISH_NAME.like(basicShipMain.getShipEnglishName(), StringUtils.isNotEmpty(basicShipMain.getShipEnglishName())))
                .and(BASIC_SHIP_MAIN.SHIP_CODE.like(basicShipMain.getShipCode(), StringUtils.isNotEmpty(basicShipMain.getShipCode())))
                .and(BASIC_SHIP_MAIN.MMSI.like(basicShipMain.getMmsi(), StringUtils.isNotEmpty(basicShipMain.getMmsi())))
                .orderBy(BASIC_SHIP_MAIN.CREATE_TIME.asc());
        return basicShipMainService.list(queryWrapper);
    }

    @GetMapping("/getById/{id}")
    public AjaxResult getById(@PathVariable("id") String id) {
        BasicShipMain basicShipMain = basicShipMainService.getById(id);
        return AjaxResult.success(basicShipMain);
    }

    @PutMapping("/editBasicShipMain")
    public AjaxResult editBasicShipMain(@RequestBody BasicShipMain basicShipMain) {
        return basicShipMainService.editBasicShipMain(basicShipMain);
    }

    @DeleteMapping("/deleteByIds")
    public AjaxResult remove(@RequestBody List<String> ids) {
        return AjaxResult.success(basicShipMainService.removeByIds(ids));
    }

}
