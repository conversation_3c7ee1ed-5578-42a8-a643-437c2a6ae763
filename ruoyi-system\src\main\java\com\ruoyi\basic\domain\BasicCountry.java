package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_country")
public class BasicCountry extends BaseEntity {
    /** 国家ID */
//    @Excel(name = "国家ID")
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 国家代码 */
    @Excel(name = "国家代码")
    private String bcCountryCode;

    /** 中文名 */
    @Excel(name = "中文名")
    private String bcChineseName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String bcEnglishName;

    /** 是否删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;
}
