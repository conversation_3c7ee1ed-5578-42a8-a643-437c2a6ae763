-- ====================================================
-- 订单物流系统V3.0数据库DDL脚本
-- 版本: v3.2 (新增 logistic_waterway 子表并优化)
-- 更新时间: 2025-07-30
-- 基于会议纪要V3.0设计方案和三层解耦架构
--
-- 架构说明：ORDER_MAIN → BOOKING_MAIN → LOGISTICS_MAIN → LOGISTIC_WATERWAY
-- 核心改进：
-- 1. 三层解耦架构，业务模型拆分
-- 2. 统一订舱主表
-- 3. 命名规范统一（小写下划线）
-- 4. 雪花ID全局唯一标识
-- 5. 支持策划员拆单功能
-- 6. 优化补料环节流程
-- 7. 新增箱量拆分子表，支持箱量细粒度管理和配船
-- 8. 新增物流运输子表 (logistic_waterway)，实现运输环节解耦
-- ====================================================

-- 删除已存在的表（按依赖关系逆序删除）
-- 注意：在实际生产环境，请谨慎执行DROP TABLE，通常只在开发环境使用
BEGIN EXECUTE IMMEDIATE 'DROP TABLE booking_transit_port CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE booking_cntr_num_SPLIT CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE booking_cntr_num CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE logistic_waterway CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END; -- 新增
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE logistics_main CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE booking_main CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE order_main CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- ====================================================
-- 1. ORDER_MAIN - 委托主表（三层架构第一层）
-- ====================================================
CREATE TABLE order_main
(
    id                 VARCHAR2(32) PRIMARY KEY,      -- 雪花ID，全局唯一
    order_no           VARCHAR2(64)  NOT NULL UNIQUE, -- 委托编号：WT+日期+序号
    shipper_id         VARCHAR2(32)  NOT NULL,        -- 托运单位ID
    shipper_name       VARCHAR2(128) NOT NULL,        -- 托运单位名称（冗余）
    order_date         DATE          DEFAULT SYSDATE, -- 委托创建日期
    business_type      VARCHAR2(32),                  -- 业务类型：EXPORT/IMPORT/DOMESTIC
    trade_type         VARCHAR2(32),                  -- 贸易类型：DOMESTIC/FOREIGN
    consortium         VARCHAR2(64),                  -- 所属共同体：中山共同体/黄埔共同体
    customer_agreement VARCHAR2(128),                 -- 客户协议编号
    settlement_method  VARCHAR2(32),                  -- 结算方式：MONTHLY/PREPAID/COD
    total_containers   NUMBER(10)    DEFAULT 0,       -- 总箱量（统计字段）
    total_weight       NUMBER(18, 4) DEFAULT 0,       -- 总重量（统计字段）
    status             VARCHAR2(32)  DEFAULT 'DRAFT', -- 委托状态
    remark             VARCHAR2(500),                 -- 备注信息
    create_by          VARCHAR2(64),                  -- 创建人
    create_time        DATE          DEFAULT SYSDATE, -- 创建时间
    update_by          VARCHAR2(64),                  -- 更新人
    update_time        DATE          DEFAULT SYSDATE, -- 更新时间
    del_flag           VARCHAR2(1)   DEFAULT '0',     -- 逻辑删除：0正常 2删除
    version            NUMBER(19)    DEFAULT 1
);

COMMENT ON TABLE order_main IS '委托主表（三层架构第一层）- 客户委托的顶层抽象，一个委托可包含多个不同业务类型的订舱';
COMMENT ON COLUMN order_main.id IS '主键，雪花ID全局唯一标识符';
COMMENT ON COLUMN order_main.order_no IS '委托编号，格式：WT+日期+6位序号，如WT202507220000001';
COMMENT ON COLUMN order_main.shipper_id IS '托运单位ID，关联客户基础资料表';
COMMENT ON COLUMN order_main.shipper_name IS '托运单位名称（冗余存储便于查询）';
COMMENT ON COLUMN order_main.order_date IS '委托创建日期，业务发生日期';
COMMENT ON COLUMN order_main.business_type IS '业务类型：EXPORT（出口）/IMPORT（进口）/DOMESTIC（内贸）';
COMMENT ON COLUMN order_main.trade_type IS '贸易类型：DOMESTIC（内贸）/FOREIGN（外贸）';
COMMENT ON COLUMN order_main.consortium IS '所属共同体：中山共同体、黄埔共同体（支持联合运营业务）';
COMMENT ON COLUMN order_main.customer_agreement IS '客户协议号，运输合同编号';
COMMENT ON COLUMN order_main.settlement_method IS '结算方式：MONTHLY（月结）/PREPAID（预付）/COD（货到付款）';
COMMENT ON COLUMN order_main.total_containers IS '总箱量，所有订舱的箱量汇总（统计字段）';
COMMENT ON COLUMN order_main.total_weight IS '总重量（吨），所有订舱的重量汇总';
COMMENT ON COLUMN order_main.status IS '委托状态：DRAFT（草稿）/SUBMITTED（已提交）/CONFIRMED（已确认）/COMPLETED（已完成）/CANCELLED（已取消）';
COMMENT ON COLUMN order_main.remark IS '备注信息，客户特殊要求或注意事项';
COMMENT ON COLUMN order_main.create_by IS '创建人，操作员工号';
COMMENT ON COLUMN order_main.create_time IS '创建时间';
COMMENT ON COLUMN order_main.update_by IS '更新人，最后修改操作员工号';
COMMENT ON COLUMN order_main.update_time IS '更新时间';
COMMENT ON COLUMN order_main.del_flag IS '逻辑删除标志：0正常 2删除';
COMMENT ON COLUMN order_main.version IS '乐观锁版本号，防止并发修改';

-- ====================================================
-- 2. BOOKING_MAIN - 订舱主表 (原: 穿巴订舱)
-- ====================================================
CREATE TABLE booking_main
(
    id                        VARCHAR2(32) PRIMARY KEY,     -- 雪花ID
    order_id                  VARCHAR2(32) NOT NULL,        -- 关联order_main
    booking_number            VARCHAR2(64) NOT NULL UNIQUE, -- 订舱号
    status                    VARCHAR2(32) DEFAULT 'DRAFT', -- 订舱状态

    -- 基础信息（严格按项目经理文档）
    shipper_id                VARCHAR2(32),                 -- 托运单位ID
    shipper_name              VARCHAR2(128),                -- 托运单位名称
    origin_location_id        VARCHAR2(32),                 -- 起运地ID（使用terminal Options，支持用户自创option时为空）
    origin_location_name      VARCHAR2(128),                -- 起运地名称（使用terminal Options或用户自定义地点名称）
    destination_location_id   VARCHAR2(32),                 -- 目的地ID（使用terminal Options，支持用户自创option时为空）
    destination_location_name VARCHAR2(128),                -- 目的地名称（使用terminal Options或用户自定义地点名称）
    booking_date              DATE         DEFAULT SYSDATE, -- 订舱日期
    departure_date            DATE,                         -- 启运日期
    delivery_date             DATE,                         -- 交货日期
    loading_terminal_id       VARCHAR2(32),                 -- 装货码头ID
    loading_terminal_name     VARCHAR2(128),                -- 装货码头名称
    unloading_terminal_id     VARCHAR2(32),                 -- 卸货码头ID
    unloading_terminal_name   VARCHAR2(128),                -- 卸货码头名称

    -- 代理信息
    loading_agent_id          VARCHAR2(32),                 -- 装货代理ID
    loading_agent_name        VARCHAR2(128),                -- 装货代理名称
    unloading_agent_id        VARCHAR2(32),                 -- 卸货代理ID
    unloading_agent_name      VARCHAR2(128),                -- 卸货代理名称

    -- 业务信息
    trade_type                VARCHAR2(32),                 -- 贸易类型
    transport_mode            VARCHAR2(32),                 -- 运输模式
    customs_type              VARCHAR2(32),                 -- 报关类型
    settlement_method         VARCHAR2(32),                 -- 结算方式
    consortium                VARCHAR2(64),                 -- 所属共同体
    customer_agreement        VARCHAR2(128),                -- 客户协议
    consignment_source        VARCHAR2(64),                 -- 委托来源
    remark                    VARCHAR2(500),                -- 备注

    -- 系统字段
    create_by                 VARCHAR2(64),
    create_time               DATE         DEFAULT SYSDATE,
    update_by                 VARCHAR2(64),
    update_time               DATE         DEFAULT SYSDATE,
    del_flag                  VARCHAR2(1)  DEFAULT '0',
    version                   NUMBER(10)   DEFAULT 1,

    CONSTRAINT fk_booking_order_id FOREIGN KEY (order_id) REFERENCES order_main (id)
);

COMMENT ON TABLE booking_main IS '订舱主表（三层架构第二层）- 原为穿巴订舱(BOOKING_MAIN_CHUANBA)，现作为统一订舱模型，支持策划员拆单操作';
COMMENT ON COLUMN booking_main.id IS '主键，雪花ID全局唯一标识符';
COMMENT ON COLUMN booking_main.order_id IS '关联委托主表ID，建立委托与订舱的关系';
COMMENT ON COLUMN booking_main.booking_number IS '订舱号，格式：BK+日期+6位序号，如BK202507220000001';
COMMENT ON COLUMN booking_main.status IS '订舱状态：DRAFT（草稿）/SUBMITTED（已提交）/CONFIRMED（已确认）/REJECTED（已拒绝）/CANCELLED（已取消）';
COMMENT ON COLUMN booking_main.shipper_id IS '托运单位ID，关联客户基础资料表';
COMMENT ON COLUMN booking_main.shipper_name IS '托运单位名称（冗余存储便于查询）';
COMMENT ON COLUMN booking_main.origin_location_id IS '起运地ID（使用terminal Options，支持用户自创option时为空）';
COMMENT ON COLUMN booking_main.origin_location_name IS '起运地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询）';
COMMENT ON COLUMN booking_main.destination_location_id IS '目的地ID（使用terminal Options，支持用户自创option时为空）';
COMMENT ON COLUMN booking_main.destination_location_name IS '目的地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询）';
COMMENT ON COLUMN booking_main.booking_date IS '订舱日期，业务申请日期';
COMMENT ON COLUMN booking_main.departure_date IS '启运日期，实际发货时间（会议要求去除要求二字）';
COMMENT ON COLUMN booking_main.delivery_date IS '交货日期，实际到货时间（会议要求去除要求二字）';
COMMENT ON COLUMN booking_main.loading_terminal_id IS '装货码头ID，关联码头基础资料表';
COMMENT ON COLUMN booking_main.loading_terminal_name IS '装货码头名称（支持自动填充逻辑）';
COMMENT ON COLUMN booking_main.unloading_terminal_id IS '卸货码头ID，关联码头基础资料表';
COMMENT ON COLUMN booking_main.unloading_terminal_name IS '卸货码头名称（支持自动填充逻辑）';
COMMENT ON COLUMN booking_main.loading_agent_id IS '装货代理ID，关联代理公司基础资料表';
COMMENT ON COLUMN booking_main.loading_agent_name IS '装货代理名称（冗余存储便于查询）';
COMMENT ON COLUMN booking_main.unloading_agent_id IS '卸货代理ID，关联代理公司基础资料表';
COMMENT ON COLUMN booking_main.unloading_agent_name IS '卸货代理名称（冗余存储便于查询）';
COMMENT ON COLUMN booking_main.trade_type IS '贸易类型：DOMESTIC（内贸）/FOREIGN（外贸）';
COMMENT ON COLUMN booking_main.transport_mode IS '运输模式：FCL（整箱）/LCL（拼箱）';
COMMENT ON COLUMN booking_main.customs_type IS '报关类型：GENERAL（一般贸易）/PROCESSING（加工贸易）';
COMMENT ON COLUMN booking_main.settlement_method IS '结算方式：MONTHLY（月结）/PREPAID（预付）/COD（货到付款）';
COMMENT ON COLUMN booking_main.consortium IS '所属共同体，穿巴业务固定为穿巴';
COMMENT ON COLUMN booking_main.customer_agreement IS '客户协议编号，运输合同编号';
COMMENT ON COLUMN booking_main.consignment_source IS '委托来源：ONLINE（网上申请）/PHONE（电话委托）/ONSITE（现场委托）/EMAIL（邮件委托）';
COMMENT ON COLUMN booking_main.remark IS '备注信息，特殊要求或注意事项';
COMMENT ON COLUMN booking_main.create_by IS '创建人，操作员工号';
COMMENT ON COLUMN booking_main.create_time IS '创建时间';
COMMENT ON COLUMN booking_main.update_by IS '更新人，最后修改操作员工号';
COMMENT ON COLUMN booking_main.update_time IS '更新时间';
COMMENT ON COLUMN booking_main.del_flag IS '逻辑删除标志：0正常 2删除';
COMMENT ON COLUMN booking_main.version IS '乐观锁版本号，防止并发修改';

-- ====================================================
-- 3. LOGISTICS_MAIN - 物流主表（三层架构第三层）
-- (优化：作为运输环节的父表，移除冗余字段)
-- ====================================================
CREATE TABLE logistics_main
(
    id                        VARCHAR2(32) PRIMARY KEY, -- 雪花ID
    booking_id                VARCHAR2(32) NOT NULL,    -- 关联booking_main表

    -- 运输环节信息 (根据QA纪要调整)
    business_type             VARCHAR2(32),             -- 运输模式: WATERWAY(水路)/ROADWAY(陆路)/AIRWAY(航空)
    supplier_id               VARCHAR2(32),             -- 供应商ID
    supplier_name             VARCHAR2(128),            -- 供应商名称
    origin_location_id        VARCHAR2(32),             -- 启运地ID
    origin_location_name      VARCHAR2(128),            -- 启运地名称
    destination_location_id   VARCHAR2(32),             -- 目的地ID
    destination_location_name VARCHAR2(128),            -- 目的地名称
    required_departure_date   DATE,                     -- 要求启运日期
    required_arrival_date     DATE,                     -- 要求到达日期
    terms_conditions          VARCHAR2(1000),           -- 条款，运输合同条款或特殊约定
    remark                    VARCHAR2(500),            -- 备注信息

    -- 系统字段
    create_by                 VARCHAR2(64),
    create_time               DATE        DEFAULT SYSDATE,
    update_by                 VARCHAR2(64),
    update_time               DATE        DEFAULT SYSDATE,
    del_flag                  VARCHAR2(1) DEFAULT '0',
    version                   NUMBER(10)  DEFAULT 1,    -- 对应Java Integer类型

    CONSTRAINT fk_logistics_booking FOREIGN KEY (booking_id) REFERENCES booking_main (id)
);

COMMENT ON TABLE logistics_main IS '物流主表（三层架构第三层）- 作为运输环节的父表，定义了运输模式（水路/陆路等），具体信息由其子表（如logistic_waterway）实现';
COMMENT ON COLUMN logistics_main.id IS '主键，雪花ID全局唯一标识符';
COMMENT ON COLUMN logistics_main.booking_id IS '关联订舱主表ID，建立订舱与物流的关系';
COMMENT ON COLUMN logistics_main.business_type IS '运输模式: WATERWAY(水路)/ROADWAY(陆路)/AIRWAY(航空)，决定了关联哪个子表';
COMMENT ON COLUMN logistics_main.supplier_id IS '供应商ID，关联船务供应商基础资料表';
COMMENT ON COLUMN logistics_main.supplier_name IS '供应商名称（冗余存储便于查询）';
COMMENT ON COLUMN logistics_main.origin_location_id IS '启运地ID';
COMMENT ON COLUMN logistics_main.origin_location_name IS '启运地名称';
COMMENT ON COLUMN logistics_main.destination_location_id IS '目的地ID';
COMMENT ON COLUMN logistics_main.destination_location_name IS '目的地名称';
COMMENT ON COLUMN logistics_main.required_departure_date IS '要求启运日期，客户要求的发货时间';
COMMENT ON COLUMN logistics_main.required_arrival_date IS '要求到达日期，客户要求的到货时间';
COMMENT ON COLUMN logistics_main.terms_conditions IS '条款，运输合同条款或特殊约定';
COMMENT ON COLUMN logistics_main.remark IS '备注信息，特殊要求或注意事项';
COMMENT ON COLUMN logistics_main.create_by IS '创建人，操作员工号';
COMMENT ON COLUMN logistics_main.create_time IS '创建时间';
COMMENT ON COLUMN logistics_main.update_by IS '更新人，最后修改操作员工号';
COMMENT ON COLUMN logistics_main.update_time IS '更新时间';
COMMENT ON COLUMN logistics_main.del_flag IS '逻辑删除标志：0正常 2删除';
COMMENT ON COLUMN logistics_main.version IS '乐观锁版本号，防止并发修改';

-- ====================================================
-- 3.1. LOGISTIC_WATERWAY - 水路运输子表 (新增)
-- ====================================================
CREATE TABLE logistic_waterway
(
    id                  VARCHAR2(32) PRIMARY KEY, -- 雪花ID
    logistics_main_id   VARCHAR2(32) NOT NULL,    -- 关联logistics_main表

    -- 水路运输具体信息
    route_id                 VARCHAR2(32),             -- 航线ID
    route_name               VARCHAR2(128),            -- 航线名称
    vessel_id                VARCHAR2(32),             -- 船舶ID
    vessel_name              VARCHAR2(128),            -- 船名
    voyage_no                VARCHAR2(128),            -- 航次
    loading_terminal_id      VARCHAR2(32),             -- 装货码头ID
    loading_terminal_name    VARCHAR2(128),            -- 装货码头名称
    unloading_terminal_id    VARCHAR2(32),             -- 卸货码头ID
    unloading_terminal_name  VARCHAR2(128),            -- 卸货码头名称
    etd                      DATE,                     -- 预计离港时间 (Estimated Time of Departure)
    eta                      DATE,                     -- 预计到港时间 (Estimated Time of Arrival)
    atd                      DATE,                     -- 实际离港时间 (Actual Time of Departure)
    ata                      DATE,                     -- 实际到港时间 (Actual Time of Arrival)
    status                   VARCHAR2(32) DEFAULT 'PENDING', -- 状态：PENDING=待处理,SPLIT=已拆解,ALLOCATED=已分配,CANCEL=已取消
    sequence_no              NUMBER(10)  DEFAULT 1,    -- 运输段顺序号，用于多段运输
    stowage_plan_id          VARCHAR2(32),             -- 配载计划ID
    split_by                 VARCHAR2(64),             -- 拆单操作员
    split_time               DATE,                     -- 拆单时间

    -- 系统字段
    remark              VARCHAR2(500),            -- 备注信息
    create_by           VARCHAR2(64),
    create_time         DATE        DEFAULT SYSDATE,
    update_by           VARCHAR2(64),
    update_time         DATE        DEFAULT SYSDATE,
    del_flag            VARCHAR2(1) DEFAULT '0',
    version             NUMBER(19)  DEFAULT 1,    -- 对应Java Long类型

    CONSTRAINT fk_waterway_logistics FOREIGN KEY (logistics_main_id) REFERENCES logistics_main (id) ON DELETE CASCADE
);

COMMENT ON TABLE logistic_waterway IS '水路运输子表 - 记录具体的水路运输信息，作为 logistics_main 的一个实现';
COMMENT ON COLUMN logistic_waterway.id IS '主键，雪花ID全局唯一标识符';
COMMENT ON COLUMN logistic_waterway.logistics_main_id IS '关联物流主表ID';
COMMENT ON COLUMN logistic_waterway.route_id IS '航线ID，关联航线基础资料表';
COMMENT ON COLUMN logistic_waterway.route_name IS '航线名称，冗余存储便于查询';
COMMENT ON COLUMN logistic_waterway.vessel_id IS '船舶ID，关联船舶基础资料表';
COMMENT ON COLUMN logistic_waterway.vessel_name IS '船名';
COMMENT ON COLUMN logistic_waterway.voyage_no IS '航次';
COMMENT ON COLUMN logistic_waterway.loading_terminal_id IS '装货码头ID';
COMMENT ON COLUMN logistic_waterway.loading_terminal_name IS '装货码头名称';
COMMENT ON COLUMN logistic_waterway.unloading_terminal_id IS '卸货码头ID';
COMMENT ON COLUMN logistic_waterway.unloading_terminal_name IS '卸货码头名称';
COMMENT ON COLUMN logistic_waterway.etd IS '预计离港时间';
COMMENT ON COLUMN logistic_waterway.eta IS '预计到港时间';
COMMENT ON COLUMN logistic_waterway.atd IS '实际离港时间';
COMMENT ON COLUMN logistic_waterway.ata IS '实际到港时间';
COMMENT ON COLUMN logistic_waterway.status IS '状态：PENDING=待处理,SPLIT=已拆解,ALLOCATED=已分配,CANCEL=已取消';
COMMENT ON COLUMN logistic_waterway.sequence_no IS '运输段顺序号，用于串联多段水路运输';
COMMENT ON COLUMN logistic_waterway.stowage_plan_id IS '配载计划ID，关联配载计划表';
COMMENT ON COLUMN logistic_waterway.split_by IS '拆单操作员，记录执行拆单操作的用户';
COMMENT ON COLUMN logistic_waterway.split_time IS '拆单时间，记录拆单操作的具体时间';
COMMENT ON COLUMN logistic_waterway.remark IS '备注信息';
COMMENT ON COLUMN logistic_waterway.create_by IS '创建人';
COMMENT ON COLUMN logistic_waterway.create_time IS '创建时间';
COMMENT ON COLUMN logistic_waterway.update_by IS '更新人';
COMMENT ON COLUMN logistic_waterway.update_time IS '更新时间';
COMMENT ON COLUMN logistic_waterway.del_flag IS '逻辑删除标志：0正常 2删除';
COMMENT ON COLUMN logistic_waterway.version IS '乐观锁版本号';

-- ====================================================
-- 4. BOOKING_CNTR_NUM - 柜量信息表 (修改注释，明确为计划量)
-- 严格按项目经理柜量信息表字段设计
-- ====================================================
CREATE TABLE booking_cntr_num
(
    id                   VARCHAR2(32) PRIMARY KEY, -- 雪花ID
    booking_id           VARCHAR2(32) NOT NULL,    -- 关联booking_main表

    -- 柜量信息（严格按项目经理文档）
    -- 容器尺寸字段组（关联BASIC_CNTR_SIZE表）
    container_size_id    VARCHAR2(32),             -- 尺寸ID，关联BASIC_CNTR_SIZE.id，外键约束RESTRICT保护基础数据
    container_size_code  VARCHAR2(16),             -- 尺寸代码（冗余），如"20", "40"，便于业务逻辑处理
    container_size_name  VARCHAR2(32),             -- 尺寸显示名称（冗余），如"20''", "40''"，便于界面显示
    -- 容器类型字段组（关联BASIC_CNTR_TYPE表）
    container_type_id    VARCHAR2(32),             -- 箱型ID，关联BASIC_CNTR_TYPE.id，外键约束RESTRICT保护基础数据
    container_type_code  VARCHAR2(16),             -- 箱型代码（冗余），如"GP", "HC"，便于业务逻辑处理
    container_type_name  VARCHAR2(64),             -- 箱型显示名称（冗余），如"普通柜GP"，便于界面显示
    is_empty             VARCHAR2(1) DEFAULT '0',  -- 重吉：0重箱 1吉箱
    quantity             NUMBER(10)  DEFAULT 0,    -- 箱量
    is_dangerous         VARCHAR2(1) DEFAULT '0',  -- 是否危险品：0否 1是
    -- 危险品等级字段组（关联BASIC_DANGER表）
    dangerous_level_id   VARCHAR2(32),             -- 危险品等级ID，关联BASIC_DANGER.id
    dangerous_level_code VARCHAR2(16),             -- 危险品等级代码（冗余），映射BASIC_DANGER.dangerLevel字段
    dangerous_level_name VARCHAR2(64),             -- 危险品等级名称（冗余），映射BASIC_DANGER.dangerName字段
    is_refrigerated      VARCHAR2(1) DEFAULT '0',  -- 是否冷藏：0否 1是
    -- 货类字段组（关联BASIC_CARGO_TYPE表）
    cargo_type_id        VARCHAR2(32),             -- 货类ID，关联basic_cargo_type表主键
    cargo_type_code      VARCHAR2(32),             -- 货类代码（冗余），映射BASIC_CARGO_TYPE.cargoCode字段
    cargo_type_name      VARCHAR2(128),            -- 货类名称（冗余），映射BASIC_CARGO_TYPE.cargoName字段
    single_weight        NUMBER(18, 4),            -- 单箱重量(KG)
    total_weight         NUMBER(18, 4),            -- 总重(KG)
    is_oversize          VARCHAR2(1) DEFAULT '0',  -- 是否超限：0否 1是
    oversize_dimensions  VARCHAR2(128),            -- 超限尺寸
    remark               VARCHAR2(500),            -- 备注

    -- 系统字段
    create_by            VARCHAR2(64),
    create_time          DATE        DEFAULT SYSDATE,
    update_by            VARCHAR2(64),
    update_time          DATE        DEFAULT SYSDATE,
    del_flag             VARCHAR2(1) DEFAULT '0',
    version              NUMBER(10)  DEFAULT 1,

    -- 外键约束（Oracle默认为RESTRICT，无需显式指定）
    CONSTRAINT fk_cntr_booking FOREIGN KEY (booking_id) REFERENCES booking_main (id)
);

COMMENT ON TABLE booking_cntr_num IS '柜量信息表 - 记录单证员录入的客户委托的计划箱量信息，此表数据一旦录入即固定，后续拆分操作在 booking_cntr_num_SPLIT 表中进行';
COMMENT ON COLUMN booking_cntr_num.id IS '主键，雪花ID全局唯一标识符';
COMMENT ON COLUMN booking_cntr_num.booking_id IS '关联订舱主表ID，建立订舱与柜量的关系';
-- 容器尺寸字段组注释
COMMENT ON COLUMN booking_cntr_num.container_size_id IS '尺寸ID，关联BASIC_CNTR_SIZE.id，外键约束RESTRICT防止基础表数据被误删';
COMMENT ON COLUMN booking_cntr_num.container_size_code IS '尺寸代码（冗余存储），如"20", "40"，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT ON COLUMN booking_cntr_num.container_size_name IS '尺寸显示名称（冗余存储），如"20''''", "40''''"，用于界面显示，确保显示稳定性';
-- 容器类型字段组注释
COMMENT ON COLUMN booking_cntr_num.container_type_id IS '箱型ID，关联BASIC_CNTR_TYPE.id，外键约束RESTRICT防止基础表数据被误删';
COMMENT ON COLUMN booking_cntr_num.container_type_code IS '箱型代码（冗余存储），如"GP", "HC"，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT ON COLUMN booking_cntr_num.container_type_name IS '箱型显示名称（冗余存储），如"普通柜GP", "普通柜HC"，用于界面显示，确保显示稳定性';
COMMENT ON COLUMN booking_cntr_num.is_empty IS '重吉标识：0重箱（有货）/1吉箱（空箱）';
COMMENT ON COLUMN booking_cntr_num.quantity IS '箱量，该规格集装箱的计划数量（单证员录入后固定）';
COMMENT ON COLUMN booking_cntr_num.is_dangerous IS '是否危险品：0否/1是';
-- 危险品等级字段组注释
COMMENT ON COLUMN booking_cntr_num.dangerous_level_id IS '危险品等级ID，关联BASIC_DANGER.id，外键约束RESTRICT防止基础表数据被误删';
COMMENT ON COLUMN booking_cntr_num.dangerous_level_code IS '危险品等级代码（冗余存储），映射BASIC_DANGER.dangerLevel字段，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT ON COLUMN booking_cntr_num.dangerous_level_name IS '危险品等级名称（冗余存储），映射BASIC_DANGER.dangerName字段，用于界面显示，确保显示稳定性';
COMMENT ON COLUMN booking_cntr_num.is_refrigerated IS '是否冷藏：0否/1是';
-- 货类字段组注释
COMMENT ON COLUMN booking_cntr_num.cargo_type_id IS '货类ID，关联basic_cargo_type表主键，外键约束RESTRICT防止基础表数据被误删';
COMMENT ON COLUMN booking_cntr_num.cargo_type_code IS '货类代码（冗余存储），映射BASIC_CARGO_TYPE.cargoCode字段，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT ON COLUMN booking_cntr_num.cargo_type_name IS '货类名称（冗余存储），映射BASIC_CARGO_TYPE.cargoName字段，用于界面显示，确保显示稳定性';
COMMENT ON COLUMN booking_cntr_num.single_weight IS '单箱重量（公斤），单个集装箱的重量';
COMMENT ON COLUMN booking_cntr_num.total_weight IS '总重（公斤），该规格所有集装箱的计划总重量（单证员录入后固定）';
COMMENT ON COLUMN booking_cntr_num.is_oversize IS '是否超限：0否/1是，是否超出标准尺寸';
COMMENT ON COLUMN booking_cntr_num.oversize_dimensions IS '超限尺寸，超出标准尺寸的具体描述';
COMMENT ON COLUMN booking_cntr_num.remark IS '备注信息，特殊要求或注意事项';
COMMENT ON COLUMN booking_cntr_num.create_by IS '创建人，操作员工号';
COMMENT ON COLUMN booking_cntr_num.create_time IS '创建时间';
COMMENT ON COLUMN booking_cntr_num.update_by IS '更新人，最后修改操作员工号';
COMMENT ON COLUMN booking_cntr_num.update_time IS '更新时间';
COMMENT ON COLUMN booking_cntr_num.del_flag IS '逻辑删除标志：0正常 2删除';
COMMENT ON COLUMN booking_cntr_num.version IS '乐观锁版本号，防止并发修改';

-- ====================================================
-- 4.1. booking_cntr_num_SPLIT - 箱量拆分明细表 (新增)
-- 记录 BOOKING_CNTR_NUM 中箱量信息的拆分明细，用于配船等操作
-- ====================================================
CREATE TABLE booking_cntr_num_SPLIT
(
    id                   VARCHAR2(32) PRIMARY KEY,                                                                    -- 雪花ID
    booking_cntr_num_id  VARCHAR2(32) NOT NULL,                                                                       -- 关联 BOOKING_CNTR_NUM 表的ID，表示这条拆分明细属于哪条原始箱量数据

    -- 继承自 BOOKING_CNTR_NUM 的箱量属性，保持一致性
    container_size_id    VARCHAR2(32),                                                                                -- 尺寸ID，关联BASIC_CNTR_SIZE.id
    container_size_code  VARCHAR2(16),                                                                                -- 尺寸代码（冗余）
    container_size_name  VARCHAR2(32),                                                                                -- 尺寸显示名称（冗余）
    container_type_id    VARCHAR2(32),                                                                                -- 箱型ID，关联BASIC_CNTR_TYPE.id
    container_type_code  VARCHAR2(16),                                                                                -- 箱型代码（冗余）
    container_type_name  VARCHAR2(64),                                                                                -- 箱型显示名称（冗余）
    is_empty             VARCHAR2(1)   DEFAULT '0',                                                                   -- 重吉：0重箱 1吉箱
    is_dangerous         VARCHAR2(1)   DEFAULT '0',                                                                   -- 是否危险品：0否 1是
    dangerous_level_id   VARCHAR2(32),                                                                                -- 危险品等级ID，关联BASIC_DANGER.id
    dangerous_level_code VARCHAR2(16),                                                                                -- 危险品等级代码（冗余）
    dangerous_level_name VARCHAR2(64),                                                                                -- 危险品等级名称（冗余）
    is_refrigerated      VARCHAR2(1)   DEFAULT '0',                                                                   -- 是否冷藏：0否 1是
    cargo_type_id        VARCHAR2(32),                                                                                -- 货类ID，关联basic_cargo_type表主键
    cargo_type_code      VARCHAR2(32),                                                                                -- 货类代码（冗余）
    cargo_type_name      VARCHAR2(128),                                                                               -- 货类名称（冗余）
    is_oversize          VARCHAR2(1)   DEFAULT '0',                                                                   -- 是否超限：0否 1是
    oversize_dimensions  VARCHAR2(128),                                                                               -- 超限尺寸

    -- 拆分后的具体箱量和重量
    split_quantity       NUMBER(10)    DEFAULT 0,                                                                     -- 拆分后的箱量
    split_total_weight   NUMBER(18, 4) DEFAULT 0,                                                                     -- 拆分后的总重 (KG，可能不准确)

    -- 拆分相关业务字段
    related_waterway_id  VARCHAR2(32),                                                                                -- 关联水路运输ID（关联 logistic_waterway.id）

    remark               VARCHAR2(500),                                                                               -- 备注信息

    -- 系统字段
    create_by            VARCHAR2(64),                                                                                -- 创建人 (策划员)
    create_time          DATE          DEFAULT SYSDATE,                                                               -- 创建时间
    update_by            VARCHAR2(64),                                                                                -- 更新人
    update_time          DATE          DEFAULT SYSDATE,                                                               -- 更新时间
    del_flag             VARCHAR2(1)   DEFAULT '0',                                                                   -- 逻辑删除：0正常 2删除
    version              NUMBER(19)    DEFAULT 1,                                                                     -- 对应Java Long类型

    CONSTRAINT fk_cntr_split_num FOREIGN KEY (booking_cntr_num_id) REFERENCES booking_cntr_num (id) ON DELETE CASCADE -- 级联删除
);

COMMENT ON TABLE booking_cntr_num_SPLIT IS '箱量拆分明细表 - 记录 BOOKING_CNTR_NUM 中箱量信息的拆分明细，用于策划员进行配船等操作，并与运输环节关联';
COMMENT ON COLUMN booking_cntr_num_SPLIT.id IS '主键，雪花ID全局唯一标识符';
COMMENT ON COLUMN booking_cntr_num_SPLIT.booking_cntr_num_id IS '关联 BOOKING_CNTR_NUM 表的ID，表示这条拆分明细属于哪条原始箱量数据';
COMMENT ON COLUMN booking_cntr_num_SPLIT.container_size_id IS '尺寸ID，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.container_size_code IS '尺寸代码（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.container_size_name IS '尺寸显示名称（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.container_type_id IS '箱型ID，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.container_type_code IS '箱型代码（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.container_type_name IS '箱型显示名称（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.is_empty IS '重吉标识：0重箱（有货）/1吉箱（空箱），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.is_dangerous IS '是否危险品：0否/1是，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.dangerous_level_id IS '危险品等级ID，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.dangerous_level_code IS '危险品等级代码（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.dangerous_level_name IS '危险品等级名称（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.is_refrigerated IS '是否冷藏：0否/1是，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.cargo_type_id IS '货类ID，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.cargo_type_code IS '货类代码（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.cargo_type_name IS '货类名称（冗余），继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.is_oversize IS '是否超限：0否/1是，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.oversize_dimensions IS '超限尺寸，继承自 BOOKING_CNTR_NUM';
COMMENT ON COLUMN booking_cntr_num_SPLIT.split_quantity IS '拆分后的箱量，该条明细对应的具体箱数';
COMMENT ON COLUMN booking_cntr_num_SPLIT.split_total_weight IS '拆分后的总重（公斤），该条明细对应的总重量';
COMMENT ON COLUMN booking_cntr_num_SPLIT.related_waterway_id IS '关联水路运输ID (logistic_waterway.id)，实现箱量到运力的分配';
COMMENT ON COLUMN booking_cntr_num_SPLIT.remark IS '备注信息';
COMMENT ON COLUMN booking_cntr_num_SPLIT.create_by IS '创建人，通常为策划员';
COMMENT ON COLUMN booking_cntr_num_SPLIT.create_time IS '创建时间';
COMMENT ON COLUMN booking_cntr_num_SPLIT.update_by IS '更新人';
COMMENT ON COLUMN booking_cntr_num_SPLIT.update_time IS '更新时间';
COMMENT ON COLUMN booking_cntr_num_SPLIT.del_flag IS '逻辑删除标志：0正常 2删除';
COMMENT ON COLUMN booking_cntr_num_SPLIT.version IS '乐观锁版本号，防止并发修改';

-- ====================================================
-- 5. BOOKING_TRANSIT_PORT - 中转港表
-- 严格按项目经理中转港表字段设计
-- ====================================================
CREATE TABLE booking_transit_port
(
    id                VARCHAR2(32) PRIMARY KEY, -- 雪花ID
    booking_id        VARCHAR2(32) NOT NULL,    -- 关联booking_main表

    -- 中转港信息（严格按项目经理文档）
    sequence_no       NUMBER(10),               -- 序号
    transit_port_id   VARCHAR2(32),             -- 中转港ID
    transit_port_name VARCHAR2(128),            -- 中转港名称
    agent_id          VARCHAR2(32),             -- 代理ID
    agent_name        VARCHAR2(128),            -- 代理名称

    -- 系统字段
    remark            VARCHAR2(500),            -- 备注信息
    create_by         VARCHAR2(64),
    create_time       DATE        DEFAULT SYSDATE,
    update_by         VARCHAR2(64),
    update_time       DATE        DEFAULT SYSDATE,
    del_flag          VARCHAR2(1) DEFAULT '0',
    version           NUMBER(10)  DEFAULT 1,

    CONSTRAINT fk_transit_booking FOREIGN KEY (booking_id) REFERENCES booking_main (id)
);

COMMENT ON TABLE booking_transit_port IS '中转港表 - 严格按项目经理中转港表字段设计，记录运输中转港信息';
COMMENT ON COLUMN booking_transit_port.id IS '主键，雪花ID全局唯一标识符';
COMMENT ON COLUMN booking_transit_port.booking_id IS '关联订舱主表ID，建立订舱与中转港的关系';
COMMENT ON COLUMN booking_transit_port.sequence_no IS '序号，中转港的顺序编号（按中转先后顺序）';
COMMENT ON COLUMN booking_transit_port.transit_port_id IS '中转港ID，关联港口基础资料表';
COMMENT ON COLUMN booking_transit_port.transit_port_name IS '中转港名称（冗余存储便于查询）';
COMMENT ON COLUMN booking_transit_port.agent_id IS '代理公司ID，关联代理公司基础资料表';
COMMENT ON COLUMN booking_transit_port.agent_name IS '代理公司名称（冗余存储便于查询）';
COMMENT ON COLUMN booking_transit_port.remark IS '备注信息，特殊要求或注意事项';
COMMENT ON COLUMN booking_transit_port.create_by IS '创建人，操作员工号';
COMMENT ON COLUMN booking_transit_port.create_time IS '创建时间';
COMMENT ON COLUMN booking_transit_port.update_by IS '更新人，最后修改操作员工号';
COMMENT ON COLUMN booking_transit_port.update_time IS '更新时间';
COMMENT ON COLUMN booking_transit_port.del_flag IS '逻辑删除标志：0正常 2删除';
COMMENT ON COLUMN booking_transit_port.version IS '乐观锁版本号，防止并发修改';

-- ====================================================
-- 创建基础索引（安全创建，避免重复索引错误）
-- ====================================================

-- 主要查询索引（使用安全创建方式）
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_order_main_no ON order_main (order_no)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_booking_order ON booking_main (order_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_logistics_booking ON logistics_main (booking_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

-- 新增 logistic_waterway 的索引
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_waterway_logistics ON logistic_waterway (logistics_main_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_cntr_num_booking ON booking_cntr_num (booking_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_cntr_cargo_type ON booking_cntr_num (cargo_type_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

-- 新增 booking_cntr_num_SPLIT 的索引
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_cntr_split_num_parent ON booking_cntr_num_SPLIT (booking_cntr_num_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_cntr_split_waterway ON booking_cntr_num_SPLIT (related_waterway_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_transit_booking ON booking_transit_port (booking_id)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 AND SQLCODE != -1408 THEN RAISE; END IF;
END;
/

-- ====================================================
-- 主要约束
-- ====================================================

-- 状态检查约束
ALTER TABLE order_main
    ADD CONSTRAINT chk_order_status
        CHECK (status IN ('DRAFT', 'SUBMITTED', 'CONFIRMED', 'COMPLETED', 'CANCELLED'));

ALTER TABLE booking_main
    ADD CONSTRAINT chk_booking_status
        CHECK (status IN ('DRAFT', 'SUBMITTED', 'CONFIRMED', 'REJECTED', 'CANCELLED'));


-- 容器数据完整性约束（确保冗余字段数据完整性）
ALTER TABLE booking_cntr_num
    ADD CONSTRAINT chk_container_size_consistency
        CHECK ((container_size_id IS NULL AND container_size_code IS NULL AND container_size_name IS NULL)
            OR (container_size_id IS NOT NULL AND container_size_code IS NOT NULL AND container_size_name IS NOT NULL));

ALTER TABLE booking_cntr_num
    ADD CONSTRAINT chk_container_type_consistency
        CHECK ((container_type_id IS NULL AND container_type_code IS NULL AND container_type_name IS NULL)
            OR (container_type_id IS NOT NULL AND container_type_code IS NOT NULL AND container_type_name IS NOT NULL));

-- 危险品等级数据完整性约束（确保冗余字段数据完整性）
ALTER TABLE booking_cntr_num
    ADD CONSTRAINT chk_dangerous_level_consistency
        CHECK ((dangerous_level_id IS NULL AND dangerous_level_code IS NULL AND dangerous_level_name IS NULL)
            OR (dangerous_level_id IS NOT NULL AND dangerous_level_code IS NOT NULL AND
                dangerous_level_name IS NOT NULL));

-- 货类数据完整性约束（确保冗余字段数据完整性）
ALTER TABLE booking_cntr_num
    ADD CONSTRAINT chk_cargo_type_consistency
        CHECK ((cargo_type_id IS NULL AND cargo_type_code IS NULL AND cargo_type_name IS NULL)
            OR (cargo_type_id IS NOT NULL AND cargo_type_code IS NOT NULL AND cargo_type_name IS NOT NULL));

-- 新增 booking_cntr_num_SPLIT 的冗余字段一致性约束 (与 BOOKING_CNTR_NUM 类似)
ALTER TABLE booking_cntr_num_SPLIT
    ADD CONSTRAINT chk_split_cntr_size_consistency
        CHECK ((container_size_id IS NULL AND container_size_code IS NULL AND container_size_name IS NULL)
            OR (container_size_id IS NOT NULL AND container_size_code IS NOT NULL AND container_size_name IS NOT NULL));

ALTER TABLE booking_cntr_num_SPLIT
    ADD CONSTRAINT chk_split_cntr_type_consistency
        CHECK ((container_type_id IS NULL AND container_type_code IS NULL AND container_type_name IS NULL)
            OR (container_type_id IS NOT NULL AND container_type_code IS NOT NULL AND container_type_name IS NOT NULL));

ALTER TABLE booking_cntr_num_SPLIT
    ADD CONSTRAINT chk_split_dangerous_level_consistency
        CHECK ((dangerous_level_id IS NULL AND dangerous_level_code IS NULL AND dangerous_level_name IS NULL)
            OR (dangerous_level_id IS NOT NULL AND dangerous_level_code IS NOT NULL AND
                dangerous_level_name IS NOT NULL));

ALTER TABLE booking_cntr_num_SPLIT
    ADD CONSTRAINT chk_split_cargo_type_consistency
        CHECK ((cargo_type_id IS NULL AND cargo_type_code IS NULL AND cargo_type_name IS NULL)
            OR (cargo_type_id IS NOT NULL AND cargo_type_code IS NOT NULL AND cargo_type_name IS NOT NULL));

-- ====================================================
-- 完成提示
-- ====================================================
SELECT '订单物流系统V3.2数据库DDL脚本执行完成!' AS message
FROM DUAL;

-- ====================================================
-- 总结：
-- 1. 四层解耦架构清晰：ORDER_MAIN → BOOKING_MAIN → LOGISTICS_MAIN → LOGISTIC_WATERWAY
-- 2. 严格按项目经理字段规范设计
-- 3. BOOKING_CNTR_NUM表定位明确：作为单证员录入的“计划箱量”，数据一旦录入即固定。
-- 4. 新增 booking_cntr_num_SPLIT 表：
--    - 承载策划员对箱量的拆分明细，用于配船等操作。
--    - 继承原始箱量属性，确保拆分前后箱型、危险品、货类等属性一致。
--    - 外键关联 BOOKING_CNTR_NUM，并设置为 ON DELETE CASCADE，实现级联删除。
-- 5. 新增 logistic_waterway 表：
--    - 作为 logistics_main 的子表，用于存储具体的水路运输信息。
--    - booking_cntr_num_split 通过 related_waterway_id 与其关联，完成箱量到运力的分配。
-- 6. 数据保护机制完善：多层约束确保数据质量和系统稳定性。
-- 7. 支持前后端联调，便于快速迭代和维护。
-- ====================================================