package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicDangerUnNumber;
import com.ruoyi.basic.mapper.BasicDangerUnNumberMapper;
import java.util.List;

public interface IBasicDangerUnNumberService extends IService<BasicDangerUnNumber> {
    BasicDangerUnNumberMapper getMapper();
    
    int deleteByDangerId(String dangerId);
    int insertBatch(List<BasicDangerUnNumber> list);
}
