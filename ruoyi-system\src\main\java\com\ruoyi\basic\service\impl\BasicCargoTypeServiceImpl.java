package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicCargoType;
import com.ruoyi.basic.mapper.BasicCargoTypeMapper;
import com.ruoyi.basic.service.IBasicCargoTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCargoTypeTableDef.BASIC_CARGO_TYPE;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicCargoTypeServiceImpl extends ServiceImpl<BasicCargoTypeMapper,BasicCargoType> implements IBasicCargoTypeService {

    @Autowired
    private BasicCargoTypeMapper basicCargoTypeMapper;


    /**
     * 查询货物基础信息
     *
     * @param id 货物基础信息主键
     * @return 货物基础信息
     */
    @Override
    public BasicCargoType selectBasicCargoTypeById(String id) {
        return basicCargoTypeMapper.selectOneById(id);
    }

    /**
     * 查询货物基础信息列表
     *
     * @param basicCargoType 货物基础信息
     * @return 货物基础信息
     */
    @Override
    public List<BasicCargoType> selectBasicCargoTypeList(BasicCargoType basicCargoType) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(BASIC_CARGO_TYPE)
                .where(BASIC_CARGO_TYPE.CARGO_CODE.eq(basicCargoType.getCargoCode()))
                .and(BASIC_CARGO_TYPE.CARGO_NAME.like(basicCargoType.getCargoName()))
                .orderBy(BASIC_CARGO_TYPE.CREATE_TIME.asc());
        return basicCargoTypeMapper.selectListByQuery(queryWrapper);
    }

    /**
     * 新增货物基础信息
     *
     * @param basicCargoType 货物基础信息
     * @return 结果
     */
    @Override
    public int insertBasicCargoType(BasicCargoType basicCargoType) throws Exception {
        checkUnique(basicCargoType);
        return basicCargoTypeMapper.insert(basicCargoType);
    }

    /**
     * 修改货物基础信息
     *
     * @param basicCargoType 货物基础信息
     * @return 结果
     */
    @Override
    public int updateBasicCargoType(BasicCargoType basicCargoType) throws Exception {
        checkUnique(basicCargoType);
        return basicCargoTypeMapper.update(basicCargoType);
    }

    /**
     * 批量删除货物基础信息
     *
     * @param ids 需要删除的货物基础信息主键
     * @return 结果
     */
    @Override
    public boolean deleteBasicCargoTypeByIds(List<String> ids) {
        return super.removeByIds(ids);
    }

    /**
     * 删除货物基础信息信息
     *
     * @param id 货物基础信息主键
     * @return 结果
     */
    @Override
    public int deleteBasicCargoTypeById(String id) {
        return basicCargoTypeMapper.deleteById(id);
    }

    public void checkUnique(BasicCargoType basicCargoType) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(BASIC_CARGO_TYPE)
                .where(BASIC_CARGO_TYPE.CARGO_CODE.eq(basicCargoType.getCargoCode()))
                .and(BASIC_CARGO_TYPE.ID.ne(basicCargoType.getId()));

        long count = basicCargoTypeMapper.selectCountByQuery(queryWrapper);
        if (count > 0) {
            throw new Exception("货物代码不唯一");
        }
    }
}
