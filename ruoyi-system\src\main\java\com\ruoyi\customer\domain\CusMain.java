package com.ruoyi.customer.domain;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.mybatisflex.annotation.*;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @TableName CUS_MAIN
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("cus_main")
public class CusMain extends BaseEntity implements Serializable {
    /**
     * 客户id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String cusId;

    /**
     * 客户名称
     */
    private String cusName;

    /**
     * 客户简称
     */
    private String cusAbbreviation;

    /**
     * 客户代码
     */
    private String cusCode;

    /**
     * 客户类型
     */
    @Column(ignore = true)
    private List<String> cusType;

    /**
     * 唯一识别码
     */
    private String cusCreditCode;

    /**
     * 客户属性
     */
    private String cusIdentity;

    /**
     * 状态
     */
    private String status;

    /**
     * 企业类型
     */
    @Column(ignore = true)
    private List<String> businessType;

    /**
     * 行业
     */
    private String industry;

    /**
     * 法人
     */
    private String legalPersonName;

    /**
     * 经营状态
     */
    private String regStatus;

    /**
     * 注册资本
     */
    private String regCapital;

    /**
     * 实缴资本
     */
    private String actualCapital;

    /**
     * 成立日期
     */
    private Date establishTime;

    /**
     * 核准日期
     */
    private Date approvedTime;

    /**
     * 核营业开始日期
     */
    private Date fromTime;

    /**
     * 人员规模
     */
    private String staffNumRange;

    /**
     * 参保人数
     */
    private Long socialStaffNum;

    /**
     * 登记机关
     */
    private String regInstitute;

    /**
     * 曾用名
     */
    private String historyName;

    /**
     * 注册地址
     */
    private String regLocation;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 发票抬头
     */
    private String invoiceHeader;

    /**
     * 税号
     */
    private String invoiceNumber;

    /**
     * 单位地址
     */
    private String invoiceAddress;

    /**
     * 电话
     */
    private String invoicePhone;

    /**
     * 开户银行
     */
    private String invoiceBank;

    /**
     * 银行账户
     */
    private String invoiceBankAccount;

    /**
     * 收票邮箱
     */
    private String invoiceReceiveAddress;

    @RelationOneToMany(selfField = "cusId", targetField = "cusMainId")
    private List<CusContact> cusContacts;

    @RelationOneToMany(selfField = "cusId", targetField = "cusMainId")
    private List<CusBank> cusBankAccounts;

    @RelationOneToMany(selfField = "cusId", targetField = "cusMainId")
    private List<CusBusinessType> cusBusinessTypes;

    @RelationOneToMany(selfField = "cusId", targetField = "cusMainId")
    private List<CusType> cusTypes;

    @RelationOneToMany(selfField = "cusId", targetField = "cusMainId")
    private List<CusCompanyType> cusCompanyTypes;

    @Column(ignore = true)
    private List<String> companyType;

    @Column(isLogicDelete = true)
    private String delFlag;

    private String containerOwner;

    private String cusArea;

    @Column(ignore = true)
    private List<String> cusAreas;

    private String cusRoute;

    @Column(ignore = true)
    private List<String> cusRoutes;

    @RelationOneToMany(selfField = "cusId", targetField = "cusMainId")
    private List<CusBargeSupplier> cusBargeSuppliers;

    @Column(ignore = true)
    private String cusBargeSupplier;

    private static final long serialVersionUID = 1L;
}