package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicZone;
import com.ruoyi.basic.domain.BasicZoneTerminal;
import com.ruoyi.basic.mapper.BasicZoneMapper;
import com.ruoyi.basic.mapper.BasicZoneTerminalMapper;
import com.ruoyi.basic.service.IBasicZoneService;
import com.ruoyi.basic.service.IBasicZoneTerminalService;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicZoneServiceImpl extends ServiceImpl<BasicZoneMapper, BasicZone> implements IBasicZoneService {
    @Autowired
    private BasicZoneMapper basicZoneMapper;

//    @Autowired
//    private BasicZoneTerminalMapper basicZoneTerminalMapper;

    @Autowired
    private IBasicZoneTerminalService basicZoneTerminalService;

    /**
     * 查询关区管理
     *
     * @param id 关区管理主键
     * @return 关区管理
     */
    @Override
    public BasicZone selectBasicZoneById(String id)
    {
        BasicZone basicZone = basicZoneMapper.selectOneById(id);

        List<BasicZoneTerminal> basicZoneTerminalList = basicZoneTerminalService.list(QueryWrapper.create()
                .eq("zone_id", id)
                .orderBy("create_time", true)
        );

        if(!basicZoneTerminalList.isEmpty()){
            for (BasicZoneTerminal bzt: basicZoneTerminalList) {
                bzt.setId("");
            }
        }

        basicZone.setBasicZoneTerminalList(basicZoneTerminalList);

        return basicZone;
    }

    /**
     * 查询关区管理列表
     *
     * @param basicZone 关区管理
     * @return 关区管理
     */
    @Override
    public List<BasicZone> selectBasicZoneList(BasicZone basicZone)
    {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicZone.getZoneCode())) {
            queryWrapper.like("zone_code", basicZone.getZoneCode());
        }
        if (StringUtils.isNotEmpty(basicZone.getZoneName())) {
            queryWrapper.like("zone_name", basicZone.getZoneName());
        }
        queryWrapper.orderBy("create_time", true);
        return basicZoneMapper.selectListByQuery(queryWrapper);
    }

    /**
     * 新增关区管理
     *
     * @param basicZone 关区管理
     * @return 结果
     */
    @Override
    public int insertBasicZone(BasicZone basicZone) throws Exception {
        int rows = basicZoneMapper.insert(basicZone);
        insertBasicZoneTerminal(basicZone);
        return rows;
    }

    /**
     * 修改关区管理
     *
     * @param basicZone 关区管理
     * @return 结果
     */
    @Override
    public int updateBasicZone(BasicZone basicZone) throws Exception {
        basicZoneTerminalService.remove(QueryWrapper.create().eq("zone_id", basicZone.getId()));
        insertBasicZoneTerminal(basicZone);
        return basicZoneMapper.update(basicZone);
    }

    /**
     * 批量删除关区管理
     *
     * @param ids 需要删除的关区管理主键集合
     * @return 结果
     */
    @Override
    public int deleteBasicZoneByIds(List<String> ids)
    {
        basicZoneTerminalService.remove(QueryWrapper.create().in("zone_id", ids));
        return basicZoneMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除关区管理信息
     *
     * @param id 关区管理主键
     * @return 结果
     */
    @Override
    public int deleteBasicZoneById(String id)
    {
        basicZoneTerminalService.remove(QueryWrapper.create().eq("zone_id", id));
        return basicZoneMapper.deleteById(id);
    }

    /**
     * 新增关区码头关联信息
     *
     * @param basicZone 关区管理对象
     */
    public void insertBasicZoneTerminal(BasicZone basicZone)
    {
        List<BasicZoneTerminal> basicZoneTerminalList = basicZone.getBasicZoneTerminalList();
        String id = basicZone.getId();
        if (StringUtils.isNotNull(basicZoneTerminalList))
        {
            List<BasicZoneTerminal> list = new ArrayList<>();
            for (BasicZoneTerminal basicZoneTerminal : basicZoneTerminalList)
            {
                basicZoneTerminal.setZoneId(id);
                list.add(basicZoneTerminal);
            }
            if (list.size() > 0)
            {
//                basicZoneTerminalMapper.insertBatch(list);
                basicZoneTerminalService.saveBatch(list);
            }
        }
    }
}
