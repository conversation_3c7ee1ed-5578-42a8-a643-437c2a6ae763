import request from '@/utils/request'

// 新增自有箱信息
export function addBasicCntrMainOwn(data) {
  return request({
    url: '/system/cntrMainOwn/add',
    method: 'post',
    data: data
  })
}

// 分页查询自有箱列表
export function list(query) {
  return request({
    url: '/system/cntrMainOwn/list',
    method: 'get',
    params: query
  })
}

// 标签查询（用于下拉选择等）
export function labelCntrMainOwn(data) {
  return request({
    url: '/system/cntrMainOwn/label',
    method: 'post',
    data: data
  })
}

// 根据ID查询自有箱信息
export function getById(id) {
  return request({
    url: '/system/cntrMainOwn/getById/' + id,
    method: 'get'
  })
}

// 修改自有箱信息
export function editBasicCntrMainOwn(data) {
  return request({
    url: '/system/cntrMainOwn/edit',
    method: 'put',
    data: data
  })
}

// 删除自有箱信息
export function deleteBasicCntrMainOwn(ids) {
  return request({
    url: '/system/cntrMainOwn/deleteByIds',
    method: 'delete',
    data: ids
  })
}
