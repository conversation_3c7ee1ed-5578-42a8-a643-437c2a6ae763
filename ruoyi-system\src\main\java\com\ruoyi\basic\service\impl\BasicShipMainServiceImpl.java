package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicShipMain;
import com.ruoyi.basic.mapper.BasicShipMainMapper;
import com.ruoyi.basic.service.IBasicShipMainService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;


import static com.ruoyi.basic.domain.table.BasicShipMainTableDef.BASIC_SHIP_MAIN;

@Service
public class BasicShipMainServiceImpl extends ServiceImpl<BasicShipMainMapper, BasicShipMain> implements IBasicShipMainService {
    @Override
    public AjaxResult addBasicShipMain(BasicShipMain basicShipMain) {
        // 1. 构建查询条件，只有非空值才加入条件
        QueryWrapper queryWrapper = QueryWrapper.create();
        boolean hasCondition = false;

        if (StringUtils.isNotBlank(basicShipMain.getShipChineseName())) {
            queryWrapper.or("ship_chinese_name = ?", basicShipMain.getShipChineseName());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getShipEnglishName())) {
            queryWrapper.or("ship_english_name = ?", basicShipMain.getShipEnglishName());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getShipCode())) {
            queryWrapper.or("ship_code = ?", basicShipMain.getShipCode());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getMmsi())) {
            queryWrapper.or("mmsi = ?", basicShipMain.getMmsi());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getImo())) {
            queryWrapper.or("imo = ?", basicShipMain.getImo());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getCallSign())) {
            queryWrapper.or("call_sign = ?", basicShipMain.getCallSign());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getHsCode())) {
            queryWrapper.or("hs_code = ?", basicShipMain.getHsCode());
            hasCondition = true;
        }

        // 如果所有字段都为空，直接返回错误
        if (!hasCondition) {
            return AjaxResult.error("船舶信息不能全部为空");
        }

        // 2. 检查是否存在重复记录
        BasicShipMain existShip = this.getOne(queryWrapper);
        if (existShip != null) {
            // 3. 判断具体是哪个字段重复，只比对非空值
            if (StringUtils.isNotBlank(basicShipMain.getShipChineseName())
                    && basicShipMain.getShipChineseName().equals(existShip.getShipChineseName())) {
                return AjaxResult.error("船舶中文名已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getShipEnglishName())
                    && basicShipMain.getShipEnglishName().equals(existShip.getShipEnglishName())) {
                return AjaxResult.error("船舶英文名已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getShipCode())
                    && basicShipMain.getShipCode().equals(existShip.getShipCode())) {
                return AjaxResult.error("船舶代码已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getMmsi())
                    && basicShipMain.getMmsi().equals(existShip.getMmsi())) {
                return AjaxResult.error("MMSI已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getImo())
                    && basicShipMain.getImo().equals(existShip.getImo())) {
                return AjaxResult.error("IMO已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getCallSign())
                    && basicShipMain.getCallSign().equals(existShip.getCallSign())) {
                return AjaxResult.error("呼号已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getHsCode())
                    && basicShipMain.getHsCode().equals(existShip.getHsCode())) {
                return AjaxResult.error("海关编码已存在");
            }
            return AjaxResult.error("数据重复");
        }

        // 4. 执行新增
        boolean success = this.save(basicShipMain);
        if (success) {
            return AjaxResult.success("新增成功");
        } else {
            return AjaxResult.error("新增失败");
        }
    }

    @Override
    public AjaxResult editBasicShipMain(BasicShipMain basicShipMain) {
        // 1. 获取原始数据
        BasicShipMain originBasicShipMain = this.getById(basicShipMain.getId());
        if (originBasicShipMain == null) {
            return AjaxResult.error("要修改的记录不存在");
        }

        // 2. 构建查询条件，只有非空值且与原值不同时才加入条件
        QueryWrapper queryWrapper = QueryWrapper.create();
        boolean hasCondition = false;

        if (StringUtils.isNotBlank(basicShipMain.getShipChineseName())
                && !basicShipMain.getShipChineseName().equals(originBasicShipMain.getShipChineseName())) {
            queryWrapper.or("ship_chinese_name = ?", basicShipMain.getShipChineseName());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getShipEnglishName())
                && !basicShipMain.getShipEnglishName().equals(originBasicShipMain.getShipEnglishName())) {
            queryWrapper.or("ship_english_name = ?", basicShipMain.getShipEnglishName());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getShipCode())
                && !basicShipMain.getShipCode().equals(originBasicShipMain.getShipCode())) {
            queryWrapper.or("ship_code = ?", basicShipMain.getShipCode());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getMmsi())
                && !basicShipMain.getMmsi().equals(originBasicShipMain.getMmsi())) {
            queryWrapper.or("mmsi = ?", basicShipMain.getMmsi());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getImo())
                && !basicShipMain.getImo().equals(originBasicShipMain.getImo())) {
            queryWrapper.or("imo = ?", basicShipMain.getImo());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getCallSign())
                && !basicShipMain.getCallSign().equals(originBasicShipMain.getCallSign())) {
            queryWrapper.or("call_sign = ?", basicShipMain.getCallSign());
            hasCondition = true;
        }
        if (StringUtils.isNotBlank(basicShipMain.getHsCode())
                && !basicShipMain.getHsCode().equals(originBasicShipMain.getHsCode())) {
            queryWrapper.or("hs_code = ?", basicShipMain.getHsCode());
            hasCondition = true;
        }

        // 如果没有字段发生变化，直接返回成功
        if (!hasCondition) {
            return AjaxResult.success("修改成功");
        }

        // 3. 排除当前记录
        queryWrapper.and("id != ?", basicShipMain.getId());

        // 4. 检查是否存在重复记录
        BasicShipMain existShip = this.getOne(queryWrapper);
        if (existShip != null) {
            // 5. 判断具体是哪个字段重复
            if (StringUtils.isNotBlank(basicShipMain.getShipChineseName())
                    && basicShipMain.getShipChineseName().equals(existShip.getShipChineseName())
                    && !basicShipMain.getShipChineseName().equals(originBasicShipMain.getShipChineseName())) {
                return AjaxResult.error("船舶中文名已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getShipEnglishName())
                    && basicShipMain.getShipEnglishName().equals(existShip.getShipEnglishName())
                    && !basicShipMain.getShipEnglishName().equals(originBasicShipMain.getShipEnglishName())) {
                return AjaxResult.error("船舶英文名已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getShipCode())
                    && basicShipMain.getShipCode().equals(existShip.getShipCode())
                    && !basicShipMain.getShipCode().equals(originBasicShipMain.getShipCode())) {
                return AjaxResult.error("船舶代码已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getMmsi())
                    && basicShipMain.getMmsi().equals(existShip.getMmsi())
                    && !basicShipMain.getMmsi().equals(originBasicShipMain.getMmsi())) {
                return AjaxResult.error("MMSI已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getImo())
                    && basicShipMain.getImo().equals(existShip.getImo())
                    && !basicShipMain.getImo().equals(originBasicShipMain.getImo())) {
                return AjaxResult.error("IMO已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getCallSign())
                    && basicShipMain.getCallSign().equals(existShip.getCallSign())
                    && !basicShipMain.getCallSign().equals(originBasicShipMain.getCallSign())) {
                return AjaxResult.error("呼号已存在");
            }
            if (StringUtils.isNotBlank(basicShipMain.getHsCode())
                    && basicShipMain.getHsCode().equals(existShip.getHsCode())
                    && !basicShipMain.getHsCode().equals(originBasicShipMain.getHsCode())) {
                return AjaxResult.error("海关编码已存在");
            }
            return AjaxResult.error("数据重复");
        }

        // 6. 执行更新
        boolean success = this.updateById(basicShipMain);
        if (success) {
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.error("修改失败");
        }
    }
}
