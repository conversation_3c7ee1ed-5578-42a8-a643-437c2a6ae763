package com.ruoyi.plan.domain;

// TODO: 等待重构 - booking模块已删除，需要重新定义配载相关的DTO结构
// import com.ruoyi.booking.domain.dto.BookingCntrCoarseWithAllocationDTO;
import lombok.Data;
import java.util.List;

@Data
public class BookingVoyageWithBerthsVO {
    private VoyageMain voyage; // 航次信息
    private String voyageId; // 航次ID（用于配载到现有航次）
    private List<VoyageSeq> berths; // 靠序信息
    // TODO: 等待重构 - 需要重新定义配载相关的DTO，替代BookingCntrCoarseWithAllocationDTO
    // private List<BookingCntrCoarseWithAllocationDTO> bookingCntrCoarseList; // 箱型明细
    private List<Object> bookingCntrCoarseList; // 箱型明细 - 临时用Object类型，等待重构
} 