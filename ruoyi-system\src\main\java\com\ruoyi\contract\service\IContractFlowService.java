package com.ruoyi.contract.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.contract.domain.ContractFlow;
import java.util.List;

/**
 * 合同流向服务接口
 */
public interface IContractFlowService {
    
    /**
     * 保存合同流向列表
     * @param flows 流向列表
     * @return 操作结果
     */
    AjaxResult saveFlows(List<ContractFlow> flows);
    
    /**
     * 根据合同ID获取流向列表
     * @param contractId 合同ID
     * @return 流向列表
     */
    AjaxResult getFlowsByContractId(String contractId);
    
    /**
     * 删除合同流向
     * @param flowId 流向ID
     * @return 操作结果
     */
    AjaxResult deleteFlow(String flowId);
} 