package com.ruoyi.booking.domain;

import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.core.domain.BaseEntity;
import java.math.BigDecimal;
import com.mybatisflex.core.keygen.KeyGenerators;

/**
 * 柜量信息表
 * 严格按项目经理柜量信息表字段设计，记录集装箱详细信息
 */
@Data
@Table("BOOKING_CNTR_NUM")
@EqualsAndHashCode(callSuper = false)
public class BookingCntrNum extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键，雪花ID全局唯一标识符 */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /** 关联订舱主表ID，建立订舱与柜量的关系 */
    @Column("BOOKING_ID")
    private String bookingId;

    // 容器尺寸字段组（关联BASIC_CNTR_SIZE表）
    /** 尺寸ID，关联BASIC_CNTR_SIZE.id，外键约束RESTRICT保护基础数据 */
    @Column("CONTAINER_SIZE_ID")
    private String containerSizeId;

    /** 尺寸代码（冗余），如"20", "40"，便于业务逻辑处理 */
    @Column("CONTAINER_SIZE_CODE")
    private String containerSizeCode;

    /** 尺寸显示名称（冗余），如"20'", "40'"，便于界面显示 */
    @Column("CONTAINER_SIZE_NAME")
    private String containerSizeName;

    // 容器类型字段组（关联BASIC_CNTR_TYPE表）
    /** 箱型ID，关联BASIC_CNTR_TYPE.id，外键约束RESTRICT保护基础数据 */
    @Column("CONTAINER_TYPE_ID")
    private String containerTypeId;

    /** 箱型代码（冗余），如"GP", "HC"，便于业务逻辑处理 */
    @Column("CONTAINER_TYPE_CODE")
    private String containerTypeCode;

    /** 箱型显示名称（冗余），如"普通柜GP"，便于界面显示 */
    @Column("CONTAINER_TYPE_NAME")
    private String containerTypeName;

    /** 重吉标识：0重箱（有货）/1吉箱（空箱） */
    @Column("IS_EMPTY")
    private String isEmpty;

    /** 箱量，该规格集装箱的数量 */
    @Column("QUANTITY")
    private Integer quantity;

    /** 是否危险品：0否/1是 */
    @Column("IS_DANGEROUS")
    private String isDangerous;

    // 危险品等级字段组（关联BASIC_DANGER表）
    /** 危险品等级ID，关联BASIC_DANGER.id */
    @Column("DANGEROUS_LEVEL_ID")
    private String dangerousLevelId;

    /** 危险品等级代码（冗余），映射BASIC_DANGER.dangerLevel字段 */
    @Column("DANGEROUS_LEVEL_CODE")
    private String dangerousLevelCode;

    /** 危险品等级名称（冗余），映射BASIC_DANGER.dangerName字段 */
    @Column("DANGEROUS_LEVEL_NAME")
    private String dangerousLevelName;

    /** 是否冷藏：0否/1是 */
    @Column("IS_REFRIGERATED")
    private String isRefrigerated;

    // 货类字段组（关联BASIC_CARGO_TYPE表）
    /** 货类ID，关联basic_cargo_type表主键 */
    @Column("CARGO_TYPE_ID")
    private String cargoTypeId;

    /** 货类代码（冗余），映射BASIC_CARGO_TYPE.cargoCode字段 */
    @Column("CARGO_TYPE_CODE")
    private String cargoTypeCode;

    /** 货类名称（冗余），映射BASIC_CARGO_TYPE.cargoName字段 */
    @Column("CARGO_TYPE_NAME")
    private String cargoTypeName;

    /** 单箱重量（公斤），单个集装箱的重量 */
    @Column("SINGLE_WEIGHT")
    private BigDecimal singleWeight;

    /** 总重（公斤），该规格所有集装箱的总重量 */
    @Column("TOTAL_WEIGHT")
    private BigDecimal totalWeight;

    /** 是否超限：0否/1是，是否超出标准尺寸 */
    @Column("IS_OVERSIZE")
    private String isOversize;

    /** 超限尺寸，超出标准尺寸的具体描述 */
    @Column("OVERSIZE_DIMENSIONS")
    private String oversizeDimensions;

    /** 备注信息，特殊要求或注意事项 */
    @Column("REMARK")
    private String remark;

    /** 逻辑删除标志：0正常 2删除 */
    @Column(value = "DEL_FLAG", isLogicDelete = true)
    private String delFlag;

    /** 乐观锁版本号，防止并发修改 */
    @Column(value = "VERSION", version = true)
    private Integer version;
}