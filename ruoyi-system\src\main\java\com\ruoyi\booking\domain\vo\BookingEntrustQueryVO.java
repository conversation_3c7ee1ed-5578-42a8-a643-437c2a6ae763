package com.ruoyi.booking.domain.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 订舱委托查询条件VO
 * 用于列表查询条件封装
 */
@Data
public class BookingEntrustQueryVO {
    
    /** 客户名称 */
    private String customerName;
    
    /** 委托号 */
    private String entrustNo;
    
    /** 船名航次 */
    private String vesselVoyage;
    
    /** 起运港 */
    private String pol;
    
    /** 目的港 */
    private String pod;
    
    /** 委托日期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entrustDateStart;
    
    /** 委托日期结束 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entrustDateEnd;
    
    /** 委托状态 */
    private String entrustStatus;
    
    /** 订舱号 */
    private String bookingNumber;
    
    /** 托运单位名称 */
    private String shipperName;
    
    /** 业务类型 */
    private String businessType;
    
    /** 贸易类型 */
    private String tradeType;
    
    /** 分页参数 - 页码 */
    private Integer pageNum = 1;
    
    /** 分页参数 - 页大小 */
    private Integer pageSize = 10;
}