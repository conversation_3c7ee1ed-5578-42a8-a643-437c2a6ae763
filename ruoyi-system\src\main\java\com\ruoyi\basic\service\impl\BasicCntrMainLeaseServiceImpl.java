package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicCntrMainLease;
import com.ruoyi.basic.mapper.BasicCntrMainLeaseMapper;
import com.ruoyi.basic.service.IBasicCntrMainLeaseService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;



/**
 * 租赁箱基础信息服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class BasicCntrMainLeaseServiceImpl extends ServiceImpl<BasicCntrMainLeaseMapper, BasicCntrMainLease> implements IBasicCntrMainLeaseService {

    @Override
    public AjaxResult addBasicCntrMainLease(BasicCntrMainLease basicCntrMainLease) {
        // 1. 检查箱号是否为空
        if (StringUtils.isBlank(basicCntrMainLease.getCntrNo())) {
            return AjaxResult.error("箱号不能为空");
        }

        // 2. 检查箱号是否已存在
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where("cntr_no = ?", basicCntrMainLease.getCntrNo());
        
        BasicCntrMainLease existCntr = this.getOne(queryWrapper);
        if (existCntr != null) {
            return AjaxResult.error("箱号已存在");
        }

        // 3. 执行新增
        boolean success = this.save(basicCntrMainLease);
        if (success) {
            return AjaxResult.success("新增成功");
        } else {
            return AjaxResult.error("新增失败");
        }
    }

    @Override
    public AjaxResult editBasicCntrMainLease(BasicCntrMainLease basicCntrMainLease) {
        // 1. 检查记录是否存在
        BasicCntrMainLease originCntr = this.getById(basicCntrMainLease.getId());
        if (originCntr == null) {
            return AjaxResult.error("要修改的记录不存在");
        }

        // 2. 检查箱号是否为空
        if (StringUtils.isBlank(basicCntrMainLease.getCntrNo())) {
            return AjaxResult.error("箱号不能为空");
        }

        // 3. 如果箱号发生变化，检查新箱号是否已存在
        if (!basicCntrMainLease.getCntrNo().equals(originCntr.getCntrNo())) {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where("cntr_no = ?", basicCntrMainLease.getCntrNo())
                    .and("id != ?", basicCntrMainLease.getId());
            
            BasicCntrMainLease existCntr = this.getOne(queryWrapper);
            if (existCntr != null) {
                return AjaxResult.error("箱号已存在");
            }
        }

        // 4. 执行更新
        boolean success = this.updateById(basicCntrMainLease);
        if (success) {
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.error("修改失败");
        }
    }
}