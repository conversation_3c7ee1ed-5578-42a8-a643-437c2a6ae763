package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCntrSize;

import java.util.List;

public interface IBasicCntrSizeService extends IService<BasicCntrSize> {

    /**
     * 查询箱尺寸
     *
     * @param id 箱尺寸主键
     * @return 箱尺寸
     */
    public BasicCntrSize selectBasicCntrSizeById(String id);

    /**
     * 查询箱尺寸列表
     *
     * @param basicCntrSize 箱尺寸
     * @return 箱尺寸集合
     */
    public List<BasicCntrSize> selectBasicCntrSizeList(BasicCntrSize basicCntrSize);

    /**
     * 新增箱尺寸
     *
     * @param basicCntrSize 箱尺寸
     * @return 结果
     */
    public int insertBasicCntrSize(BasicCntrSize basicCntrSize);

    /**
     * 修改箱尺寸
     *
     * @param basicCntrSize 箱尺寸
     * @return 结果
     */
    public int updateBasicCntrSize(BasicCntrSize basicCntrSize);

    /**
     * 批量删除箱尺寸
     *
     * @param ids 需要删除的箱尺寸主键集合
     * @return 结果
     */
    public int deleteBasicCntrSizeByIds(List<String> ids);

    /**
     * 删除箱尺寸信息
     *
     * @param id 箱尺寸主键
     * @return 结果
     */
    public int deleteBasicCntrSizeById(String id);

}
