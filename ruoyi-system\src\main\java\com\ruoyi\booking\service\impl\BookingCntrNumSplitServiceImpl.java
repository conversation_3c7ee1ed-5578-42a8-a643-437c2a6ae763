package com.ruoyi.booking.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.BookingCntrNumSplit;
import com.ruoyi.booking.mapper.BookingCntrNumSplitMapper;
import com.ruoyi.booking.service.IBookingCntrNumSplitService;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ruoyi.booking.domain.table.BookingCntrNumSplitTableDef.BOOKING_CNTR_NUM_SPLIT;


/**
 * 箱量拆分明细表 Service 实现
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
@Slf4j
public class BookingCntrNumSplitServiceImpl extends ServiceImpl<BookingCntrNumSplitMapper, BookingCntrNumSplit> implements IBookingCntrNumSplitService {
    
    /**
     * 分页查询箱量拆分明细
     */
    @Override
    public Page<BookingCntrNumSplit> selectBookingCntrNumSplitPage(int pageNumber, int pageSize, BookingCntrNumSplit bookingCntrNumSplit) {
        log.debug("分页查询箱量拆分明细 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, bookingCntrNumSplit);
        
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(BOOKING_CNTR_NUM_SPLIT)
                .where(BOOKING_CNTR_NUM_SPLIT.BOOKING_CNTR_NUM_ID.eq(bookingCntrNumSplit.getBookingCntrNumId(), StringUtils.isNotBlank(bookingCntrNumSplit.getBookingCntrNumId())))
                .and(BOOKING_CNTR_NUM_SPLIT.CONTAINER_SIZE_CODE.like(bookingCntrNumSplit.getContainerSizeCode(), StringUtils.isNotBlank(bookingCntrNumSplit.getContainerSizeCode())))
                .and(BOOKING_CNTR_NUM_SPLIT.CONTAINER_TYPE_CODE.like(bookingCntrNumSplit.getContainerTypeCode(), StringUtils.isNotBlank(bookingCntrNumSplit.getContainerTypeCode())))
                .and(BOOKING_CNTR_NUM_SPLIT.CARGO_TYPE_CODE.like(bookingCntrNumSplit.getCargoTypeCode(), StringUtils.isNotBlank(bookingCntrNumSplit.getCargoTypeCode())))
                .and(BOOKING_CNTR_NUM_SPLIT.RELATED_WATERWAY_ID.eq(bookingCntrNumSplit.getRelatedWaterwayId(), StringUtils.isNotBlank(bookingCntrNumSplit.getRelatedWaterwayId())))
                .and(BOOKING_CNTR_NUM_SPLIT.IS_EMPTY.eq(bookingCntrNumSplit.getIsEmpty(), StringUtils.isNotBlank(bookingCntrNumSplit.getIsEmpty())))
                .and(BOOKING_CNTR_NUM_SPLIT.IS_DANGEROUS.eq(bookingCntrNumSplit.getIsDangerous(), StringUtils.isNotBlank(bookingCntrNumSplit.getIsDangerous())))
                .and(BOOKING_CNTR_NUM_SPLIT.IS_REFRIGERATED.eq(bookingCntrNumSplit.getIsRefrigerated(), StringUtils.isNotBlank(bookingCntrNumSplit.getIsRefrigerated())))
                .and(BOOKING_CNTR_NUM_SPLIT.IS_OVERSIZE.eq(bookingCntrNumSplit.getIsOversize(), StringUtils.isNotBlank(bookingCntrNumSplit.getIsOversize())))
                .orderBy(BOOKING_CNTR_NUM_SPLIT.CREATE_TIME.desc());
        
        Page<BookingCntrNumSplit> result = mapper.paginate(pageNumber, pageSize, queryWrapper);
        
        log.debug("分页查询箱量拆分明细完成 - 总记录数: {}", result.getTotalRow());
        
        return result;
    }
} 