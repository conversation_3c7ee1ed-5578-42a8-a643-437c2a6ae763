package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCntrType;
import com.ruoyi.basic.service.IBasicCntrTypeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCntrTypeTableDef.BASIC_CNTR_TYPE;

/**
 * 箱型Controller
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
@RestController
@RequestMapping("/system/cntrType")
public class BasicCntrTypeController extends BaseController {

    @Autowired
    private IBasicCntrTypeService basicCntrTypeService;

    /**
     * 查询箱型列表
     */
    @PreAuthorize("@ss.hasPermi('system:cntrType:list')")
    @GetMapping("/list")
    public Page<BasicCntrType> list(BasicCntrType basicCntrType)
    {
        // startPage();
        // List<BasicCntrType> list = basicCntrTypeService.selectBasicCntrTypeList(basicCntrType);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper
        .select()
            .from(BASIC_CNTR_TYPE)
            .where(BASIC_CNTR_TYPE.TYPE_CODE.like(basicCntrType.getTypeCode()))
            .and(BASIC_CNTR_TYPE.TYPE_NAME.like(basicCntrType.getTypeName()))
        .orderBy(BasicCntrType::getCreateTime, true);
        return basicCntrTypeService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    @PreAuthorize("@ss.hasPermi('system:cntrType:list')")
    @GetMapping("/label")
    public AjaxResult label(){
        return AjaxResult.success(basicCntrTypeService.list(QueryChain.of(BasicCntrType.class)
                .orderBy(BasicCntrType::getCreateTime, true)));
    }

    /**
     * 导出箱型列表
     */
    @PreAuthorize("@ss.hasPermi('system:cntrType:export')")
    @Log(title = "箱型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicCntrType basicCntrType)
    {
        List<BasicCntrType> list = basicCntrTypeService.selectBasicCntrTypeList(basicCntrType);
        ExcelUtil<BasicCntrType> util = new ExcelUtil<BasicCntrType>(BasicCntrType.class);
        util.exportExcel(response, list, "箱型数据");
    }

    /**
     * 获取箱型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:cntrType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicCntrTypeService.selectBasicCntrTypeById(id));
    }

    /**
     * 新增箱型
     */
    @PreAuthorize("@ss.hasPermi('system:cntrType:add')")
    @Log(title = "箱型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicCntrType basicCntrType)
    {
        try {
            return toAjax(basicCntrTypeService.insertBasicCntrType(basicCntrType));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改箱型
     */
    @PreAuthorize("@ss.hasPermi('system:cntrType:edit')")
    @Log(title = "箱型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicCntrType basicCntrType)
    {
        try {
            return toAjax(basicCntrTypeService.updateBasicCntrType(basicCntrType));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除箱型
     */
    @PreAuthorize("@ss.hasPermi('system:cntrType:remove')")
    @Log(title = "箱型", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicCntrTypeService.deleteBasicCntrTypeByIds(ids));
    }

}
