package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCountry;

import java.util.List;

public interface IBasicCountryService extends IService<BasicCountry> {

    /**
     * 查询国家
     *
     * @param id 国家主键
     * @return 国家
     */
    public BasicCountry selectBasicCountryById(String id);

    /**
     * 查询国家列表
     *
     * @param basicCountry 国家
     * @return 国家集合
     */
    public List<BasicCountry> selectBasicCountryList(BasicCountry basicCountry);

    /**
     * 新增国家
     *
     * @param basicCountry 国家
     * @return 结果
     */
    public int insertBasicCountry(BasicCountry basicCountry);

    /**
     * 修改国家
     *
     * @param basicCountry 国家
     * @return 结果
     */
    public int updateBasicCountry(BasicCountry basicCountry);

    /**
     * 批量删除国家
     *
     * @param ids 需要删除的国家主键集合
     * @return 结果
     */
    public int deleteBasicCountryByIds(List<String> ids);

    /**
     * 删除国家信息
     *
     * @param id 国家主键
     * @return 结果
     */
    public int deleteBasicCountryById(String id);

}
