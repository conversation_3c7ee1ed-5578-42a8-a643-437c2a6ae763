package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCountry;
import com.ruoyi.basic.service.IBasicCountryService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCountryTableDef.BASIC_COUNTRY;

/**
 * 国家Controller
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@RestController
@RequestMapping("/system/country")
public class BasicCountryController extends BaseController {

    @Autowired
    private IBasicCountryService basicCountryService;

    /**
     * 查询国家列表
     */
    @PreAuthorize("@ss.hasPermi('system:country:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasicCountry basicCountry)
    {
        startPage();
        List<BasicCountry> list = basicCountryService.selectBasicCountryList(basicCountry);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('system:country:list')")
    @GetMapping("/page")
    public Page<BasicCountry> page(BasicCountry basicCountry, Integer pageNum, Integer pageSize){
        return basicCountryService.page(new Page<>(pageNum,pageSize),  QueryWrapper.create()
                .select()
                .from(BASIC_COUNTRY)
                .where(BASIC_COUNTRY.BC_COUNTRY_CODE.like(basicCountry.getBcCountryCode()))
                .and(BASIC_COUNTRY.BC_CHINESE_NAME.like(basicCountry.getBcChineseName()))
                .and(BASIC_COUNTRY.BC_ENGLISH_NAME.like(basicCountry.getBcEnglishName())));
    }

    @GetMapping("/label")
    public AjaxResult label(){

        return AjaxResult.success(basicCountryService.list());

    }

    /**
     * 导出国家列表
     */
    @PreAuthorize("@ss.hasPermi('system:country:export')")
    @Log(title = "国家", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicCountry basicCountry)
    {
        List<BasicCountry> list = basicCountryService.selectBasicCountryList(basicCountry);
        ExcelUtil<BasicCountry> util = new ExcelUtil<BasicCountry>(BasicCountry.class);
        util.exportExcel(response, list, "国家数据");
    }

    /**
     * 获取国家详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:country:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicCountryService.selectBasicCountryById(id));
    }

    /**
     * 新增国家
     */
    @PreAuthorize("@ss.hasPermi('system:country:add')")
    @Log(title = "国家", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicCountry basicCountry)
    {
        return toAjax(basicCountryService.insertBasicCountry(basicCountry));
    }

    /**
     * 修改国家
     */
    @PreAuthorize("@ss.hasPermi('system:country:edit')")
    @Log(title = "国家", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicCountry basicCountry)
    {
        return toAjax(basicCountryService.updateBasicCountry(basicCountry));
    }

    /**
     * 删除国家
     */
    @PreAuthorize("@ss.hasPermi('system:country:remove')")
    @Log(title = "国家", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicCountryService.deleteBasicCountryByIds(ids));
    }

}
