<template>
  <div class="booking-panel">
    <!-- 基础信息 -->
    <div class="form-section">
      <div class="section-header">
        <h4 class="section-title">
          <el-icon>
            <InfoFilled/>
          </el-icon>
          基础信息
        </h4>
      </div>

      <div class="section-content">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="订舱号" prop="bookingMain.bookingNumber">
              <el-input
                  v-model="formData.bookingMain.bookingNumber"
                  :placeholder="mode === 'add' ? '保存后系统自动生成' : '请输入订舱号'"
                  :disabled="isViewMode || mode === 'add'"
                  clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="托运单位" prop="bookingMain.shipperId">
              <el-select
                  v-model="formData.bookingMain.shipperId"
                  placeholder="请选择托运单位"
                  filterable
                  remote
                  default-first-option
                  clearable
                  :remote-method="searchCustomerOptions"
                  :loading="loading"
                  :loading-text="'搜索中...'"
                  :disabled="isViewMode"
                  :reserve-keyword="false"
                  :automatic-dropdown="true"
                  :no-match-text="customerOptions.length === 0 ? '未找到匹配的托运单位' : '按 Enter 键自动选择'"
                  style="width: 100%"
                  @change="handleShipperChange"
              >
                <el-option
                    v-for="shipper in customerOptions"
                    :key="shipper.value"
                    :label="shipper.label"
                    :value="shipper.value"
                >
                  <div class="customer-option-flex">
                    <span class="customer-name">{{ shipper.cusName }}</span>
                    <span class="customer-extra" v-if="shipper.extraInfo">
                      {{ shipper.extraInfo }}
                    </span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订舱日期" prop="bookingMain.bookingDate">
              <el-date-picker
                  v-model="formData.bookingMain.bookingDate"
                  type="date"
                  placeholder="选择订舱日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  :disabled="isViewMode"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="起运地" prop="bookingMain.originLocationName">
              <el-select
                  v-model="originLocationDisplayValue"
                  placeholder="请选择起运地"
                  filterable
                  allow-create
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
              >
                <el-option
                    v-for="terminal in terminalOptions"
                    :key="terminal.value"
                    :label="terminal.label"
                    :value="terminal.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="目的地" prop="bookingMain.destinationLocationName">
              <el-select
                  v-model="destinationLocationDisplayValue"
                  placeholder="请选择目的地"
                  filterable
                  allow-create
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
              >
                <el-option
                    v-for="terminal in terminalOptions"
                    :key="terminal.value"
                    :label="terminal.label"
                    :value="terminal.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启运日期" prop="bookingMain.departureDate">
              <el-date-picker
                  v-model="formData.bookingMain.departureDate"
                  type="date"
                  placeholder="选择启运日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  :disabled="isViewMode"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="装货码头" prop="bookingMain.loadingTerminalId">
              <el-select
                  v-model="formData.bookingMain.loadingTerminalId"
                  placeholder="请选择装货码头"
                  filterable
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
                  @change="handleLoadingTerminalChange"
              >
                <el-option
                    v-for="terminal in terminalOptions"
                    :key="terminal.value"
                    :label="terminal.label"
                    :value="terminal.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卸货码头" prop="bookingMain.unloadingTerminalId">
              <el-select
                  v-model="formData.bookingMain.unloadingTerminalId"
                  placeholder="请选择卸货码头"
                  filterable
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
                  @change="handleUnloadingTerminalChange"
              >
                <el-option
                    v-for="terminal in terminalOptions"
                    :key="terminal.value"
                    :label="terminal.label"
                    :value="terminal.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交货日期" prop="bookingMain.deliveryDate">
              <el-date-picker
                  v-model="formData.bookingMain.deliveryDate"
                  type="date"
                  placeholder="选择交货日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  :disabled="isViewMode"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="装货代理" prop="bookingMain.loadingAgentId">
              <el-select
                  v-model="formData.bookingMain.loadingAgentId"
                  placeholder="请选择装货代理"
                  filterable
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
                  @change="handleLoadingAgentChange"
              >
                <el-option
                    v-for="agent in agentOptions"
                    :key="agent.value"
                    :label="agent.label"
                    :value="agent.value"
                >
                  <span style="float: left">{{ agent.label }}</span>
                  <span
                      v-if="agent.routeName"
                      style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                  >
                    {{ agent.routeName }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卸货代理" prop="bookingMain.unloadingAgentId">
              <el-select
                  v-model="formData.bookingMain.unloadingAgentId"
                  placeholder="请选择卸货代理"
                  filterable
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
                  @change="handleUnloadingAgentChange"
              >
                <el-option
                    v-for="agent in agentOptions"
                    :key="agent.value"
                    :label="agent.label"
                    :value="agent.value"
                >
                  <span style="float: left">{{ agent.label }}</span>
                  <span
                      v-if="agent.routeName"
                      style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                  >
                    {{ agent.routeName }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="贸易类型" prop="bookingMain.tradeType">
              <el-radio-group v-model="formData.bookingMain.tradeType" :disabled="isViewMode">
                <el-radio value="DOMESTIC">内贸</el-radio>
                <el-radio value="FOREIGN">外贸</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="运输模式" prop="bookingMain.transportMode">
              <el-select
                  v-model="formData.bookingMain.transportMode"
                  placeholder="请选择运输模式"
                  clearable
                  :disabled="isViewMode"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
              >
                <el-option
                    v-for="mode in shipping_transport_mode"
                    :key="mode.value"
                    :label="mode.label"
                    :value="mode.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报关类型" prop="bookingMain.customsType">
              <el-select
                  v-model="formData.bookingMain.customsType"
                  placeholder="请选择报关类型"
                  clearable
                  :disabled="isViewMode"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
              >
                <el-option
                    v-for="type in shipping_customs_type"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结算方式" prop="bookingMain.settlementMethod">
              <el-select
                  v-model="formData.bookingMain.settlementMethod"
                  placeholder="请选择结算方式"
                  clearable
                  :disabled="isViewMode"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
              >
                <el-option
                    v-for="method in shipping_settlement_method"
                    :key="method.value"
                    :label="method.label"
                    :value="method.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="所属共同体" prop="bookingMain.consortium">
              <el-select
                  v-model="formData.bookingMain.consortium"
                  placeholder="请选择所属共同体"
                  clearable
                  :disabled="isViewMode"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
              >
                <el-option
                    v-for="consortium in shipping_consortium"
                    :key="consortium.value"
                    :label="consortium.label"
                    :value="consortium.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="委托来源" prop="bookingMain.consignmentSource">
              <el-select
                  v-model="formData.bookingMain.consignmentSource"
                  placeholder="请选择委托来源"
                  clearable
                  :disabled="isViewMode"
                  :no-data-text="'暂无数据'"
                  style="width: 100%"
              >
                <el-option
                    v-for="source in shipping_consignment_source"
                    :key="source.value"
                    :label="source.label"
                    :value="source.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户协议" prop="bookingMain.customerAgreement">
              <el-input
                  v-model="formData.bookingMain.customerAgreement"
                  placeholder="请输入客户协议号"
                  :disabled="isViewMode"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="bookingMain.remark">
              <el-input
                  v-model="formData.bookingMain.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                  maxlength="500"
                  show-word-limit
                  :disabled="isViewMode"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 柜量信息 -->
    <div class="form-section">
      <div class="section-header">
        <h4 class="section-title">
          <el-icon>
            <Box/>
          </el-icon>
          柜量信息
        </h4>
        <el-button
            type="primary"
            size="small"
            @click="addContainer"
            :disabled="isViewMode"
        >
          <el-icon>
            <Plus/>
          </el-icon>
          添加柜量
        </el-button>
      </div>

      <div class="section-content">
        <div class="container-table-wrapper">
          <el-table
              :data="formData.bookingCntrNumList"
              border
              style="width: 100%"
              class="container-table"
              max-height="250"
          >
            <el-table-column label="序号" type="index" width="60" align="center" fixed="left"/>

            <el-table-column label="尺寸" width="100" align="center">
              <template #default="{ row, $index }">
                <el-select
                    v-model="row.containerSizeId"
                    size="small"
                    filterable
                    default-first-option
                    clearable
                    :disabled="isViewMode"
                    :automatic-dropdown="true"
                    :reserve-keyword="false"
                    :no-data-text="'暂无数据'"
                    @change="(value) => handleContainerSizeChange(row, value)"
                >
                  <el-option
                      v-for="size in containerSizeOptions"
                      :key="size.value"
                      :value="size.value"
                      :label="size.label"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="箱型" width="120" align="center">
              <template #default="{ row, $index }">
                <el-select
                    v-model="row.containerTypeId"
                    size="small"
                    filterable
                    default-first-option
                    clearable
                    :disabled="isViewMode"
                    :automatic-dropdown="true"
                    :reserve-keyword="false"
                    :no-data-text="'暂无数据'"
                    @change="(value) => handleContainerTypeChange(row, value)"
                >
                  <el-option
                      v-for="type in containerTypeOptions"
                      :key="type.value"
                      :value="type.value"
                      :label="type.label"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="重吉" width="80" align="center">
              <template #default="{ row, $index }">
                <el-select
                    v-model="row.isEmpty"
                    size="small"
                    filterable
                    default-first-option
                    clearable
                    :disabled="isViewMode"
                    :automatic-dropdown="true"
                    :reserve-keyword="false"
                    :no-data-text="'暂无数据'"
                >
                  <el-option value="0" label="重箱"/>
                  <el-option value="1" label="吉箱"/>
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="箱量" width="110" align="center">
              <template #default="{ row, $index }">
                <el-input-number
                    v-model="row.quantity"
                    size="small"
                    :min="1"
                    :max="999"
                    :disabled="isViewMode"
                    @change="handleContainerChange"
                />
              </template>
            </el-table-column>

            <el-table-column label="危险品" width="80" align="center">
              <template #default="{ row, $index }">
                <el-switch
                    v-model="row.isDangerous"
                    active-value="1"
                    inactive-value="0"
                    size="small"
                    :disabled="isViewMode"
                />
              </template>
            </el-table-column>

            <el-table-column label="危险等级" width="120" align="center">
              <template #default="{ row, $index }">
                <el-select
                    v-model="row.dangerousLevelId"
                    size="small"
                    placeholder="等级"
                    filterable
                    default-first-option
                    clearable
                    :disabled="isViewMode || row.isDangerous === '0'"
                    :automatic-dropdown="true"
                    :reserve-keyword="false"
                    :no-data-text="'暂无数据'"
                    @change="(value) => handleDangerousLevelChange(row, value)"
                >
                  <el-option
                      v-for="danger in dangerLevelOptions"
                      :key="danger.value"
                      :value="danger.value"
                      :label="danger.label"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="冷藏" width="80" align="center">
              <template #default="{ row, $index }">
                <el-switch
                    v-model="row.isRefrigerated"
                    active-value="1"
                    inactive-value="0"
                    size="small"
                    :disabled="isViewMode"
                />
              </template>
            </el-table-column>

            <el-table-column label="货类" width="120" align="center">
              <template #default="{ row, $index }">
                <el-select
                    v-model="row.cargoTypeId"
                    size="small"
                    placeholder="货类"
                    filterable
                    default-first-option
                    clearable
                    :disabled="isViewMode"
                    :automatic-dropdown="true"
                    :reserve-keyword="false"
                    :no-data-text="'暂无数据'"
                    @change="(value) => handleCargoTypeChange(row, value)"
                >
                  <el-option
                      v-for="cargo in cargoTypeOptions"
                      :key="`cargo-${cargo.value}`"
                      :value="cargo.value"
                      :label="cargo.label"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="单箱重量(KG)" width="140" align="center">
              <template #default="{ row, $index }">
                <el-input-number
                    v-model="row.singleWeight"
                    size="small"
                    :min="0"
                    :precision="2"
                    :disabled="isViewMode"
                    @change="calculateRowTotalWeight(row)"
                />
              </template>
            </el-table-column>

            <el-table-column label="总重(KG)" width="120" align="center">
              <template #default="{ row, $index }">
                <el-input-number
                    v-model="row.totalWeight"
                    size="small"
                    :min="0"
                    :precision="2"
                    :disabled="isViewMode"
                    @change="handleContainerChange"
                />
              </template>
            </el-table-column>

            <el-table-column label="超限" width="80" align="center">
              <template #default="{ row, $index }">
                <el-switch
                    v-model="row.isOversize"
                    active-value="1"
                    inactive-value="0"
                    size="small"
                    :disabled="isViewMode"
                />
              </template>
            </el-table-column>

            <el-table-column label="超限尺寸" width="150" align="center">
              <template #default="{ row, $index }">
                <el-input
                    v-model="row.oversizeDimensions"
                    size="small"
                    placeholder="长x宽x高"
                    :disabled="isViewMode || row.isOversize === '0'"
                />
              </template>
            </el-table-column>

            <el-table-column label="备注" width="150" align="center">
              <template #default="{ row, $index }">
                <el-input
                    v-model="row.remark"
                    size="small"
                    placeholder="备注"
                    :disabled="isViewMode"
                />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80" align="center" fixed="right" v-if="!isViewMode">
              <template #default="{ row, $index }">
                <el-button
                    type="danger"
                    size="small"
                    @click="removeContainer($index)"
                    :disabled="formData.bookingCntrNumList.length <= 1"
                >
                  <el-icon>
                    <Delete/>
                  </el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 运输环节信息 -->
    <div class="form-section">
      <div class="section-header">
        <h4 class="section-title">
          <el-icon>
            <Van/>
          </el-icon>
          运输环节
        </h4>
        <el-button
            type="primary"
            size="small"
            @click="addLogistics"
            :disabled="isViewMode"
        >
          <el-icon>
            <Plus/>
          </el-icon>
          添加运输环节
        </el-button>
      </div>

      <div class="section-content">
        <el-table
            :data="formData.logisticsMainList"
            border
            style="width: 100%"
            class="logistics-table"
            max-height="200"
            v-if="formData.logisticsMainList && formData.logisticsMainList.length > 0"
        >
          <el-table-column label="序号" type="index" width="60" align="center"/>

          <el-table-column label="运输方式" width="120" align="center">
            <template #default="{ row, $index }">
              <el-select
                  v-model="row.businessType"
                  size="small"
                  placeholder="运输方式"
                  style="width: 100%"
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
              >
                <el-option
                    v-for="type in shipping_business_type"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="供应商" min-width="150" align="center">
            <template #default="{ row, $index }">
              <el-select
                  v-model="row.supplierId"
                  size="small"
                  filterable
                  placeholder="请选择供应商"
                  style="width: 100%"
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  @change="handleSupplierChange(row)"
              >
                <el-option
                    v-for="supplier in supplierOptions"
                    :key="supplier.value"
                    :label="supplier.label"
                    :value="supplier.value"
                >
                  <span style="float: left">{{ supplier.label }}</span>
                  <span
                      v-if="supplier.routeName"
                      style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                  >
                    {{ supplier.routeName }}
                  </span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="启运地" min-width="150" align="center">
            <template #default="{ row, $index }">
              <el-select
                  v-model="row.originLocationId"
                  size="small"
                  filterable
                  placeholder="请选择启运地"
                  style="width: 100%"
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  @change="handleLogisticsOriginChange(row)"
              >
                <el-option
                    v-for="terminal in terminalOptions"
                    :key="terminal.value"
                    :label="terminal.label"
                    :value="terminal.value"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="目的地" min-width="150" align="center">
            <template #default="{ row, $index }">
              <el-select
                  v-model="row.destinationLocationId"
                  size="small"
                  filterable
                  placeholder="请选择目的地"
                  style="width: 100%"
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  @change="handleLogisticsDestinationChange(row)"
              >
                <el-option
                    v-for="terminal in terminalOptions"
                    :key="terminal.value"
                    :label="terminal.label"
                    :value="terminal.value"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="条款" min-width="120" align="center">
            <template #default="{ row, $index }">
              <el-input
                  v-model="row.termsConditions"
                  size="small"
                  placeholder="请输入条款"
                  :disabled="isViewMode"
              />
            </template>
          </el-table-column>

          <el-table-column label="要求启运日期" width="130" align="center">
            <template #default="{ row, $index }">
              <el-date-picker
                  v-model="row.requiredDepartureDate"
                  type="date"
                  size="small"
                  placeholder="启运日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  :disabled="isViewMode"
                  style="width: 100%"
              />
            </template>
          </el-table-column>

          <el-table-column label="要求到达日期" width="130" align="center">
            <template #default="{ row, $index }">
              <el-date-picker
                  v-model="row.requiredArrivalDate"
                  type="date"
                  size="small"
                  placeholder="到达日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  :disabled="isViewMode"
                  style="width: 100%"
              />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" align="center" fixed="right" v-if="!isViewMode">
            <template #default="{ row, $index }">
              <el-button
                  type="danger"
                  size="small"
                  @click="removeLogistics($index)"
                  :disabled="isViewMode"
              >
                <el-icon>
                  <Delete/>
                </el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-empty v-else description="暂无运输环节信息" :image-size="80"/>
      </div>
    </div>

    <!-- 中转港信息 -->
    <div class="form-section">
      <div class="section-header">
        <h4 class="section-title">
          <el-icon>
            <Position/>
          </el-icon>
          中转港信息
        </h4>
        <el-button
            type="primary"
            size="small"
            @click="addTransitPort"
            :disabled="isViewMode"
        >
          <el-icon>
            <Plus/>
          </el-icon>
          添加中转港
        </el-button>
      </div>

      <div class="section-content">
        <el-table
            :data="formData.bookingTransitPortList"
            border
            style="width: 100%"
            v-if="formData.bookingTransitPortList.length > 0"
        >
          <el-table-column label="序号" width="80" align="center">
            <template #default="{ row, $index }">
              <span>{{ $index + 1 }}</span>
            </template>
          </el-table-column>

          <el-table-column label="中转港" min-width="200" align="center">
            <template #default="{ row, $index }">
              <el-select
                  v-model="row.transitPortId"
                  size="small"
                  filterable
                  placeholder="请选择中转港"
                  style="width: 100%"
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  @change="handleTransitPortChange(row)"
              >
                <el-option
                    v-for="port in portOptions"
                    :key="port.value"
                    :label="port.label"
                    :value="port.value"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="代理" min-width="200" align="center">
            <template #default="{ row, $index }">
              <el-select
                  v-model="row.agentId"
                  size="small"
                  filterable
                  placeholder="请选择代理"
                  style="width: 100%"
                  default-first-option
                  clearable
                  :disabled="isViewMode"
                  :automatic-dropdown="true"
                  :reserve-keyword="false"
                  :no-data-text="'暂无数据'"
                  @change="handleTransitAgentChange(row)"
              >
                <el-option
                    v-for="agent in agentOptions"
                    :key="agent.value"
                    :label="agent.label"
                    :value="agent.value"
                >
                  <span style="float: left">{{ agent.label }}</span>
                  <span
                      v-if="agent.routeName"
                      style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                  >
                    {{ agent.routeName }}
                  </span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="80" align="center" v-if="!isViewMode">
            <template #default="{ row, $index }">
              <el-button
                  type="danger"
                  size="small"
                  @click="removeTransitPort($index)"
              >
                <el-icon>
                  <Delete/>
                </el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-empty v-else description="暂无中转港信息" :image-size="80"/>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * 订舱面板组件 - 单证员工作台V2核心组件
 * 负责订舱委托的基础信息、柜量信息、运输环节、中转港信息的录入和管理
 *
 * 主要功能：
 * 1. 基础信息：订舱号、托运单位、起运地、目的地、时间信息等
 * 2. 柜量信息：集装箱规格、数量、重量、危险品、冷藏等信息管理
 * 3. 运输环节：多段运输的供应商、起止地、时间要求等信息
 * 4. 中转港信息：中转港及代理信息管理
 *
 * 特色功能：
 * - 支持用户自定义输入起运地和目的地（allow-create）
 * - 智能联动：起运地自动关联装货码头，目的地自动关联卸货码头
 * - 重量自动计算：单箱重量 × 箱量 = 总重
 * - 条件禁用：危险品等级仅在危险品开关开启时可选
 */

import {computed} from 'vue'
import {
  InfoFilled,
  Box,
  Plus,
  Delete,
  Position,
  Van
} from '@element-plus/icons-vue'

// 导入若依字典工具
import {useDict} from '@/utils/dict'

// 导入时间工具
import {dateShortcuts} from '@/utils/date'

// 导入业务选项composables - 遵循分散式架构
import {useCustomerOptions} from '@/composables/useCustomerOptions'
import {usePortOptions} from '@/composables/usePortOptions'
import {useTerminalOptions} from '@/composables/useTerminalOptions'
import {useAgentOptions} from '@/composables/useAgentOptions'
import {useSupplierOptions} from '@/composables/useSupplierOptions'
import {useContainerTypeOptions} from '@/composables/useContainerTypeOptions'
import {useContainerSizeOptions} from '@/composables/useContainerSizeOptions'
import {useCargoTypeOptions} from '@/composables/useCargoTypeOptions'
import {useDangerLevelOptions} from '@/composables/useDangerLevelOptions'

// 组件属性定义
const props = defineProps({
  /** 表单数据对象，包含订舱主信息、柜量、运输环节、中转港等子表数据 */
  formData: {
    type: Object,
    required: true
  },
  /** 组件模式：add-新增, edit-编辑, view-查看 */
  mode: {
    type: String,
    default: 'add'
  }
})

// 组件事件定义
const emit = defineEmits(['container-change'])

// 若依系统字典数据
const {
  shipping_transport_mode,      // 运输模式字典（集装箱运输模式）
  shipping_business_type,       // 运输方式字典（水路/陆路/航空）
  shipping_customs_type,        // 报关类型字典
  shipping_settlement_method,   // 结算方式字典
  shipping_consortium,          // 所属共同体字典
  shipping_consignment_source   // 委托来源字典
} = useDict(
    'shipping_transport_mode',
    'shipping_business_type',
    'shipping_customs_type',
    'shipping_settlement_method',
    'shipping_consortium',
    'shipping_consignment_source'
)

// 业务选项composables - 保持响应式，只导入需要的方法
const {customerOptions, searchCustomerOptions, getCustomerName, loading} = useCustomerOptions()
const {portOptions, getPortName} = usePortOptions()
const {terminalOptions, getTerminalName} = useTerminalOptions()
const {agentOptions, getAgentName} = useAgentOptions()
const {supplierOptions, getSupplierName} = useSupplierOptions()

// 容器相关选项composables
const {containerTypeOptions} = useContainerTypeOptions()
const {containerSizeOptions} = useContainerSizeOptions()
const {cargoTypeOptions} = useCargoTypeOptions()
const {dangerLevelOptions} = useDangerLevelOptions()

// 计算属性
/** 是否为查看模式 */
const isViewMode = computed(() => props.mode === 'view')

/**
 * 起运地显示值计算属性
 * 支持用户自定义输入，优先显示ID匹配的选项，否则显示用户输入的名称
 */
const originLocationDisplayValue = computed({
  get() {
    const {originLocationId, originLocationName} = props.formData.bookingMain
    // 如果有ID，返回ID用于el-select匹配选项
    if (originLocationId) {
      return originLocationId
    }
    // 如果只有name（用户自创），返回name作为显示值
    if (originLocationName) {
      return originLocationName
    }
    return ''
  },
  set(value) {
    // 处理选择事件，value可能是选项ID或用户输入的字符串
    handleOriginLocationChange(value)
  }
})

/**
 * 目的地显示值计算属性
 * 支持用户自定义输入，优先显示ID匹配的选项，否则显示用户输入的名称
 */
const destinationLocationDisplayValue = computed({
  get() {
    const {destinationLocationId, destinationLocationName} = props.formData.bookingMain
    // 如果有ID，返回ID用于el-select匹配选项
    if (destinationLocationId) {
      return destinationLocationId
    }
    // 如果只有name（用户自创），返回name作为显示值
    if (destinationLocationName) {
      return destinationLocationName
    }
    return ''
  },
  set(value) {
    // 处理选择事件，value可能是选项ID或用户输入的字符串
    handleDestinationLocationChange(value)
  }
})

// 工具函数：选择器通用处理逻辑

/**
 * 通用选择器变更处理
 * @param {string} value - 选择的值
 * @param {Function} getNameFn - 获取名称的函数
 * @param {Object} target - 目标对象
 * @param {string} idField - ID字段名
 * @param {string} nameField - 名称字段名
 */
const handleSelectChange = (value, getNameFn, target, idField, nameField) => {
  try {
    if (value && getNameFn && target) {
      target[idField] = value
      target[nameField] = getNameFn(value) || ''
    } else if (!value) {
      // 清空选择时，清空对应字段
      if (target) {
        target[idField] = null
        target[nameField] = ''
      }
    }
  } catch (error) {
    console.warn('选择器变更处理出错:', error, {value, idField, nameField})
    // 出错时设置安全的默认值
    if (target) {
      target[idField] = value || null
      target[nameField] = ''
    }
  }
}

/**
 * 码头选择器变更处理（支持用户自创选项）
 * @param {string} value - 选择的值
 * @param {Object} target - 目标对象
 * @param {string} idField - ID字段名
 * @param {string} nameField - 名称字段名
 * @param {Object} relatedTerminal - 关联码头对象（可选）
 * @param {string} relatedIdField - 关联ID字段名（可选）
 * @param {string} relatedNameField - 关联名称字段名（可选）
 */
const handleTerminalSelectChange = (value, target, idField, nameField, relatedTerminal = null, relatedIdField = '', relatedNameField = '') => {
  try {
    if (!target) {
      console.warn('码头选择器变更处理：目标对象不存在')
      return
    }

    const terminal = terminalOptions.value.find(t => t.value === value)
    if (terminal) {
      // 选择了现有的terminal选项
      target[idField] = terminal.value
      target[nameField] = terminal.label

      // 如果有关联码头，同时更新关联码头信息
      if (relatedTerminal && relatedIdField && relatedNameField) {
        relatedTerminal[relatedIdField] = terminal.value
        relatedTerminal[relatedNameField] = terminal.label
      }
    } else if (value) {
      // 用户自创选项，只有name有值，id为空
      target[idField] = null
      target[nameField] = String(value)

      // 清空关联码头选择
      if (relatedTerminal && relatedIdField && relatedNameField) {
        relatedTerminal[relatedIdField] = null
        relatedTerminal[relatedNameField] = ''
      }
    } else {
      // 清空选择
      target[idField] = null
      target[nameField] = ''

      // 清空关联码头选择
      if (relatedTerminal && relatedIdField && relatedNameField) {
        relatedTerminal[relatedIdField] = null
        relatedTerminal[relatedNameField] = ''
      }
    }
  } catch (error) {
    console.warn('码头选择器变更处理出错:', error, {value, idField, nameField})
    // 出错时设置安全的默认值
    if (target) {
      target[idField] = value || null
      target[nameField] = String(value || '')
    }
  }
}

// 选择器变更事件处理方法

/**
 * 托运单位选择变更处理
 * 根据选择的客户ID自动填充客户名称
 */
const handleShipperChange = (value) => {
  handleSelectChange(value, getCustomerName,
      props.formData.bookingMain, 'shipperId', 'shipperName')
}

/**
 * 起运地变更处理 - 支持用户自创选项
 * 智能联动：选择码头时自动关联到装货码头
 */
const handleOriginLocationChange = (value) => {
  handleTerminalSelectChange(value,
      props.formData.bookingMain, 'originLocationId', 'originLocationName',
      props.formData.bookingMain, 'loadingTerminalId', 'loadingTerminalName')
}

/**
 * 目的地变更处理 - 支持用户自创选项
 * 智能联动：选择码头时自动关联到卸货码头
 */
const handleDestinationLocationChange = (value) => {
  handleTerminalSelectChange(value,
      props.formData.bookingMain, 'destinationLocationId', 'destinationLocationName',
      props.formData.bookingMain, 'unloadingTerminalId', 'unloadingTerminalName')
}

/**
 * 装货码头变更处理
 * 仅更新装货码头信息，不影响起运地选择
 */
const handleLoadingTerminalChange = (value) => {
  handleSelectChange(value, getTerminalName,
      props.formData.bookingMain, 'loadingTerminalId', 'loadingTerminalName')
}

/**
 * 卸货码头变更处理
 * 仅更新卸货码头信息，不影响目的地选择
 */
const handleUnloadingTerminalChange = (value) => {
  handleSelectChange(value, getTerminalName,
      props.formData.bookingMain, 'unloadingTerminalId', 'unloadingTerminalName')
}

/**
 * 装货代理变更处理
 */
const handleLoadingAgentChange = (value) => {
  handleSelectChange(value, getAgentName,
      props.formData.bookingMain, 'loadingAgentId', 'loadingAgentName')
}

/**
 * 卸货代理变更处理
 */
const handleUnloadingAgentChange = (value) => {
  handleSelectChange(value, getAgentName,
      props.formData.bookingMain, 'unloadingAgentId', 'unloadingAgentName')
}

/**
 * 中转港变更处理
 */
const handleTransitPortChange = (row) => {
  handleSelectChange(row.transitPortId, getPortName,
      row, 'transitPortId', 'transitPortName')
}

/**
 * 中转港代理变更处理
 */
const handleTransitAgentChange = (row) => {
  handleSelectChange(row.agentId, getAgentName,
      row, 'agentId', 'agentName')
}

// 表格数据管理工具函数

/**
 * 创建新的表格行默认数据
 * @param {string} type - 表格类型：container, logistics, transitPort
 * @param {Object} customData - 自定义数据（可选）
 * @returns {Object} 新行数据
 */
const createNewRowData = (type, customData = {}) => {
  const baseData = {
    id: null,
    bookingId: null,
    createBy: '',
    createTime: null,
    updateBy: '',
    updateTime: null,
    delFlag: '0',
    version: 1
  }

  const typeDefaults = {
    // 对应数据库表 booking_cntr_num - 柜量信息表
    container: {
      // 容器尺寸字段组（关联BASIC_CNTR_SIZE表）
      containerSizeId: null,      // container_size_id - 尺寸ID
      containerSizeCode: '20',    // container_size_code - 尺寸代码（如"20", "40"）
      containerSizeName: "20'",   // container_size_name - 尺寸显示名称（如"20'", "40'"）
      
      // 容器类型字段组（关联BASIC_CNTR_TYPE表）
      containerTypeId: null,      // container_type_id - 箱型ID
      containerTypeCode: 'GP',    // container_type_code - 箱型代码（如"GP", "HC"）
      containerTypeName: '普通柜GP', // container_type_name - 箱型显示名称
      
      isEmpty: '0',               // is_empty - 重吉：0重箱 1吉箱
      quantity: 1,                // quantity - 箱量
      isDangerous: '0',           // is_dangerous - 是否危险品：0否 1是
      
      // 危险品等级字段组（关联BASIC_DANGEROUS_LEVEL表）
      dangerousLevelId: '',       // dangerous_level_id - 危险品等级ID
      dangerousLevelCode: '',     // dangerous_level_code - 危险品等级代码
      dangerousLevelName: '',     // dangerous_level_name - 危险品等级名称
      
      isRefrigerated: '0',        // is_refrigerated - 是否冷藏：0否 1是
      
      // 货类字段组（关联BASIC_CARGO_TYPE表）
      cargoTypeId: '',           // cargo_type_id - 货类ID
      cargoTypeCode: '',         // cargo_type_code - 货类代码
      cargoTypeName: '',         // cargo_type_name - 货类名称
      singleWeight: 0,           // single_weight - 单箱重量(KG)
      totalWeight: 0,            // total_weight - 总重(KG)
      isOversize: '0',           // is_oversize - 是否超限：0否 1是
      oversizeDimensions: '',    // oversize_dimensions - 超限尺寸
      remark: ''                 // remark - 备注
    },
    // 对应数据库表 logistics_main - 物流主表（运输环节）
    logistics: {
      businessType: 'WATERWAY',      // business_type - 运输方式：WATERWAY(水路)/ROADWAY(陆路)/AIRWAY(航空)
      supplierId: null,              // supplier_id - 供应商ID
      supplierName: '',              // supplier_name - 供应商名称
      originLocationId: null,        // origin_location_id - 启运地ID
      originLocationName: '',        // origin_location_name - 启运地名称
      destinationLocationId: null,   // destination_location_id - 目的地ID
      destinationLocationName: '',   // destination_location_name - 目的地名称
      containerDetails: '',          // container_details - 箱量明细（JSON格式）
      termsConditions: '',           // terms_conditions - 条款
      requiredDepartureDate: null,   // required_departure_date - 要求启运日期
      requiredArrivalDate: null      // required_arrival_date - 要求到达日期
    },
    // 对应数据库表 booking_transit_port - 中转港表
    transitPort: {
      sequenceNo: 1,             // sequence_no - 序号
      transitPortId: null,       // transit_port_id - 中转港ID
      transitPortName: '',       // transit_port_name - 中转港名称
      agentId: null,            // agent_id - 代理ID
      agentName: '',            // agent_name - 代理名称
      remark: ''                // remark - 备注信息
    }
  }

  return {...baseData, ...typeDefaults[type], ...customData}
}

/**
 * 安全删除表格行
 * @param {Array} list - 目标数组
 * @param {number} index - 删除索引
 * @param {number} minLength - 最小保留长度
 * @param {Function} callback - 删除后回调函数
 */
const safeRemoveRow = (list, index, minLength = 0, callback = null) => {
  try {
    if (!Array.isArray(list)) {
      console.warn('safeRemoveRow: 传入的不是数组类型', list)
      return
    }

    const numIndex = Number(index)
    if (isNaN(numIndex) || numIndex < 0 || numIndex >= list.length) {
      console.warn('safeRemoveRow: 无效的索引值', {index, numIndex, listLength: list.length})
      return
    }

    if (list.length > minLength) {
      list.splice(numIndex, 1)
      callback && typeof callback === 'function' && callback()
    } else {
      console.warn('safeRemoveRow: 数组长度已达到最小限制', {currentLength: list.length, minLength})
    }
  } catch (error) {
    console.error('safeRemoveRow: 删除操作失败', error, {index, minLength, listLength: list?.length})
  }
}

// 柜量信息管理方法

/**
 * 添加新的柜量记录
 * 默认添加20GP重箱，数量为1
 */
const addContainer = () => {
  const newContainer = createNewRowData('container')
  props.formData.bookingCntrNumList.push(newContainer)
}

/**
 * 删除柜量记录
 * 至少保留一条记录
 */
const removeContainer = (index) => {
  safeRemoveRow(props.formData.bookingCntrNumList, index, 1, handleContainerChange)
}

/**
 * 计算行总重量
 * 单箱重量 × 箱量 = 总重量
 */
const calculateRowTotalWeight = (row) => {
  const singleWeight = Number(row.singleWeight || 0)
  const quantity = Number(row.quantity || 0)
  row.totalWeight = singleWeight * quantity
  handleContainerChange()
}

/**
 * 柜量信息变更事件处理
 * 触发父组件的container-change事件
 */
const handleContainerChange = () => {
  emit('container-change')
}

// 中转港信息管理方法

/**
 * 添加新的中转港记录
 */
const addTransitPort = () => {
  // 计算下一个序号（按中转先后顺序）
  const nextSequenceNo = (props.formData.bookingTransitPortList?.length || 0) + 1
  const newTransitPort = createNewRowData('transitPort', {sequenceNo: nextSequenceNo})
  props.formData.bookingTransitPortList.push(newTransitPort)
}

/**
 * 删除中转港记录
 */
const removeTransitPort = (index) => {
  safeRemoveRow(props.formData.bookingTransitPortList, index)
}

// 运输环节管理方法

/**
 * 添加新的运输环节记录
 * 默认为内贸业务类型
 */
const addLogistics = () => {
  // 确保数组存在
  if (!props.formData.logisticsMainList) {
    props.formData.logisticsMainList = []
  }

  const newLogistics = createNewRowData('logistics')
  props.formData.logisticsMainList.push(newLogistics)
}

/**
 * 删除运输环节记录
 */
const removeLogistics = (index) => {
  if (props.formData.logisticsMainList) {
    safeRemoveRow(props.formData.logisticsMainList, index)
  }
}

/**
 * 运输环节供应商变更处理
 */
const handleSupplierChange = (row) => {
  handleSelectChange(row.supplierId, getSupplierName,
      row, 'supplierId', 'supplierName')
}

/**
 * 运输环节起运地变更处理
 */
const handleLogisticsOriginChange = (row) => {
  handleSelectChange(row.originLocationId, getTerminalName,
      row, 'originLocationId', 'originLocationName')
}

/**
 * 运输环节目的地变更处理
 */
const handleLogisticsDestinationChange = (row) => {
  handleSelectChange(row.destinationLocationId, getTerminalName,
      row, 'destinationLocationId', 'destinationLocationName')
}

/**
 * 容器尺寸选择变更处理
 * 根据选择的尺寸ID同步保存ID、CODE、NAME三个字段
 */
const handleContainerSizeChange = (row, value) => {
  try {
    if (value) {
      const sizeOption = containerSizeOptions.value.find(opt => opt.value === value)
      if (sizeOption) {
        row.containerSizeId = sizeOption.value          // 存储ID
        row.containerSizeCode = sizeOption.sizeCode     // 存储代码
        row.containerSizeName = sizeOption.sizeName     // 存储显示名称
      }
    } else {
      // 清空选择时，清空所有相关字段
      row.containerSizeId = null
      row.containerSizeCode = ''
      row.containerSizeName = ''
    }
    
    // 触发容器变更事件
    handleContainerChange()
  } catch (error) {
    console.warn('容器尺寸选择处理出错:', error, {value})
  }
}

/**
 * 容器类型选择变更处理
 * 根据选择的类型ID同步保存ID、CODE、NAME三个字段
 */
const handleContainerTypeChange = (row, value) => {
  try {
    if (value) {
      const typeOption = containerTypeOptions.value.find(opt => opt.value === value)
      if (typeOption) {
        row.containerTypeId = typeOption.value          // 存储ID
        row.containerTypeCode = typeOption.typeCode     // 存储代码
        row.containerTypeName = typeOption.typeName     // 存储显示名称
      }
    } else {
      // 清空选择时，清空所有相关字段
      row.containerTypeId = null
      row.containerTypeCode = ''
      row.containerTypeName = ''
    }
    
    // 触发容器变更事件
    handleContainerChange()
  } catch (error) {
    console.warn('容器类型选择处理出错:', error, {value})
  }
}

/**
 * 危险品等级选择变更处理
 * 根据选择的等级ID同步保存ID、CODE、NAME三个字段
 */
const handleDangerousLevelChange = (row, value) => {
  try {
    if (value) {
      const levelOption = dangerLevelOptions.value.find(opt => opt.value === value)
      if (levelOption) {
        row.dangerousLevelId = levelOption.value
        row.dangerousLevelCode = levelOption.code || levelOption.value
        row.dangerousLevelName = levelOption.label || levelOption.name
      }
    } else {
      // 清空时清除所有相关字段
      row.dangerousLevelId = ''
      row.dangerousLevelCode = ''
      row.dangerousLevelName = ''
    }
    
    // 触发容器变更事件
    handleContainerChange()
  } catch (error) {
    console.warn('危险品等级选择处理出错:', error, {value})
  }
}

/**
 * 货类选择变更处理
 * 根据选择的货类ID同步保存ID、CODE、NAME三个字段
 */
const handleCargoTypeChange = (row, value) => {
  try {
    if (value) {
      const cargoOption = cargoTypeOptions.value.find(opt => opt.value === value)
      if (cargoOption) {
        row.cargoTypeId = cargoOption.value
        row.cargoTypeCode = cargoOption.code || cargoOption.value
        row.cargoTypeName = cargoOption.label || cargoOption.name
      }
    } else {
      // 清空时清除所有相关字段
      row.cargoTypeId = ''
      row.cargoTypeCode = ''
      row.cargoTypeName = ''
    }
    
    // 触发容器变更事件
    handleContainerChange()
  } catch (error) {
    console.warn('货类选择处理出错:', error, {value})
  }
}
</script>

<style scoped>
.booking-panel {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0;
  padding-bottom: 12px;
}

.form-section {
  margin-bottom: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.section-content {
  padding: 16px;
}

.container-table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.container-table {
  min-width: 1200px;
}

.container-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.container-table :deep(.el-table__body-wrapper) {
  max-height: 250px;
  overflow-y: auto;
}

/* 表单元素样式优化 */
.el-form-item {
  margin-bottom: 12px;
}

.el-input-number {
  width: 100%;
}

.el-select {
  width: 100%;
}

.el-date-editor {
  width: 100%;
}

.el-switch {
  --el-switch-on-color: var(--el-color-primary);
}

.el-empty {
  padding: 24px 16px;
}

/* 进一步紧凑化表单控件 */
.booking-panel :deep(.el-input__wrapper) {
  min-height: 28px;
}

.booking-panel :deep(.el-select .el-input__wrapper) {
  min-height: 28px;
}

.booking-panel :deep(.el-date-editor.el-input) {
  height: 28px;
}

.booking-panel :deep(.el-date-editor .el-input__wrapper) {
  min-height: 28px;
}

.booking-panel :deep(.el-radio) {
  margin-right: 16px;
}

.booking-panel :deep(.el-radio__label) {
  font-size: 12px;
}

.booking-panel :deep(.el-textarea__inner) {
  font-size: 12px;
}

/* 表格紧凑化 */
.booking-panel :deep(.el-table th.el-table__cell) {
  padding: 8px 0;
  font-size: 12px;
}

.booking-panel :deep(.el-table td.el-table__cell) {
  padding: 6px 4px;
}

.booking-panel :deep(.el-table--small .el-table__cell) {
  padding: 4px 2px;
}

.booking-panel :deep(.el-button--small) {
  padding: 4px 8px;
  font-size: 11px;
}

/* 表格内输入数字框样式优化 */
.booking-panel :deep(.el-input-number) {
  width: 100%;
}

.booking-panel :deep(.el-input-number .el-input__wrapper) {
  padding: 0 4px;
}

.booking-panel :deep(.el-input-number .el-input-number__decrease),
.booking-panel :deep(.el-input-number .el-input-number__increase) {
  width: 24px;
  height: 24px;
  font-size: 12px;
  border: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.booking-panel :deep(.el-input-number .el-input-number__decrease:hover),
.booking-panel :deep(.el-input-number .el-input-number__increase:hover) {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .section-content {
    padding: 16px;
  }
}

/* 滚动条样式 */
.container-table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.container-table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.container-table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.container-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 客户选项样式 */
.customer-option-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.customer-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.customer-extra {
  color: #999;
  font-size: 12px;
  white-space: nowrap;
  margin-left: 8px;
  flex-shrink: 0;
}
</style>