# ojdbc8.jar 使用说明

## 1. 说明

本项目依赖 Oracle 数据库驱动 `ojdbc8.jar`，由于授权原因，**该依赖无法通过公开Maven仓库自动下载**，需要开发者手动安装到本地Maven仓库。

驱动包已放置于本目录下：  
`SHIPPING-SPRING3/doc/ojdbc8/ojdbc8.jar`

---

## 2. 手动安装到本地Maven仓库

### Windows系统
请在命令行中执行以下命令：
```shell

mvn install:install-file -DgroupId=com.oracle -DartifactId=ojdbc -Dversion=8 -Dpackaging=jar -Dfile=SHIPPING-SPRING3\doc\ojdbc8\ojdbc8.jar
```

### Linux/macOS系统
请在终端中执行以下命令：
```bash
mvn install:install-file -DgroupId=com.oracle -DartifactId=ojdbc -Dversion=8 -Dpackaging=jar -Dfile=SHIPPING-SPRING3/doc/ojdbc8/ojdbc8.jar


```

> **注意：**  
> - 请在项目根目录下执行命令
> - 路径请根据实际存放位置调整

---

## 3. pom.xml 依赖配置

项目已在 `pom.xml` 中配置如下依赖，无需修改：

```xml
<dependency>
    <groupId>com.oracle</groupId>
    <artifactId>ojdbc</artifactId>
    <version>8</version>
</dependency>
```

---

## 4. 常见问题

- **依赖找不到/构建失败？**  
  请确认已正确执行第2步命令，并且路径无误。
- **如有其他问题，请联系项目维护者。**
