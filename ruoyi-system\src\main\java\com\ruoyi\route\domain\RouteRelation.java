package com.ruoyi.route.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 码头连接关系对象 route_relation
 */
@Data
@Table("ROUTE_RELATION")
@EqualsAndHashCode(callSuper = true)
public class RouteRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 连接ID */
    @Id(keyType = KeyType.Generator, value = "snowFlakeId")
    @Excel(name = "连接ID")
    private String relationId;

    /** 关联支线表ID */
    @Excel(name = "关联支线表ID")
    private String branchId;

    /** 关联主线表ID */
    @Excel(name = "关联主线表ID")
    @Column(ignore = true)
    private String mainId;

    /** 起点码头ID */
    @Excel(name = "起点码头ID")
    private String startPointId;

    /** 终点码头ID */
    @Excel(name = "终点码头ID")
    private String endPointId;

    /** 距离（公里） */
    @Excel(name = "距离（公里）")
    private BigDecimal distanceKm;

    /** 时间（小时） */
    @Excel(name = "时间（小时）")
    private BigDecimal durationHour;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 乐观锁 */
    private Long version;

    /** 主线名称 */
    @Excel(name = "主线名称")
    @Column(ignore = true)
    private String mainName;

    /** 支线名称 */
    @Excel(name = "支线名称")
    @Column(ignore = true)
    private String branchName;

    /** 起点码头名称 */
    @Excel(name = "起点码头名称")
    @Column(ignore = true)
    private String startPointName;

    /** 终点码头名称 */
    @Excel(name = "终点码头名称")
    @Column(ignore = true)
    private String endPointName;

    
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("relationId", getRelationId())
                .append("branchId", getBranchId())
                .append("mainId", getMainId())
                .append("startPointId", getStartPointId())
                .append("endPointId", getEndPointId())
                .append("distanceKm", getDistanceKm())
                .append("durationHour", getDurationHour())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("version", getVersion())
                .append("mainName", getMainName())
                .append("branchName", getBranchName())
                .append("startPointName", getStartPointName())
                .append("endPointName", getEndPointName())
                .toString();
    }
} 