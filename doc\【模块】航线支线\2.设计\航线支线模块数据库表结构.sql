-- 航线支线模块数据库表结构
-- 创建时间：2025年6月4日

-- 1. 创建航线主表
CREATE TABLE ROUTE_MAIN (
    MAIN_ID VARCHAR2(32) NOT NULL,
    MAIN_NAME VARCHAR2(255) NOT NULL,
    REMARK VARCHAR2(255),
    CREATE_BY VARCHAR2(50),
    CREATE_TIME DATE,
    UPDATE_BY VARCHAR2(50),
    UPDATE_TIME DATE,
    DEL_FLAG VARCHAR2(1) DEFAULT '0',
    VERSION NUMBER(10) DEFAULT 0,
    CONSTRAINT PK_ROUTE_MAIN PRIMARY KEY (MAIN_ID)
);

-- 2. 创建支线表
CREATE TABLE ROUTE_BRANCH (
    BRANCH_ID VARCHAR2(32) NOT NULL,
    BRANCH_NAME VARCHAR2(255) NOT NULL,
    MAIN_ID VARCHAR2(32) NOT NULL,
    REMARK VARCHAR2(255),
    CREATE_BY VARCHAR2(50),
    CREATE_TIME DATE,
    UPDATE_BY VARCHAR2(50),
    UPDATE_TIME DATE,
    DEL_FLAG VARCHAR2(1) DEFAULT '0',
    VERSION NUMBER(10) DEFAULT 0,
    CONSTRAINT PK_ROUTE_BRANCH PRIMARY KEY (BRANCH_ID),
    CONSTRAINT FK_BRANCH_MAIN FOREIGN KEY (MAIN_ID) 
        REFERENCES ROUTE_MAIN(MAIN_ID)
);

-- 3. 创建码头连接关系表
CREATE TABLE ROUTE_RELATION (
    RELATION_ID VARCHAR2(32) NOT NULL,
    BRANCH_ID VARCHAR2(32) NOT NULL,
    START_POINT_ID VARCHAR2(50) NOT NULL,
    END_POINT_ID VARCHAR2(50) NOT NULL,
    DISTANCE_KM NUMBER(10,2),
    DURATION_HOUR NUMBER(10,2),
    REMARK VARCHAR2(255),
    CREATE_BY VARCHAR2(50),
    CREATE_TIME DATE,
    UPDATE_BY VARCHAR2(50),
    UPDATE_TIME DATE,
    DEL_FLAG VARCHAR2(1) DEFAULT '0',
    VERSION NUMBER(10) DEFAULT 0,
    CONSTRAINT PK_ROUTE_RELATION PRIMARY KEY (RELATION_ID),
    CONSTRAINT FK_RELATION_BRANCH FOREIGN KEY (BRANCH_ID) 
        REFERENCES ROUTE_BRANCH(BRANCH_ID),
    CONSTRAINT FK_RELATION_START_PORT FOREIGN KEY (START_POINT_ID) 
        REFERENCES BASIC_PORT_AREA(ID),
    CONSTRAINT FK_RELATION_END_PORT FOREIGN KEY (END_POINT_ID) 
        REFERENCES BASIC_PORT_AREA(ID)
);

-- 添加表注释
COMMENT ON TABLE ROUTE_MAIN IS '航线主线表';
COMMENT ON TABLE ROUTE_BRANCH IS '航线支线表';
COMMENT ON TABLE ROUTE_RELATION IS '码头连接关系表';

-- 添加字段注释
-- ROUTE_MAIN表字段注释
COMMENT ON COLUMN ROUTE_MAIN.MAIN_ID IS '主线ID（主键），雪花ID';
COMMENT ON COLUMN ROUTE_MAIN.MAIN_NAME IS '主线名称';
COMMENT ON COLUMN ROUTE_MAIN.REMARK IS '备注';
COMMENT ON COLUMN ROUTE_MAIN.CREATE_BY IS '创建人';
COMMENT ON COLUMN ROUTE_MAIN.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN ROUTE_MAIN.UPDATE_BY IS '更新人';
COMMENT ON COLUMN ROUTE_MAIN.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN ROUTE_MAIN.DEL_FLAG IS '逻辑删除标志';
COMMENT ON COLUMN ROUTE_MAIN.VERSION IS '乐观锁';

-- ROUTE_BRANCH表字段注释
COMMENT ON COLUMN ROUTE_BRANCH.BRANCH_ID IS '支线ID（主键），雪花ID';
COMMENT ON COLUMN ROUTE_BRANCH.BRANCH_NAME IS '支线名称';
COMMENT ON COLUMN ROUTE_BRANCH.MAIN_ID IS '所属主线ID（外键）';
COMMENT ON COLUMN ROUTE_BRANCH.REMARK IS '备注';
COMMENT ON COLUMN ROUTE_BRANCH.CREATE_BY IS '创建人';
COMMENT ON COLUMN ROUTE_BRANCH.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN ROUTE_BRANCH.UPDATE_BY IS '更新人';
COMMENT ON COLUMN ROUTE_BRANCH.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN ROUTE_BRANCH.DEL_FLAG IS '逻辑删除标志';
COMMENT ON COLUMN ROUTE_BRANCH.VERSION IS '乐观锁';

-- ROUTE_RELATION表字段注释
COMMENT ON COLUMN ROUTE_RELATION.RELATION_ID IS '连接ID（主键），雪花ID';
COMMENT ON COLUMN ROUTE_RELATION.BRANCH_ID IS '关联支线表ID（外键）';
COMMENT ON COLUMN ROUTE_RELATION.START_POINT_ID IS '起点码头ID（外键）';
COMMENT ON COLUMN ROUTE_RELATION.END_POINT_ID IS '终点码头ID（外键）';
COMMENT ON COLUMN ROUTE_RELATION.DISTANCE_KM IS '距离（公里）';
COMMENT ON COLUMN ROUTE_RELATION.DURATION_HOUR IS '时间（小时）';
COMMENT ON COLUMN ROUTE_RELATION.REMARK IS '备注';
COMMENT ON COLUMN ROUTE_RELATION.CREATE_BY IS '创建人';
COMMENT ON COLUMN ROUTE_RELATION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN ROUTE_RELATION.UPDATE_BY IS '更新人';
COMMENT ON COLUMN ROUTE_RELATION.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN ROUTE_RELATION.DEL_FLAG IS '逻辑删除标志';
COMMENT ON COLUMN ROUTE_RELATION.VERSION IS '乐观锁'; 