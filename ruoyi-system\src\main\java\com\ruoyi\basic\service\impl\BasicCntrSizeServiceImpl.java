package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicCntrSize;
import com.ruoyi.basic.mapper.BasicCntrSizeMapper;
import com.ruoyi.basic.service.IBasicCntrSizeService;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCntrSizeTableDef.BASIC_CNTR_SIZE;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicCntrSizeServiceImpl extends ServiceImpl<BasicCntrSizeMapper, BasicCntrSize> implements IBasicCntrSizeService {
    @Autowired
    private BasicCntrSizeMapper basicCntrSizeMapper;

    @Override
    public BasicCntrSizeMapper getMapper() {
        return basicCntrSizeMapper;
    }

    /**
     * 查询箱尺寸
     *
     * @param id 箱尺寸主键
     * @return 箱尺寸
     */
    @Override
    public BasicCntrSize selectBasicCntrSizeById(String id) {
        return basicCntrSizeMapper.selectOneById(id);
    }

    /**
     * 查询箱尺寸列表
     *
     * @param basicCntrSize 箱尺寸
     * @return 箱尺寸
     */
    @Override
    public List<BasicCntrSize> selectBasicCntrSizeList(BasicCntrSize basicCntrSize) {
        return QueryChain.of(BasicCntrSize.class)
                .where(BASIC_CNTR_SIZE.SIZE_CODE.like(basicCntrSize.getSizeCode(), StringUtils::isNotEmpty))
                .and(BASIC_CNTR_SIZE.SIZE_NAME.like(basicCntrSize.getSizeName(), StringUtils::isNotEmpty))
                .list();
    }

    /**
     * 新增箱尺寸
     *
     * @param basicCntrSize 箱尺寸
     * @return 结果
     */
    @Override
    public int insertBasicCntrSize(BasicCntrSize basicCntrSize) {
        return basicCntrSizeMapper.insert(basicCntrSize);
    }

    /**
     * 修改箱尺寸
     *
     * @param basicCntrSize 箱尺寸
     * @return 结果
     */
    @Override
    public int updateBasicCntrSize(BasicCntrSize basicCntrSize) {
        return basicCntrSizeMapper.update(basicCntrSize);
    }

    /**
     * 批量删除箱尺寸
     *
     * @param ids 需要删除的箱尺寸主键集合
     * @return 结果
     */
    @Override
    public int deleteBasicCntrSizeByIds(List<String> ids) {
        return basicCntrSizeMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除箱尺寸信息
     *
     * @param id 箱尺寸主键
     * @return 结果
     */
    @Override
    public int deleteBasicCntrSizeById(String id) {
        return basicCntrSizeMapper.deleteById(id);
    }

    public void check(BasicCntrSize basicCntrSize) throws Exception {
        QueryChain queryChain = QueryChain.of(BasicCntrSize.class)
                .and(BASIC_CNTR_SIZE.ID.ne(basicCntrSize.getId()).when(StringUtils.isNotEmpty(basicCntrSize.getId())))
                .and(BASIC_CNTR_SIZE.SIZE_CODE.eq(basicCntrSize.getSizeCode()));

        long check = basicCntrSizeMapper.selectCountByQuery(queryChain);

        if(check > 0){
            throw new Exception("箱尺寸代码不唯一");
        }
    }

}
