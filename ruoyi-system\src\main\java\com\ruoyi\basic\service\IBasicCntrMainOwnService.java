package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCntrMainOwn;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 自有箱基础信息服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IBasicCntrMainOwnService extends IService<BasicCntrMainOwn> {

    /**
     * 新增自有箱信息
     * 
     * @param basicCntrMainOwn 自有箱信息
     * @return 结果
     */
    AjaxResult addBasicCntrMainOwn(BasicCntrMainOwn basicCntrMainOwn);

    /**
     * 修改自有箱信息
     * 
     * @param basicCntrMainOwn 自有箱信息
     * @return 结果
     */
    AjaxResult editBasicCntrMainOwn(BasicCntrMainOwn basicCntrMainOwn);
}
