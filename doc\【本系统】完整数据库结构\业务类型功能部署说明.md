# 业务类型功能部署说明

## 功能概述
将客户管理模块中的业务类型字段从单行输入框改为多选下拉框，客户状态字段从单选按钮组改为下拉框，并将选中的业务类型值保存到新的关联表 `CUS_BUSINESS_TYPE` 中。同时增强了搜索功能。

## 部署步骤

### 1. 数据库表创建
执行以下SQL文件创建业务类型关联表：
```sql
-- 执行文件：CUS_BUSINESS_TYPE.sql
-- 创建 CUS_BUSINESS_TYPE 表
```

### 2. 字典数据插入
执行以下SQL文件插入业务类型字典数据：
```sql
-- 执行文件：CUS_BUSINESS_TYPE_DICT.sql
-- 插入业务类型字典类型和数据
```

### 3. 代码部署
#### 前端代码
- 修改 `SHIPPING-UI/src/views/customer/cusMain.vue`
  - 引入 `cus_business_type` 字典
  - 将业务类型字段改为多选下拉框
  - 将客户状态字段改为下拉框
  - 修改数据处理逻辑，直接传递数组给后端
  - 修改表格显示，支持显示多个业务类型标签
  - 增强搜索表单，添加客户类型、状态、业务类型搜索条件
  - 为状态字段添加验证规则

#### 后端代码
- 修改 `CusMain` 实体类，将 `businessType` 字段改为 `List<String>` 类型
- 修改 `CusMainServiceImpl`，添加业务类型保存和更新逻辑
- 修改 `selectCusMainList` 方法，为每个客户查询业务类型数据
- 修改 `CusBusinessTypeService` 接口和实现类
- 更新 `CusMainMapper.xml`，支持更多搜索条件

### 4. 功能验证
1. 启动前端和后端服务
2. 进入客户管理页面
3. 验证搜索功能：客户名称、客户类型、客户状态、业务类型
4. 新增或编辑客户时，验证业务类型多选功能
5. 验证客户状态下拉框选择功能
6. 检查数据库中 `CUS_BUSINESS_TYPE` 表是否正确保存数据
7. 验证表格中是否正确显示多个业务类型标签

## 功能特性
- 支持多选业务类型
- 客户状态改为下拉框选择
- 数据持久化到关联表
- 支持新增、修改、删除操作
- 表格显示使用字典标签，支持显示多个标签
- 前端直接传递数组，后端直接处理数组
- 增强的搜索功能，支持多条件搜索

## 数据流程
1. 前端多选下拉框选择业务类型 → 数组
2. 前端下拉框选择客户状态 → 字符串
3. 前端提交时直接传递数据给后端
4. 后端接收数据，保存到相应表
5. 查询时从关联表获取数据，设置到相应字段
6. 前端表格显示多个业务类型标签和状态标签

## 搜索功能
- 客户名称：模糊搜索
- 客户类型：精确匹配
- 客户状态：精确匹配
- 业务类型：多选匹配（支持选择多个业务类型进行搜索，使用AND逻辑，客户必须包含所有指定的业务类型）

## 注意事项
- 确保字典数据已正确插入
- 确保数据库表已创建
- 重启服务以加载新的字典配置
- 前端不再进行数组到字符串的转换，直接传递数组
- 搜索功能中的业务类型支持多选搜索 