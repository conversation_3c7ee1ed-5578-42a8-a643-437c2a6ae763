package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicAncBerth;

import java.util.List;

public interface IBasicAncBerthService extends IService<BasicAncBerth> {
    /**
     * 查询锚位管理
     *
     * @param id 锚位管理主键
     * @return 锚位管理
     */
    public BasicAncBerth selectBasicAncBerthById(String id);

    /**
     * 查询锚位管理列表
     *
     * @param basicAncBerth 锚位管理
     * @return 锚位管理集合
     */
    public List<BasicAncBerth> selectBasicAncBerthList(BasicAncBerth basicAncBerth);

    /**
     * 新增锚位管理
     *
     * @param basicAncBerth 锚位管理
     * @return 结果
     */
    public int insertBasicAncBerth(BasicAncBerth basicAncBerth);

    /**
     * 修改锚位管理
     *
     * @param basicAncBerth 锚位管理
     * @return 结果
     */
    public int updateBasicAncBerth(BasicAncBerth basicAncBerth);

    /**
     * 批量删除锚位管理
     *
     * @param ids 需要删除的锚位管理主键集合
     * @return 结果
     */
    public int deleteBasicAncBerthByIds(List<String> ids);

    /**
     * 删除锚位管理信息
     *
     * @param id 锚位管理主键
     * @return 结果
     */
    public int deleteBasicAncBerthById(String id);
}
