package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCargoType;

import java.util.List;

public interface IBasicCargoTypeService extends IService<BasicCargoType> {

    /**
     * 查询货物基础信息
     *
     * @param id 货物基础信息主键
     * @return 货物基础信息
     */
    public BasicCargoType selectBasicCargoTypeById(String id);

    /**
     * 查询货物基础信息列表
     *
     * @param basicCargoType 货物基础信息
     * @return 货物基础信息集合
     */
    public List<BasicCargoType> selectBasicCargoTypeList(BasicCargoType basicCargoType);

    /**
     * 新增货物基础信息
     *
     * @param basicCargoType 货物基础信息
     * @return 结果
     */
    public int insertBasicCargoType(BasicCargoType basicCargoType) throws Exception;

    /**
     * 修改货物基础信息
     *
     * @param basicCargoType 货物基础信息
     * @return 结果
     */
    public int updateBasicCargoType(BasicCargoType basicCargoType) throws Exception;

    /**
     * 批量删除货物基础信息
     *
     * @param ids 需要删除的货物基础信息主键集合
     * @return 结果
     */
    public boolean deleteBasicCargoTypeByIds(List<String> ids);

    /**
     * 删除货物基础信息信息
     *
     * @param id 货物基础信息主键
     * @return 结果
     */
    public int deleteBasicCargoTypeById(String id);

}
