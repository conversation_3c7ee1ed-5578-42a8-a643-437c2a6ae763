package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicMileage;
import com.ruoyi.basic.mapper.BasicMileageMapper;
import com.ruoyi.basic.service.IBasicMileageService;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicMileageServiceImpl extends ServiceImpl<BasicMileageMapper, BasicMileage> implements IBasicMileageService {
    @Autowired
    private BasicMileageMapper basicMileageMapper;

    /**
     * 查询码头里程
     *
     * @param id 码头里程主键
     * @return 码头里程
     */
    @Override
    public BasicMileage selectBasicMileageById(String id)
    {
        return basicMileageMapper.selectOneById(id);
    }

    /**
     * 查询码头里程列表
     *
     * @param basicMileage 码头里程
     * @return 码头里程
     */
    @Override
    public List<BasicMileage> selectBasicMileageList(BasicMileage basicMileage)
    {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicMileage.getStartTerminalId())) {
            queryWrapper.eq("start_terminal_id", basicMileage.getStartTerminalId());
        }
        if (StringUtils.isNotEmpty(basicMileage.getEndTerminalId())) {
            queryWrapper.eq("end_terminal_id", basicMileage.getEndTerminalId());
        }
        queryWrapper.orderBy("create_time", true);
        return super.list(queryWrapper);
    }

    /**
     * 新增码头里程
     *
     * @param basicMileage 码头里程
     * @return 结果
     */
    @Override
    public int insertBasicMileage(BasicMileage basicMileage) throws Exception {
        checkUnique(basicMileage);
        return basicMileageMapper.insert(basicMileage);
    }

    /**
     * 修改码头里程
     *
     * @param basicMileage 码头里程
     * @return 结果
     */
    @Override
    public int updateBasicMileage(BasicMileage basicMileage) throws Exception {
        checkUnique(basicMileage);
        return basicMileageMapper.update(basicMileage);
    }

    public void checkUnique(BasicMileage basicMileage) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("start_terminal_id", basicMileage.getStartTerminalId())
                .eq("end_terminal_id", basicMileage.getEndTerminalId());
        if (StringUtils.isNotEmpty(basicMileage.getId())) {
            queryWrapper.ne("id", basicMileage.getId());
        }
        long check = super.count(queryWrapper);
        if(check > 0){
            throw new Exception("起始码头和目的码头不唯一");
        }
    }

    /**
     * 批量删除码头里程
     *
     * @param ids 需要删除的码头里程主键
     * @return 结果
     */
    @Override
    public int deleteBasicMileageByIds(List<String> ids)
    {
        return basicMileageMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除码头里程信息
     *
     * @param id 码头里程主键
     * @return 结果
     */
    @Override
    public int deleteBasicMileageById(String id)
    {
        return basicMileageMapper.deleteById(id);
    }
}
