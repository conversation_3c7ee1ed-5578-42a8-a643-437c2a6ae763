package com.ruoyi.common.core.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.StringUtils;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class ElementPlusTreeSelect {

    private Long value;

    private String label;

    /** 节点禁用 */
    private boolean disabled = false;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ElementPlusTreeSelect> children;

    public ElementPlusTreeSelect(){

    }

    public ElementPlusTreeSelect(SysDept dept){
        this.value = dept.getDeptId();
        this.label = dept.getDeptName();
        this.disabled = StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus());
        this.children = dept.getChildren().stream().map(ElementPlusTreeSelect::new).collect(Collectors.toList());
    }

}
