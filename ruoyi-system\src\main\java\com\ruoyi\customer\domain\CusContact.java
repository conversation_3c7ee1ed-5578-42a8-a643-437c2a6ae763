package com.ruoyi.customer.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "CUS_CONTACT")
public class CusContact extends BaseEntity implements Serializable {

    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String cusContactId;

    @Column(value = "CUS_MAIN_ID")
    private String cusMainId;

    @Column(value = "ADDRESS")
    private String address;

    @Column(value = "NAME")
    private String name;

    @Column(value = "PHONE_NUMBER")
    private String phoneNumber;

    @Column(value = "MAIL_ADDRESS")
    private String mailAddress;


    @Column(isLogicDelete = true)
    private String delFlag;


}
