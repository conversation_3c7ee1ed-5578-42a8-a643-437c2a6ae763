-- ----------------------------
-- 业务类型字典数据
-- ----------------------------

-- 插入字典类型
INSERT INTO "SHIPPING"."SYS_DICT_TYPE" ("DICT_ID", "DICT_NAME", "DICT_TYPE", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (100, '业务类型', 'cus_business_type', '0', 'admin', SYSDATE, '', NULL, '客户业务类型字典');

-- 插入字典数据
INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1001, 1, '集装箱运输', 'container', 'cus_business_type', '', 'primary', 'Y', '0', 'admin', SYSDATE, '', NULL, '集装箱运输业务');

INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1002, 2, '散货运输', 'bulk', 'cus_business_type', '', 'success', 'N', '0', 'admin', SYSDATE, '', NULL, '散货运输业务');

INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1003, 3, '危险品运输', 'dangerous', 'cus_business_type', '', 'warning', 'N', '0', 'admin', SYSDATE, '', NULL, '危险品运输业务');

INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1004, 4, '冷藏运输', 'refrigerated', 'cus_business_type', '', 'info', 'N', '0', 'admin', SYSDATE, '', NULL, '冷藏运输业务');

INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1005, 5, '超重货物', 'overweight', 'cus_business_type', '', 'danger', 'N', '0', 'admin', SYSDATE, '', NULL, '超重货物运输业务');

INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1006, 6, '件杂货', 'general', 'cus_business_type', '', 'default', 'N', '0', 'admin', SYSDATE, '', NULL, '件杂货运输业务');

INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1007, 7, '滚装运输', 'ro_ro', 'cus_business_type', '', 'primary', 'N', '0', 'admin', SYSDATE, '', NULL, '滚装运输业务');

INSERT INTO "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE", "DICT_SORT", "DICT_LABEL", "DICT_VALUE", "DICT_TYPE", "CSS_CLASS", "LIST_CLASS", "IS_DEFAULT", "STATUS", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "REMARK") 
VALUES (1008, 8, '其他', 'other', 'cus_business_type', '', 'default', 'N', '0', 'admin', SYSDATE, '', NULL, '其他运输业务'); 