package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicZoneTerminal;

import java.util.List;

public interface IBasicZoneTerminalService extends IService<BasicZoneTerminal> {
    /**
     * 查询关区码头关联
     *
     * @param id 关区码头关联主键
     * @return 关区码头关联
     */
    public BasicZoneTerminal selectBasicZoneTerminalById(String id);

    /**
     * 查询关区码头关联列表
     *
     * @param basicZoneTerminal 关区码头关联
     * @return 关区码头关联集合
     */
    public List<BasicZoneTerminal> selectBasicZoneTerminalList(BasicZoneTerminal basicZoneTerminal);

    /**
     * 新增关区码头关联
     *
     * @param basicZoneTerminal 关区码头关联
     * @return 结果
     */
    public int insertBasicZoneTerminal(BasicZoneTerminal basicZoneTerminal) throws Exception;

    /**
     * 修改关区码头关联
     *
     * @param basicZoneTerminal 关区码头关联
     * @return 结果
     */
    public int updateBasicZoneTerminal(BasicZoneTerminal basicZoneTerminal) throws Exception;

    /**
     * 批量删除关区码头关联
     *
     * @param ids 需要删除的关区码头关联主键集合
     * @return 结果
     */
    public int deleteBasicZoneTerminalByIds(List<String> ids);

    /**
     * 删除关区码头关联信息
     *
     * @param id 关区码头关联主键
     * @return 结果
     */
    public int deleteBasicZoneTerminalById(String id);
} 