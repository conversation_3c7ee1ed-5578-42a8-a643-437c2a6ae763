package com.ruoyi.plan.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.plan.domain.RelationBookingVoyageCntrCoarse;
import com.ruoyi.plan.service.IRelationBookingVoyageCntrCoarseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/plan/RelationBookingVoyageCntrCoarse")
public class RelationBookingVoyageCntrCoarseController extends BaseController {

    @Autowired
    private IRelationBookingVoyageCntrCoarseService relationBookingVoyageCntrCoarseService;

    @GetMapping("/list")
    public AjaxResult list(RelationBookingVoyageCntrCoarse query) {
        return AjaxResult.success(relationBookingVoyageCntrCoarseService.list());
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        return AjaxResult.success(relationBookingVoyageCntrCoarseService.selectById(id));
    }

    @PostMapping
    public AjaxResult add(@RequestBody RelationBookingVoyageCntrCoarse entity) {
        return toAjax(relationBookingVoyageCntrCoarseService.insert(entity));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody RelationBookingVoyageCntrCoarse entity) {
        return toAjax(relationBookingVoyageCntrCoarseService.update(entity));
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(relationBookingVoyageCntrCoarseService.deleteByIds(ids));
    }
}
