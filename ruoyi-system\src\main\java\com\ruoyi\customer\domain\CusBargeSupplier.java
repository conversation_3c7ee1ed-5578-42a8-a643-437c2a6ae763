package com.ruoyi.customer.domain;

import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

/**
 * 驳船供应商实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "CUS_BARGE_SUPPLIER")
public class CusBargeSupplier extends BaseEntity {

    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    @Column(value = "CUS_MAIN_ID")
    private String cusMainId;

    @Column(value = "ROUTE_NAME")
    private String routeName;

    @Column(value = "BARGE_SUPPLIER_NAME")
    private String bargeSupplierName;

}
