package com.ruoyi.plan.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicEndurance;
import com.ruoyi.basic.domain.BasicPortArea;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.plan.domain.VoyageSeq;
import com.ruoyi.plan.service.IVoyageSeqService;
import com.ruoyi.basic.domain.BasicTerminal;
import com.ruoyi.basic.service.IBasicTerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/plan/voyageSeq")
public class VoyageSeqController extends BaseController {
    @Autowired
    private IVoyageSeqService voyageSeqService;
    @Autowired
    private IBasicTerminalService basicTerminalService;

    /**
     * 查询VOYAGE_SEQ列表
     */
    @GetMapping("/list")
    public Page<VoyageSeq> list(VoyageSeq voyageSeq) {
//        startPage();
//        List<VoyageSeq> list = voyageSeqService.selectVoyageSeqList(voyageSeq);
//        return getDataTable(list);


        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(voyageSeq.getVoyageId())) {
            queryWrapper.eq("voyage_id", voyageSeq.getVoyageId());
        }
        return voyageSeqService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 获取VOYAGE_SEQ详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(voyageSeqService.selectVoyageSeqById(id));
    }

    /**
     * 新增VOYAGE_SEQ
     */
    @PostMapping
    public AjaxResult add(@RequestBody VoyageSeq voyageSeq) {
        return toAjax(voyageSeqService.insertVoyageSeq(voyageSeq));
    }

    /**
     * 修改VOYAGE_SEQ
     */
    @PutMapping
    public AjaxResult edit(@RequestBody VoyageSeq voyageSeq) {
        return toAjax(voyageSeqService.updateVoyageSeq(voyageSeq));
    }

    /**
     * 删除VOYAGE_SEQ
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(voyageSeqService.deleteVoyageSeqByIds(ids));
    }

    /**
     * 查询所有码头ID和名称（用于靠序新增下拉）
     */
    @GetMapping("/terminalOptions")
    public AjaxResult terminalOptions() {
        BasicTerminal query = new BasicTerminal();
        query.setDelFlag("0");
        List<BasicTerminal> list = basicTerminalService.selectBasicTerminalList(query);
        // 只返回ID和TERMINAL_NAME
        List<Map<String, Object>> result = list.stream()
            .map(t -> {
                Map<String, Object> m = new java.util.HashMap<>();
                m.put("id", t.getId());
                m.put("terminalName", t.getTerminalName());
                return m;
            })
            .toList();
        return AjaxResult.success(result);
    }
}
