package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicPortArea;
import com.ruoyi.basic.service.IBasicPortAreaService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 港区管理Controller
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RestController
@RequestMapping("/system/portArea")
public class BasicPortAreaController extends BaseController {

    @Autowired
    private IBasicPortAreaService basicPortAreaService;

    /**
     * 查询港区管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:list')")
    @GetMapping("/list")
    public Page<BasicPortArea> list(BasicPortArea basicPortArea)
    {
        // startPage();
        // List<BasicPortArea> list = basicPortAreaService.selectBasicPortAreaList(basicPortArea);
        // return getDataTable(list);

        var pageDomain = TableSupport.buildPageRequest();

        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicPortArea.getPortAreaCode())) {
            queryWrapper.like("port_area_code", basicPortArea.getPortAreaCode());
        }
        if (StringUtils.isNotEmpty(basicPortArea.getPortAreaName())) {
            queryWrapper.like("port_area_name", basicPortArea.getPortAreaName());
        }
        queryWrapper.orderBy("create_time", true);

        return basicPortAreaService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
        
    }

    @GetMapping("/label")
    public AjaxResult label(BasicPortArea basicPortArea){
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicPortArea.getPortId())) {
            queryWrapper.eq("port_id", basicPortArea.getPortId());
        }
        return AjaxResult.success(basicPortAreaService.list(queryWrapper));
    }

    /**
     * 导出港区管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:export')")
    @Log(title = "港区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicPortArea basicPortArea)
    {
        List<BasicPortArea> list = basicPortAreaService.selectBasicPortAreaList(basicPortArea);
        ExcelUtil<BasicPortArea> util = new ExcelUtil<BasicPortArea>(BasicPortArea.class);
        util.exportExcel(response, list, "港区管理数据");
    }

    /**
     * 获取港区管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:area:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicPortAreaService.selectBasicPortAreaById(id));
    }

    /**
     * 新增港区管理
     */
    @PreAuthorize("@ss.hasPermi('system:area:add')")
    @Log(title = "港区管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicPortArea basicPortArea)
    {
        try {
            return toAjax(basicPortAreaService.insertBasicPortArea(basicPortArea));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改港区管理
     */
    @PreAuthorize("@ss.hasPermi('system:area:edit')")
    @Log(title = "港区管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicPortArea basicPortArea)
    {
        try {
            return toAjax(basicPortAreaService.updateBasicPortArea(basicPortArea));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除港区管理
     */
    @PreAuthorize("@ss.hasPermi('system:area:remove')")
    @Log(title = "港区管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicPortAreaService.deleteBasicPortAreaByIds(ids));
    }

}
