-- DROP USER SHIPPING;

CREATE
    USER SHIPPING
-- IDENTIFIED BY <password>
;

CREATE TABLE "SHIPPING"."BASIC_ANC_BERTH"
(
    "ID"           VARCHAR2(50 CHAR) DEFAULT '0' NOT NULL ENABLE,
    "TERMINAL_ID"  VARCHAR2(50 CHAR),
    "ANB_CODE"     VARCHAR2(255 CHAR)            NOT NULL ENABLE,
    "ANB_CNAME"    VARCHAR2(255 CHAR),
    "ANB_TON"      NUMBER(10, 2),
    "ANB_DEPTH"    NUMBER(10, 2),
    "REMARK"       VARCHAR2(255 CHAR),
    "ANB_LENGTH"   NUMBER(10, 2),
    "ANB_CENTER"   VARCHAR2(255 CHAR),
    "ANB_MATERIAL" VARCHAR2(255 CHAR),
    "ANB_FUNCTION" VARCHAR2(255 CHAR),
    "CREATE_BY"    VARCHAR2(255 CHAR),
    "CREATE_TIME"  DATE,
    "UPDATE_BY"    VARCHAR2(255 CHAR),
    "UPDATE_TIME"  DATE,
    "VERSION"      NUMBER            DEFAULT 1,
    "DEL_FLAG"     VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010235" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010233" CHECK ("ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010234" CHECK ("ANB_CODE" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010235" ON "SHIPPING"."BASIC_ANC_BERTH" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_ANC_BERTH_1" ON "SHIPPING"."BASIC_ANC_BERTH" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_ANC_BERTH IS '锚位管理';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ID IS '锚位ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.TERMINAL_ID IS '关联码头ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_CODE IS '锚位编号';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_CNAME IS '锚位名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_TON IS '锚位吨级';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_DEPTH IS '设计水深';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_LENGTH IS '锚位长度（米)';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_CENTER IS '中心位置';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_MATERIAL IS '底质';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.ANB_FUNCTION IS '功能';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_ANC_BERTH.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_AREA"
(
    "ID"          VARCHAR2(50 CHAR),
    "COU_ID"      VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "AREA_CODE"   VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "AREA_NAME"   VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "EMAIL"       VARCHAR2(255 CHAR),
    "SHORT_CODE"  VARCHAR2(255 CHAR),
    "SHORT_NAME"  VARCHAR2(255 CHAR),
    "REMARK"      VARCHAR2(255 CHAR),
    "CREATE_BY"   VARCHAR2(255 CHAR),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(255 CHAR),
    "UPDATE_TIME" DATE,
    "VERSION"     NUMBER(*, 0)      DEFAULT 0,
    "DEL_FLAG"    VARCHAR2(10 CHAR) DEFAULT '0',
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."BASIC_AREA"
    ADD CONSTRAINT "FK_BASIC_AREA_1" FOREIGN KEY ("COU_ID")
        REFERENCES "SHIPPING"."BASIC_COUNTRY" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010210" ON "SHIPPING"."BASIC_AREA" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_AREA_1" ON "SHIPPING"."BASIC_AREA" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_AREA IS '区域管理';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.ID IS '区域ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.COU_ID IS '国家ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.AREA_CODE IS '区域代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.AREA_NAME IS '区域名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.EMAIL IS '邮编';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.SHORT_CODE IS '短名代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.SHORT_NAME IS '短名名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_AREA.DEL_FLAG IS '删除标志';

CREATE TABLE "SHIPPING"."BASIC_CARGO_TYPE"
(
    "ID"          VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "CARGO_CODE"  VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "CARGO_NAME"  VARCHAR2(255 CHAR),
    "CARGO_KIND"  VARCHAR2(255 CHAR),
    "REMARK"      VARCHAR2(255 CHAR),
    "CREATE_BY"   VARCHAR2(255 CHAR),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(255 CHAR),
    "UPDATE_TIME" DATE,
    "VERSION"     NUMBER            DEFAULT 1,
    "DEL_FLAG"    VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010250" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010248" CHECK ("ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010249" CHECK ("CARGO_CODE" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010250" ON "SHIPPING"."BASIC_CARGO_TYPE" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_CARGO_TYPE_1" ON "SHIPPING"."BASIC_CARGO_TYPE" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_CARGO_TYPE IS '货物基础信息';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.ID IS '货物基础信息编号';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.CARGO_CODE IS '货物代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.CARGO_NAME IS '货物中文名';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.CARGO_KIND IS '货物种类';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_CARGO_TYPE.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_CNTR_HEIGHT"
(
    "ID"                 VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "HEIGHT_CODE"        VARCHAR2(255 CHAR),
    "HEIGHT_NAME"        VARCHAR2(255 CHAR),
    "HEIGHT_ISO"         VARCHAR2(255 CHAR),
    "HEIGHT_SIGN"        VARCHAR2(255 CHAR),
    "HEIGHT_DESCRIPTION" VARCHAR2(255 CHAR),
    "REMARK"             VARCHAR2(255 CHAR),
    "CREATE_BY"          VARCHAR2(255 CHAR),
    "CREATE_TIME"        DATE,
    "UPDATE_BY"          VARCHAR2(255 CHAR),
    "UPDATE_TIME"        DATE,
    "VERSION"            NUMBER            DEFAULT 1,
    "DEL_FLAG"           VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010278" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010277" CHECK ("ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010278" ON "SHIPPING"."BASIC_CNTR_HEIGHT" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_CNTR_HEIGHT_1" ON "SHIPPING"."BASIC_CNTR_HEIGHT" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_CNTR_HEIGHT IS '箱高';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.ID IS '箱高ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.HEIGHT_CODE IS '箱高度代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.HEIGHT_NAME IS '箱高度名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.HEIGHT_ISO IS 'ISO代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.HEIGHT_SIGN IS '高箱标志';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.HEIGHT_DESCRIPTION IS '描述';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_HEIGHT.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_CNTR_ISO"
(
    "ID"           VARCHAR2(50)  NOT NULL ENABLE,
    "ISO_CODE"     VARCHAR2(255) NOT NULL ENABLE,
    "CNTR_SIZE_ID" VARCHAR2(50),
    "CNTR_TYPE_ID" VARCHAR2(50),
    "REMARK"       VARCHAR2(255),
    "CREATE_BY"    VARCHAR2(255),
    "CREATE_TIME"  DATE,
    "UPDATE_BY"    VARCHAR2(255),
    "UPDATE_TIME"  DATE,
    "VERSION"      NUMBER       DEFAULT 1,
    "DEL_FLAG"     VARCHAR2(10) DEFAULT '0',
    CONSTRAINT "SYS_C0010284" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010282" CHECK ("ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010283" CHECK ("ISO_CODE" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010284" ON "SHIPPING"."BASIC_CNTR_ISO" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_CNTR_ISO_1" ON "SHIPPING"."BASIC_CNTR_ISO" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_CNTR_ISO IS '国际箱型';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.ID IS '国际箱型ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.ISO_CODE IS '国际箱型代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.CNTR_SIZE_ID IS '关联箱尺寸ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.CNTR_TYPE_ID IS '关联箱型ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_ISO.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_CNTR_SIZE"
(
    "ID"               VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "SIZE_CODE"        VARCHAR2(255 CHAR),
    "SIZE_NAME"        VARCHAR2(255 CHAR),
    "SIZE_ISO"         VARCHAR2(255 CHAR),
    "SIZE_NET_WEIGHT"  NUMBER(10, 2),
    "SIZE_OVER_WEIGHT" NUMBER(10, 2),
    "SIZE_CATEGORIZED" VARCHAR2(255 CHAR),
    "REMARK"           VARCHAR2(255 CHAR),
    "CREATE_BY"        VARCHAR2(255 CHAR),
    "CREATE_TIME"      DATE,
    "UPDATE_BY"        VARCHAR2(255 CHAR),
    "UPDATE_TIME"      DATE,
    "VERSION"          NUMBER            DEFAULT 1,
    "DEL_FLAG"         VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010276" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010275" CHECK ("ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010276" ON "SHIPPING"."BASIC_CNTR_SIZE" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_CHTR_SIZE_1" ON "SHIPPING"."BASIC_CNTR_SIZE" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_CNTR_SIZE IS '箱尺寸';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.ID IS '箱尺寸ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.SIZE_CODE IS '箱尺寸代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.SIZE_NAME IS '箱尺寸名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.SIZE_ISO IS 'ISO名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.SIZE_NET_WEIGHT IS '净重';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.SIZE_OVER_WEIGHT IS '超重值';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.SIZE_CATEGORIZED IS '归类尺寸';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_SIZE.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_CNTR_TYPE"
(
    "ID"               VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "TYPE_CODE"        VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "TYPE_NAME"        VARCHAR2(255 CHAR),
    "TYPE_ISO"         VARCHAR2(255 CHAR),
    "TYPE_COMMON_CODE" VARCHAR2(255 CHAR),
    "REMARK"           VARCHAR2(255 CHAR),
    "CREATE_BY"        VARCHAR2(255 CHAR),
    "CREATE_TIME"      DATE,
    "UPDATE_BY"        VARCHAR2(255 CHAR),
    "UPDATE_TIME"      DATE,
    "VERSION"          NUMBER             DEFAULT 1,
    "DEL_FLAG"         VARCHAR2(255 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010281" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010279" CHECK ("ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010280" CHECK ("TYPE_CODE" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010281" ON "SHIPPING"."BASIC_CNTR_TYPE" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_CNTR_TYPE_1" ON "SHIPPING"."BASIC_CNTR_TYPE" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_CNTR_TYPE IS '箱型';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.ID IS '箱型ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.TYPE_CODE IS '箱型代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.TYPE_NAME IS '中文名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.TYPE_ISO IS '箱型ISO代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.TYPE_COMMON_CODE IS '公共箱型代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_CNTR_TYPE.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_COUNTRY"
(
    "ID"              VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "BC_COUNTRY_CODE" VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "BC_CHINESE_NAME" VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "BC_ENGLISH_NAME" VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "DEL_FLAG"        VARCHAR2(10 CHAR) DEFAULT '0',
    "VERSION"         NUMBER(*, 0)      DEFAULT 0,
    "CREATE_BY"       VARCHAR2(255 CHAR),
    "CREATE_TIME"     DATE,
    "UPDATE_BY"       VARCHAR2(255 CHAR),
    "UPDATE_TIME"     DATE,
    "REMARK"          VARCHAR2(255 CHAR),
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010199" ON "SHIPPING"."BASIC_COUNTRY" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_COUNTRY_1" ON "SHIPPING"."BASIC_COUNTRY" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_COUNTRY IS '国家';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.ID IS '国家ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.BC_COUNTRY_CODE IS '国家代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.BC_CHINESE_NAME IS '中文名';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.BC_ENGLISH_NAME IS '英文名';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.DEL_FLAG IS '是否删除';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_COUNTRY.REMARK IS '备注';

CREATE TABLE "SHIPPING"."BASIC_DANGER"
(
    "ID"                VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "DANGER_LEVEL"      VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "DANGER_NAME"       VARCHAR2(255 CHAR),
    "DANGER_EN_NAME"    VARCHAR2(255 CHAR),
    "DANGER_CLASS"      VARCHAR2(255 CHAR),
    "ATTENTION"         VARCHAR2(255 CHAR),
    "ISOLATION_REQUEST" VARCHAR2(255 CHAR),
    "CREATE_BY"         VARCHAR2(255 CHAR),
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(255 CHAR),
    "UPDATE_TIME"       DATE,
    "REMARK"            VARCHAR2(255 CHAR),
    "VERSION"           NUMBER             DEFAULT 1,
    "DEL_FLAG"          VARCHAR2(255 CHAR) DEFAULT '0',
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010239" ON "SHIPPING"."BASIC_DANGER" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_DANGER_1" ON "SHIPPING"."BASIC_DANGER" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_DANGER IS '危险品等级';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.ID IS '危险品等级ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.DANGER_LEVEL IS '危险品等级';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.DANGER_NAME IS '危险品等级名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.DANGER_EN_NAME IS '危险品等级英文名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.DANGER_CLASS IS '危险品大类';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.ATTENTION IS '注意事项';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.ISOLATION_REQUEST IS '隔离要求';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_DANGER_UN_NUMBER"
(
    "ID"                VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "DANGER_ID"         VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "UN_NUMBER"         VARCHAR2(255 CHAR),
    "UN_UNIO"           VARCHAR2(255 CHAR),
    "ATTENTION"         VARCHAR2(255 CHAR),
    "ISOLATION_REQUEST" VARCHAR2(255 CHAR),
    "TRANS_REQUEST"     VARCHAR2(255 CHAR),
    "CREATE_BY"         VARCHAR2(255 CHAR),
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(255 CHAR),
    "UPDATE_TIME"       DATE,
    "REMARK"            VARCHAR2(255 CHAR),
    "VERSION"           NUMBER            DEFAULT 1,
    "DEL_FLAG"          VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010242" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010240" CHECK ("ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010241" CHECK ("DANGER_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010242" ON "SHIPPING"."BASIC_DANGER_UN_NUMBER" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_DANGER_UN_1" ON "SHIPPING"."BASIC_DANGER_UN_NUMBER" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_DANGER_UN_NUMBER IS '危险品联合国编号';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.ID IS '联合国编号ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.DANGER_ID IS '危险品等级ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.UN_NUMBER IS '联合国编号';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.UN_UNIO IS '联合国编号名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.ATTENTION IS '注意事项';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.ISOLATION_REQUEST IS '隔离要求';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.TRANS_REQUEST IS '运输要求';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_DANGER_UN_NUMBER.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_ENDURANCE"
(
    "ID"                VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "START_TERMINAL_ID" VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "END_TERMINAL_ID"   VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "VOYAGE_TIME"       NUMBER(10, 2)     NOT NULL ENABLE,
    "IS_SAME"           VARCHAR2(10 CHAR) NOT NULL ENABLE,
    "REMARK"            VARCHAR2(255 CHAR),
    "CREATE_BY"         VARCHAR2(255 CHAR),
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(255 CHAR),
    "UPDATE_TIME"       DATE,
    "VERSION"           NUMBER            DEFAULT 1,
    "DEL_FLAG"          VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010272" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010267" CHECK ("ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010268" CHECK ("START_TERMINAL_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010269" CHECK ("END_TERMINAL_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010270" CHECK ("VOYAGE_TIME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010271" CHECK ("IS_SAME" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010272" ON "SHIPPING"."BASIC_ENDURANCE" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_ENDURANCE_1" ON "SHIPPING"."BASIC_ENDURANCE" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_ENDURANCE IS '航时信息';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.ID IS '航时信息ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.START_TERMINAL_ID IS '起始码头';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.END_TERMINAL_ID IS '目的码头';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.VOYAGE_TIME IS '航行小时';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.IS_SAME IS '是否往返一样 Y是 N否';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_ENDURANCE.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_MILEAGE"
(
    "ID"                VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "START_TERMINAL_ID" VARCHAR2(50 CHAR),
    "END_TERMINAL_ID"   VARCHAR2(50 CHAR),
    "MILEAGE"           NUMBER(10, 2),
    "MILEAGE_UNIT"      VARCHAR2(255 CHAR),
    "REMARK"            VARCHAR2(255 CHAR),
    "CREATE_BY"         VARCHAR2(255 CHAR),
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(255 CHAR),
    "UPDATE_TIME"       DATE,
    "VERSION"           NUMBER            DEFAULT 1,
    "DEL_FLAG"          VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010252" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010251" CHECK ("ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010252" ON "SHIPPING"."BASIC_MILEAGE" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_MILEAGE_1" ON "SHIPPING"."BASIC_MILEAGE" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_MILEAGE IS '码头里程';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.ID IS '码头里程ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.START_TERMINAL_ID IS '起始码头';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.END_TERMINAL_ID IS '目的码头';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.MILEAGE IS '里程';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.MILEAGE_UNIT IS '里程单位';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_MILEAGE.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_PORT"
(
    "ID"                VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "PORT_CODE"         VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "COU_ID"            VARCHAR2(50 CHAR),
    "AREA_ID"           VARCHAR2(50 CHAR),
    "PORT_NAME"         VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "PORT_ENGLISH_NAME" VARCHAR2(255 CHAR),
    "POSITION_X"        NUMBER(8, 0),
    "POSITION_Y"        NUMBER(8, 0),
    "KILOMETERS"        VARCHAR2(255 CHAR),
    "REMARK"            VARCHAR2(255 CHAR),
    "CREATE_BY"         VARCHAR2(255 CHAR),
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(255 CHAR),
    "UPDATE_TIME"       DATE,
    "VERSION"           NUMBER(10, 0)     DEFAULT 0,
    "DEL_FLAG"          VARCHAR2(10 CHAR) DEFAULT '0',
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010218" ON "SHIPPING"."BASIC_PORT" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_PORT_1" ON "SHIPPING"."BASIC_PORT" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_PORT IS '港口管理';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.ID IS '港口ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.PORT_CODE IS '港口代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.COU_ID IS '国家/地区';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.AREA_ID IS '区域';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.PORT_NAME IS '港口名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.PORT_ENGLISH_NAME IS '港口英文名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.POSITION_X IS '港口X坐标';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.POSITION_Y IS '港口Y坐标';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.KILOMETERS IS '支线名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_PORT_AREA"
(
    "ID"                VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "PORT_AREA_CODE"    VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "PORT_AREA_NAME"    VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "PORT_AERA_EN_NAME" VARCHAR2(255 CHAR),
    "PORT_ID"           VARCHAR2(50 CHAR),
    "BELONG_CUSTOMS"    VARCHAR2(255 CHAR),
    "REMARK"            VARCHAR2(255 CHAR),
    "CREATE_BY"         VARCHAR2(50 CHAR),
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(50 CHAR),
    "UPDATE_TIME"       DATE,
    "VERSION"           NUMBER(*, 0)      DEFAULT 1,
    "DEL_FLAG"          VARCHAR2(10 CHAR) DEFAULT '0',
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."BASIC_PORT_AREA"
    ADD CONSTRAINT "FK_BASIC_PORT_AREA_1" FOREIGN KEY ("PORT_ID")
        REFERENCES "SHIPPING"."BASIC_PORT" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010225" ON "SHIPPING"."BASIC_PORT_AREA" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_PORT_AREA_1" ON "SHIPPING"."BASIC_PORT_AREA" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_PORT_AREA IS '港区管理';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.ID IS '港区ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.PORT_AREA_CODE IS '港区代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.PORT_AREA_NAME IS '港区名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.PORT_AERA_EN_NAME IS '港区英文名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.PORT_ID IS '港口ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.BELONG_CUSTOMS IS '所属海关';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_PORT_AREA.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_SHIP_HAZARD_REGISTRATION"
(
    "ID"               VARCHAR2(50)        NOT NULL ENABLE,
    "SHIP_MAIN_ID"     VARCHAR2(50),
    "HAZARD_CLASS"     VARCHAR2(255),
    "TONNAGE"          NUMBER(10, 2),
    "CONTAINER_NUMBER" NUMBER(10, 0),
    "CREATE_BY"        VARCHAR2(50),
    "CREATE_TIME"      DATE,
    "UPDATE_BY"        VARCHAR2(50),
    "UPDATE_TIME"      DATE,
    "VERSION"          NUMBER(10, 0),
    "DEL_FLAG"         CHAR(1) DEFAULT '0' NOT NULL ENABLE,
    "REMARK"           VARCHAR2(255),
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010662" ON "SHIPPING"."BASIC_SHIP_HAZARD_REGISTRATION" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION IS '危险品载重吨（船舶备案子表）';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.ID IS '主键';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.SHIP_MAIN_ID IS '船舶备案主表ID（外键）';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.HAZARD_CLASS IS '危险品等级';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.TONNAGE IS '吨数';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.CONTAINER_NUMBER IS '箱量';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.DEL_FLAG IS '删除标志 0未删除 2已删除';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_HAZARD_REGISTRATION.REMARK IS '备注';

CREATE TABLE "SHIPPING"."BASIC_SHIP_MAIN"
(
    "ID"                  VARCHAR2(50)        NOT NULL ENABLE,
    "SHIP_CHINESE_NAME"   VARCHAR2(255),
    "SHIP_ENGLISH_NAME"   VARCHAR2(255),
    "SHIP_CODE"           VARCHAR2(255),
    "MMSI"                VARCHAR2(255),
    "IMO"                 VARCHAR2(255),
    "CALL_SIGN"           VARCHAR2(255),
    "HS_CODE"             VARCHAR2(255),
    "SHIPPING_LINE"       VARCHAR2(255),
    "SHIP_TYPE"           VARCHAR2(255),
    "SHIP_LENGTH"         NUMBER(10, 2),
    "SHIP_WIDTH"          NUMBER(10, 2),
    "SHIP_HEIGHT"         NUMBER(10, 2),
    "NET_TONNAGE"         NUMBER(10, 2),
    "DEAD_WEIGHT_TONNAGE" NUMBER(10, 2),
    "CONTAINER_CAPACITY"  NUMBER(10, 2),
    "DRAFT"               NUMBER(10, 2),
    "CREATE_BY"           VARCHAR2(50),
    "CREATE_TIME"         DATE,
    "UPDATE_BY"           VARCHAR2(50),
    "UPDATE_TIME"         DATE,
    "DEL_FLAG"            CHAR(1) DEFAULT '0' NOT NULL ENABLE,
    "REMARK"              VARCHAR2(255),
    "VERSION"             NUMBER(10, 0),
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010635" ON "SHIPPING"."BASIC_SHIP_MAIN" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_SHIP_MAIN IS '船舶备案主表';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.ID IS '主键id';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIP_CHINESE_NAME IS '船舶中文名';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIP_ENGLISH_NAME IS '船舶英文名';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIP_CODE IS '船舶代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.MMSI IS 'MMSI';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.IMO IS 'IMO';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.CALL_SIGN IS '呼号';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.HS_CODE IS '海关编码';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIPPING_LINE IS '所属船公司';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIP_TYPE IS '船舶类型';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIP_LENGTH IS '船长';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIP_WIDTH IS '船宽';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.SHIP_HEIGHT IS '船高';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.NET_TONNAGE IS '净吨';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.DEAD_WEIGHT_TONNAGE IS '载重吨';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.CONTAINER_CAPACITY IS '载箱量';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.DRAFT IS '吃水';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.DEL_FLAG IS '删除标志 0未删除 2已删除';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_SHIP_MAIN.VERSION IS '乐观锁';

CREATE TABLE "SHIPPING"."BASIC_TERMINAL"
(
    "ID"                  VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "TERMINAL_NAME"       VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "ENGLISH_NAME"        VARCHAR2(255 CHAR),
    "TERMINAL_CODE"       VARCHAR2(255 CHAR),
    "PORT_ID"             VARCHAR2(50 CHAR),
    "SORT"                NUMBER,
    "REMARK"              VARCHAR2(255 CHAR),
    "CREATE_BY"           VARCHAR2(255 CHAR),
    "CREATE_TIME"         DATE,
    "UPDATE_BY"           VARCHAR2(255 CHAR),
    "UPDATE_TIME"         DATE,
    "VERSION"             NUMBER(*, 0)      DEFAULT 1,
    "DEL_FLAG"            VARCHAR2(10 CHAR) DEFAULT '0',
    "TYPE"                VARCHAR2(255 CHAR),
    "TERMINAL_OTHER_NAME" VARCHAR2(255 CHAR),
    "HG_CODE"             VARCHAR2(255 CHAR),
    "SSHSJ"               VARCHAR2(255 CHAR),
    "PORT_AREA_ID"        VARCHAR2(50 CHAR),
    "TER_TYPE"            VARCHAR2(255 CHAR),
    "AREA_ID"             VARCHAR2(50 CHAR),
    "EXPECT_TIME_IN_PORT" NUMBER,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."BASIC_TERMINAL"
    ADD CONSTRAINT "FK_BASIC_TERMINAL_1" FOREIGN KEY ("PORT_ID")
        REFERENCES "SHIPPING"."BASIC_PORT" ("ID") ENABLE;
ALTER TABLE "SHIPPING"."BASIC_TERMINAL"
    ADD CONSTRAINT "FK_BASIC_TERMINAL_2" FOREIGN KEY ("AREA_ID")
        REFERENCES "SHIPPING"."BASIC_AREA" ("ID") ENABLE;
ALTER TABLE "SHIPPING"."BASIC_TERMINAL"
    ADD CONSTRAINT "FK_BASIC_TERMINAL_3" FOREIGN KEY ("PORT_AREA_ID")
        REFERENCES "SHIPPING"."BASIC_PORT_AREA" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010229" ON "SHIPPING"."BASIC_TERMINAL" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_TERMINAL_1" ON "SHIPPING"."BASIC_TERMINAL" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_TERMINAL IS '码头管理';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.ID IS '码头ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.TERMINAL_NAME IS '码头名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.ENGLISH_NAME IS '码头英文名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.TERMINAL_CODE IS '码头代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.PORT_ID IS '所属港口';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.SORT IS '排序';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.DEL_FLAG IS '逻辑删除';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL."TYPE" IS '所属类别 字典terminal';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.TERMINAL_OTHER_NAME IS '码头别名';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.HG_CODE IS '海事编码';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.SSHSJ IS '所属海事局 字典marine_board';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.PORT_AREA_ID IS '所属港区';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.TER_TYPE IS '类型 字典anchorage_type';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.AREA_ID IS '所属区域';
COMMENT
    ON COLUMN SHIPPING.BASIC_TERMINAL.EXPECT_TIME_IN_PORT IS '预计在港时长';

CREATE TABLE "SHIPPING"."BASIC_ZONE"
(
    "ID"          VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "ZONE_CODE"   VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "ZONE_NAME"   VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "REMARK"      VARCHAR2(255 CHAR),
    "CREATE_BY"   VARCHAR2(255 CHAR),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(255 CHAR),
    "UPDATE_TIME" DATE,
    "VERSION"     NUMBER(*, 0)      DEFAULT 1,
    "DEL_FLAG"    VARCHAR2(10 CHAR) DEFAULT '0',
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010260" ON "SHIPPING"."BASIC_ZONE" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_ZONE_1" ON "SHIPPING"."BASIC_ZONE" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_ZONE IS '关区管理';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.ID IS '关区ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.ZONE_CODE IS '关区代码';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.ZONE_NAME IS '关区名称';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BASIC_ZONE_TERMINAL"
(
    "ID"          VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "ZONE_ID"     VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "TERMINAL_ID" VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "REMARK"      VARCHAR2(255 CHAR),
    "CREATE_BY"   VARCHAR2(255 CHAR),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(255 CHAR),
    "UPDATE_TIME" DATE,
    "VERSION"     NUMBER            DEFAULT 1,
    "DEL_FLAG"    VARCHAR2(10 CHAR) DEFAULT '0',
    CONSTRAINT "SYS_C0010264" PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010261" CHECK ("ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010262" CHECK ("ZONE_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010263" CHECK ("TERMINAL_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010264" ON "SHIPPING"."BASIC_ZONE_TERMINAL" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_ZONE_TERMINAL_1" ON "SHIPPING"."BASIC_ZONE_TERMINAL" ("CREATE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BASIC_ZONE_TERMINAL IS '关区码头关联';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.ID IS '关区码头关联ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.ZONE_ID IS '关区ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.TERMINAL_ID IS '码头ID';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.UPDATE_BY IS '修改人';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.UPDATE_TIME IS '修改时间';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.BASIC_ZONE_TERMINAL.DEL_FLAG IS '逻辑删除';

CREATE TABLE "SHIPPING"."BOOKING_CNTR_NUM"
(
    "ID"                   VARCHAR2(32),
    "BOOKING_ID"           VARCHAR2(32) NOT NULL ENABLE,
    "CONTAINER_SIZE_ID"    VARCHAR2(32),
    "CONTAINER_SIZE_CODE"  VARCHAR2(16),
    "CONTAINER_SIZE_NAME"  VARCHAR2(32),
    "CONTAINER_TYPE_ID"    VARCHAR2(32),
    "CONTAINER_TYPE_CODE"  VARCHAR2(16),
    "CONTAINER_TYPE_NAME"  VARCHAR2(64),
    "IS_EMPTY"             VARCHAR2(1)   DEFAULT '0',
    "QUANTITY"             NUMBER(10, 0) DEFAULT 0,
    "IS_DANGEROUS"         VARCHAR2(1)   DEFAULT '0',
    "DANGEROUS_LEVEL_ID"   VARCHAR2(32),
    "DANGEROUS_LEVEL_CODE" VARCHAR2(16),
    "DANGEROUS_LEVEL_NAME" VARCHAR2(64),
    "IS_REFRIGERATED"      VARCHAR2(1)   DEFAULT '0',
    "CARGO_TYPE_ID"        VARCHAR2(32),
    "CARGO_TYPE_CODE"      VARCHAR2(32),
    "CARGO_TYPE_NAME"      VARCHAR2(128),
    "SINGLE_WEIGHT"        NUMBER(18, 4),
    "TOTAL_WEIGHT"         NUMBER(18, 4),
    "IS_OVERSIZE"          VARCHAR2(1)   DEFAULT '0',
    "OVERSIZE_DIMENSIONS"  VARCHAR2(128),
    "REMARK"               VARCHAR2(500),
    "CREATE_BY"            VARCHAR2(64),
    "CREATE_TIME"          DATE          DEFAULT SYSDATE,
    "UPDATE_BY"            VARCHAR2(64),
    "UPDATE_TIME"          DATE          DEFAULT SYSDATE,
    "DEL_FLAG"             VARCHAR2(1)   DEFAULT '0',
    "VERSION"              NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "CHK_CONTAINER_SIZE_CONSISTENCY" CHECK ((container_size_id IS NULL AND container_size_code IS NULL AND
                                                        container_size_name IS NULL)
        OR (container_size_id IS NOT NULL AND container_size_code IS NOT NULL AND
            container_size_name IS NOT NULL)) ENABLE,
    CONSTRAINT "CHK_CONTAINER_TYPE_CONSISTENCY" CHECK ((container_type_id IS NULL AND container_type_code IS NULL AND
                                                        container_type_name IS NULL)
        OR (container_type_id IS NOT NULL AND container_type_code IS NOT NULL AND
            container_type_name IS NOT NULL)) ENABLE,
    CONSTRAINT "CHK_DANGEROUS_LEVEL_CONSISTENCY" CHECK ((dangerous_level_id IS NULL AND dangerous_level_code IS NULL AND
                                                         dangerous_level_name IS NULL)
        OR (dangerous_level_id IS NOT NULL AND dangerous_level_code IS NOT NULL AND
            dangerous_level_name IS NOT NULL)) ENABLE,
    CONSTRAINT "CHK_CARGO_TYPE_CONSISTENCY" CHECK ((cargo_type_id IS NULL AND cargo_type_code IS NULL AND
                                                    cargo_type_name IS NULL)
        OR (cargo_type_id IS NOT NULL AND cargo_type_code IS NOT NULL AND cargo_type_name IS NOT NULL)) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."BOOKING_CNTR_NUM"
    ADD CONSTRAINT "FK_CNTR_BOOKING" FOREIGN KEY ("BOOKING_ID")
        REFERENCES "SHIPPING"."BOOKING_MAIN" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0011081" ON "SHIPPING"."BOOKING_CNTR_NUM" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_CNTR_NUM_BOOKING" ON "SHIPPING"."BOOKING_CNTR_NUM" ("BOOKING_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_CNTR_CARGO_TYPE" ON "SHIPPING"."BOOKING_CNTR_NUM" ("CARGO_TYPE_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BOOKING_CNTR_NUM IS '柜量信息表 - 严格按项目经理柜量信息表字段设计，记录集装箱详细信息';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.ID IS '主键，雪花ID全局唯一标识符';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.BOOKING_ID IS '关联订舱主表ID，建立订舱与柜量的关系';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CONTAINER_SIZE_ID IS '尺寸ID，关联BASIC_CNTR_SIZE.id，外键约束RESTRICT防止基础表数据被误删';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CONTAINER_SIZE_CODE IS '尺寸代码（冗余存储），如"20", "40"，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CONTAINER_SIZE_NAME IS '尺寸显示名称（冗余存储），如"20''", "40''"，用于界面显示，确保显示稳定性';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CONTAINER_TYPE_ID IS '箱型ID，关联BASIC_CNTR_TYPE.id，外键约束RESTRICT防止基础表数据被误删';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CONTAINER_TYPE_CODE IS '箱型代码（冗余存储），如"GP", "HC"，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CONTAINER_TYPE_NAME IS '箱型显示名称（冗余存储），如"普通柜GP", "普通柜HC"，用于界面显示，确保显示稳定性';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.IS_EMPTY IS '重吉标识：0重箱（有货）/1吉箱（空箱）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.QUANTITY IS '箱量，该规格集装箱的数量';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.IS_DANGEROUS IS '是否危险品：0否/1是';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.DANGEROUS_LEVEL_ID IS '危险品等级ID，关联BASIC_DANGER.id，外键约束RESTRICT防止基础表数据被误删';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.DANGEROUS_LEVEL_CODE IS '危险品等级代码（冗余存储），映射BASIC_DANGER.dangerLevel字段，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.DANGEROUS_LEVEL_NAME IS '危险品等级名称（冗余存储），映射BASIC_DANGER.dangerName字段，用于界面显示，确保显示稳定性';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.IS_REFRIGERATED IS '是否冷藏：0否/1是';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CARGO_TYPE_ID IS '货类ID，关联basic_cargo_type表主键，外键约束RESTRICT防止基础表数据被误删';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CARGO_TYPE_CODE IS '货类代码（冗余存储），映射BASIC_CARGO_TYPE.cargoCode字段，用于业务逻辑处理，即使基础表关联断裂也能正常工作';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CARGO_TYPE_NAME IS '货类名称（冗余存储），映射BASIC_CARGO_TYPE.cargoName字段，用于界面显示，确保显示稳定性';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.SINGLE_WEIGHT IS '单箱重量（公斤），单个集装箱的重量';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.TOTAL_WEIGHT IS '总重（公斤），该规格所有集装箱的总重量';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.IS_OVERSIZE IS '是否超限：0否/1是，是否超出标准尺寸';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.OVERSIZE_DIMENSIONS IS '超限尺寸，超出标准尺寸的具体描述';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.REMARK IS '备注信息，特殊要求或注意事项';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CREATE_BY IS '创建人，操作员工号';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.UPDATE_BY IS '更新人，最后修改操作员工号';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.DEL_FLAG IS '逻辑删除标志：0正常 2删除';
COMMENT
    ON COLUMN SHIPPING.BOOKING_CNTR_NUM.VERSION IS '乐观锁版本号，防止并发修改';

CREATE TABLE "SHIPPING"."BOOKING_MAIN"
(
    "ID"                        VARCHAR2(32),
    "ORDER_ID"                  VARCHAR2(32) NOT NULL ENABLE,
    "BOOKING_NUMBER"            VARCHAR2(64) NOT NULL ENABLE,
    "STATUS"                    VARCHAR2(32)  DEFAULT 'DRAFT',
    "SHIPPER_ID"                VARCHAR2(32),
    "SHIPPER_NAME"              VARCHAR2(128),
    "ORIGIN_LOCATION_ID"        VARCHAR2(32),
    "ORIGIN_LOCATION_NAME"      VARCHAR2(128),
    "DESTINATION_LOCATION_ID"   VARCHAR2(32),
    "DESTINATION_LOCATION_NAME" VARCHAR2(128),
    "BOOKING_DATE"              DATE          DEFAULT SYSDATE,
    "DEPARTURE_DATE"            DATE,
    "DELIVERY_DATE"             DATE,
    "LOADING_TERMINAL_ID"       VARCHAR2(32),
    "LOADING_TERMINAL_NAME"     VARCHAR2(128),
    "UNLOADING_TERMINAL_ID"     VARCHAR2(32),
    "UNLOADING_TERMINAL_NAME"   VARCHAR2(128),
    "LOADING_AGENT_ID"          VARCHAR2(32),
    "LOADING_AGENT_NAME"        VARCHAR2(128),
    "UNLOADING_AGENT_ID"        VARCHAR2(32),
    "UNLOADING_AGENT_NAME"      VARCHAR2(128),
    "TRADE_TYPE"                VARCHAR2(32),
    "TRANSPORT_MODE"            VARCHAR2(32),
    "CUSTOMS_TYPE"              VARCHAR2(32),
    "SETTLEMENT_METHOD"         VARCHAR2(32),
    "CONSORTIUM"                VARCHAR2(64),
    "CUSTOMER_AGREEMENT"        VARCHAR2(128),
    "CONSIGNMENT_SOURCE"        VARCHAR2(64),
    "REMARK"                    VARCHAR2(500),
    "CREATE_BY"                 VARCHAR2(64),
    "CREATE_TIME"               DATE          DEFAULT SYSDATE,
    "UPDATE_BY"                 VARCHAR2(64),
    "UPDATE_TIME"               DATE          DEFAULT SYSDATE,
    "DEL_FLAG"                  VARCHAR2(1)   DEFAULT '0',
    "VERSION"                   NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    UNIQUE ("BOOKING_NUMBER")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "CHK_BOOKING_STATUS" CHECK (status IN ('DRAFT', 'SUBMITTED', 'CONFIRMED', 'REJECTED', 'CANCELLED')) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."BOOKING_MAIN"
    ADD CONSTRAINT "FK_BOOKING_ORDER_ID" FOREIGN KEY ("ORDER_ID")
        REFERENCES "SHIPPING"."ORDER_MAIN" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0011074" ON "SHIPPING"."BOOKING_MAIN" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_C0011075" ON "SHIPPING"."BOOKING_MAIN" ("BOOKING_NUMBER") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_BOOKING_ORDER" ON "SHIPPING"."BOOKING_MAIN" ("ORDER_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BOOKING_MAIN IS '订舱主表（三层架构第二层）- 原为穿巴订舱(BOOKING_MAIN_CHUANBA)，现作为统一订舱模型，支持策划员拆单操作';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.ID IS '主键，雪花ID全局唯一标识符';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.ORDER_ID IS '关联委托主表ID，建立委托与订舱的关系';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.BOOKING_NUMBER IS '订舱号，格式：BK+日期+6位序号，如BK202507220000001';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.STATUS IS '订舱状态：DRAFT（草稿）/SUBMITTED（已提交）/CONFIRMED（已确认）/REJECTED（已拒绝）/CANCELLED（已取消）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.SHIPPER_ID IS '托运单位ID，关联客户基础资料表';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.SHIPPER_NAME IS '托运单位名称（冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.ORIGIN_LOCATION_ID IS '起运地ID（使用terminal Options，支持用户自创option时为空）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.ORIGIN_LOCATION_NAME IS '起运地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.DESTINATION_LOCATION_ID IS '目的地ID（使用terminal Options，支持用户自创option时为空）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.DESTINATION_LOCATION_NAME IS '目的地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.BOOKING_DATE IS '订舱日期，业务申请日期';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.DEPARTURE_DATE IS '启运日期，实际发货时间（会议要求去除要求二字）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.DELIVERY_DATE IS '交货日期，实际到货时间（会议要求去除要求二字）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.LOADING_TERMINAL_ID IS '装货码头ID，关联码头基础资料表';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.LOADING_TERMINAL_NAME IS '装货码头名称（支持自动填充逻辑）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.UNLOADING_TERMINAL_ID IS '卸货码头ID，关联码头基础资料表';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.UNLOADING_TERMINAL_NAME IS '卸货码头名称（支持自动填充逻辑）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.LOADING_AGENT_ID IS '装货代理ID，关联代理公司基础资料表';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.LOADING_AGENT_NAME IS '装货代理名称（冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.UNLOADING_AGENT_ID IS '卸货代理ID，关联代理公司基础资料表';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.UNLOADING_AGENT_NAME IS '卸货代理名称（冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.TRADE_TYPE IS '贸易类型：DOMESTIC（内贸）/FOREIGN（外贸）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.TRANSPORT_MODE IS '运输模式：FCL（整箱）/LCL（拼箱）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.CUSTOMS_TYPE IS '报关类型：GENERAL（一般贸易）/PROCESSING（加工贸易）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.SETTLEMENT_METHOD IS '结算方式：MONTHLY（月结）/PREPAID（预付）/COD（货到付款）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.CONSORTIUM IS '所属共同体，穿巴业务固定为穿巴';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.CUSTOMER_AGREEMENT IS '客户协议编号，运输合同编号';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.CONSIGNMENT_SOURCE IS '委托来源：ONLINE（网上申请）/PHONE（电话委托）/ONSITE（现场委托）/EMAIL（邮件委托）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.REMARK IS '备注信息，特殊要求或注意事项';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.CREATE_BY IS '创建人，操作员工号';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.UPDATE_BY IS '更新人，最后修改操作员工号';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.DEL_FLAG IS '逻辑删除标志：0正常 2删除';
COMMENT
    ON COLUMN SHIPPING.BOOKING_MAIN.VERSION IS '乐观锁版本号，防止并发修改';

CREATE TABLE "SHIPPING"."BOOKING_TRANSIT_PORT"
(
    "ID"                VARCHAR2(32),
    "BOOKING_ID"        VARCHAR2(32) NOT NULL ENABLE,
    "SEQUENCE_NO"       NUMBER(10, 0),
    "TRANSIT_PORT_ID"   VARCHAR2(32),
    "TRANSIT_PORT_NAME" VARCHAR2(128),
    "AGENT_ID"          VARCHAR2(32),
    "AGENT_NAME"        VARCHAR2(128),
    "REMARK"            VARCHAR2(500),
    "CREATE_BY"         VARCHAR2(64),
    "CREATE_TIME"       DATE          DEFAULT SYSDATE,
    "UPDATE_BY"         VARCHAR2(64),
    "UPDATE_TIME"       DATE          DEFAULT SYSDATE,
    "DEL_FLAG"          VARCHAR2(1)   DEFAULT '0',
    "VERSION"           NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."BOOKING_TRANSIT_PORT"
    ADD CONSTRAINT "FK_TRANSIT_BOOKING" FOREIGN KEY ("BOOKING_ID")
        REFERENCES "SHIPPING"."BOOKING_MAIN" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0011084" ON "SHIPPING"."BOOKING_TRANSIT_PORT" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_TRANSIT_BOOKING" ON "SHIPPING"."BOOKING_TRANSIT_PORT" ("BOOKING_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.BOOKING_TRANSIT_PORT IS '中转港表 - 严格按项目经理中转港表字段设计，记录运输中转港信息';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.ID IS '主键，雪花ID全局唯一标识符';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.BOOKING_ID IS '关联订舱主表ID，建立订舱与中转港的关系';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.SEQUENCE_NO IS '序号，中转港的顺序编号（按中转先后顺序）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.TRANSIT_PORT_ID IS '中转港ID，关联港口基础资料表';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.TRANSIT_PORT_NAME IS '中转港名称（冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.AGENT_ID IS '代理公司ID，关联代理公司基础资料表';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.AGENT_NAME IS '代理公司名称（冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.REMARK IS '备注信息，特殊要求或注意事项';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.CREATE_BY IS '创建人，操作员工号';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.UPDATE_BY IS '更新人，最后修改操作员工号';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.DEL_FLAG IS '逻辑删除标志：0正常 2删除';
COMMENT
    ON COLUMN SHIPPING.BOOKING_TRANSIT_PORT.VERSION IS '乐观锁版本号，防止并发修改';

CREATE TABLE "SHIPPING"."CONTRACT_CUSTOM"
(
    "CONTRACT_ID"   VARCHAR2(50) NOT NULL ENABLE,
    "CUS_ID"        VARCHAR2(50) NOT NULL ENABLE,
    "CONTRACT_ROLE" VARCHAR2(50) NOT NULL ENABLE,
    CONSTRAINT "SYS_C0010291" CHECK ("CONTRACT_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010292" CHECK ("CUS_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010293" CHECK ("CONTRACT_ROLE" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";
;

CREATE TABLE "SHIPPING"."CONTRACT_FLOW"
(
    "FLOW_ID"     VARCHAR2(50 CHAR),
    "CONTRACT_ID" VARCHAR2(50 CHAR)  NOT NULL ENABLE,
    "FLOW_NAME"   VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "FLOW_DESC"   VARCHAR2(255 CHAR),
    "DEL_FLAG"    VARCHAR2(10 CHAR) DEFAULT '0',
    "VERSION"     NUMBER(*, 0)      DEFAULT 0,
    "CREATE_BY"   VARCHAR2(255 CHAR),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(255 CHAR),
    "UPDATE_TIME" DATE,
    "REMARK"      VARCHAR2(255 CHAR),
    PRIMARY KEY ("FLOW_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010623" ON "SHIPPING"."CONTRACT_FLOW" ("FLOW_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.CONTRACT_FLOW IS '合同流向';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW.FLOW_ID IS '流向ID';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW.CONTRACT_ID IS '合同ID';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW.FLOW_NAME IS '流向名称';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW.FLOW_DESC IS '流向描述';

CREATE TABLE "SHIPPING"."CONTRACT_FLOW_TERMINAL"
(
    "FLOW_TERMINAL_ID" VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "FLOW_ID"          VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "TERMINAL_ID"      VARCHAR2(50 CHAR) NOT NULL ENABLE,
    "ORDER_NUM"        NUMBER(*, 0)      NOT NULL ENABLE,
    "DEL_FLAG"         VARCHAR2(255 CHAR) DEFAULT '0',
    "VERSION"          NUMBER(*, 0)       DEFAULT 0,
    "CREATE_BY"        VARCHAR2(255 CHAR),
    "CREATE_TIME"      DATE,
    "UPDATE_BY"        VARCHAR2(255 CHAR),
    "UPDATE_TIME"      DATE,
    "REMARK"           VARCHAR2(255 CHAR),
    PRIMARY KEY ("FLOW_TERMINAL_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010628" ON "SHIPPING"."CONTRACT_FLOW_TERMINAL" ("FLOW_TERMINAL_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW_TERMINAL.FLOW_TERMINAL_ID IS '合同流向 明细';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW_TERMINAL.FLOW_ID IS '流向ID';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW_TERMINAL.TERMINAL_ID IS '码头ID';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_FLOW_TERMINAL.ORDER_NUM IS '排序';

CREATE TABLE "SHIPPING"."CONTRACT_MAIN"
(
    "CONTRACT_ID"         VARCHAR2(50) NOT NULL ENABLE,
    "CONTRACT_NO"         VARCHAR2(255),
    "CONTRACT_NAME"       VARCHAR2(255),
    "VALIDITY_START_DATE" DATE,
    "VALIDITY_END_DATE"   DATE,
    "CONTRACT_TYPE"       VARCHAR2(255),
    "DEPT_ID"             NUMBER(20, 0),
    "USED_TIMES"          NUMBER(20, 0),
    "IS_VALIDITY"         VARCHAR2(10) DEFAULT 'N',
    "CREATE_BY"           VARCHAR2(255),
    "CREATE_TIME"         DATE,
    "UPDATE_BY"           VARCHAR2(255),
    "UPDATE_TIME"         DATE,
    "DEL_FLAG"            VARCHAR2(10),
    "VERSION"             NUMBER(20, 0),
    "REMARK"              VARCHAR2(255),
    CONSTRAINT "SYS_C0010290" PRIMARY KEY ("CONTRACT_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010290" ON "SHIPPING"."CONTRACT_MAIN" ("CONTRACT_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.CONTRACT_NO IS '合同编号';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.CONTRACT_NAME IS '合同名称';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.VALIDITY_START_DATE IS '合同有效期开始时间';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.VALIDITY_END_DATE IS '合同有效期结束时间';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.CONTRACT_TYPE IS '合同类型';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.DEPT_ID IS '经办部门';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.USED_TIMES IS '使用次数';
COMMENT
    ON COLUMN SHIPPING.CONTRACT_MAIN.IS_VALIDITY IS '是否生效';

CREATE TABLE "SHIPPING"."CONTRACT_PAYMENT_RELATION"
(
    "CONTRACT_ID"   VARCHAR2(50) NOT NULL ENABLE,
    "RELATION_CODE" VARCHAR2(50) NOT NULL ENABLE,
    CONSTRAINT "SYS_C0010294" CHECK ("RELATION_CODE" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010295" CHECK ("CONTRACT_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";
;

CREATE TABLE "SHIPPING"."CUS_BANK"
(
    "CUS_BANK_ID"       VARCHAR2(255) NOT NULL ENABLE,
    "CUS_MAIN_ID"       VARCHAR2(255),
    "BANK_OF_DEPOSIT"   VARCHAR2(255),
    "BANK_ACCOUNT"      VARCHAR2(255),
    "BANK_ACCOUNT_NAME" VARCHAR2(255),
    "DEL_FLAG"          VARCHAR2(255) DEFAULT '0',
    "CREATE_BY"         VARCHAR2(255),
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(255),
    "UPDATE_TIME"       DATE,
    "REMARK"            VARCHAR2(255),
    "SORT"              VARCHAR2(255),
    PRIMARY KEY ("CUS_BANK_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE INDEX "SHIPPING"."INDEX_CUS_BANK_1" ON "SHIPPING"."CUS_BANK" ("CUS_MAIN_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010606" ON "SHIPPING"."CUS_BANK" ("CUS_BANK_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.CUS_BANK IS '客户银行账号';
COMMENT
    ON COLUMN SHIPPING.CUS_BANK.CUS_BANK_ID IS '银行账户id';
COMMENT
    ON COLUMN SHIPPING.CUS_BANK.CUS_MAIN_ID IS '客户id';
COMMENT
    ON COLUMN SHIPPING.CUS_BANK.BANK_OF_DEPOSIT IS '开户行';
COMMENT
    ON COLUMN SHIPPING.CUS_BANK.BANK_ACCOUNT IS '银行账户';
COMMENT
    ON COLUMN SHIPPING.CUS_BANK.BANK_ACCOUNT_NAME IS '开户名';
COMMENT
    ON COLUMN SHIPPING.CUS_BANK.SORT IS '排序';

CREATE TABLE "SHIPPING"."CUS_BARGE_SUPPLIER"
(
    "ID"                  VARCHAR2(255) NOT NULL ENABLE,
    "CUS_MAIN_ID"         VARCHAR2(255),
    "ROUTE_NAME"          VARCHAR2(255),
    "BARGE_SUPPLIER_NAME" VARCHAR2(4000),
    "CREATE_BY"           VARCHAR2(255),
    "CREATE_TIME"         DATE,
    "UPDATE_BY"           VARCHAR2(255),
    "UPDATE_TIME"         DATE,
    "REMARK"              VARCHAR2(255),
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010680" ON "SHIPPING"."CUS_BARGE_SUPPLIER" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."INDEX_CUS_BARGE_SUPPLIER_1" ON "SHIPPING"."CUS_BARGE_SUPPLIER" ("CUS_MAIN_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
;

CREATE TABLE "SHIPPING"."CUS_BUSINESS_TYPE"
(
    "CUS_MAIN_ID"       VARCHAR2(255) NOT NULL ENABLE,
    "CUS_BUSINESS_TYPE" VARCHAR2(255) NOT NULL ENABLE,
    PRIMARY KEY ("CUS_MAIN_ID", "CUS_BUSINESS_TYPE")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010669" ON "SHIPPING"."CUS_BUSINESS_TYPE" ("CUS_MAIN_ID", "CUS_BUSINESS_TYPE") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.CUS_BUSINESS_TYPE IS '客户业务类型';
COMMENT
    ON COLUMN SHIPPING.CUS_BUSINESS_TYPE.CUS_MAIN_ID IS '客户ID';
COMMENT
    ON COLUMN SHIPPING.CUS_BUSINESS_TYPE.CUS_BUSINESS_TYPE IS '关联字典cus_business_type';

CREATE TABLE "SHIPPING"."CUS_COMPANY_TYPE"
(
    "CUS_MAIN_ID"  VARCHAR2(255) NOT NULL ENABLE,
    "COMPANY_TYPE" VARCHAR2(255) NOT NULL ENABLE,
    PRIMARY KEY ("CUS_MAIN_ID", "COMPANY_TYPE")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010677" ON "SHIPPING"."CUS_COMPANY_TYPE" ("CUS_MAIN_ID", "COMPANY_TYPE") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.CUS_COMPANY_TYPE.CUS_MAIN_ID IS '客户主表id';
COMMENT
    ON COLUMN SHIPPING.CUS_COMPANY_TYPE.COMPANY_TYPE IS '客户企业类型';

CREATE TABLE "SHIPPING"."CUS_CONTACT"
(
    "CUS_CONTACT_ID" VARCHAR2(255),
    "CUS_MAIN_ID"    VARCHAR2(255) NOT NULL ENABLE,
    "ADDRESS"        VARCHAR2(255),
    "NAME"           VARCHAR2(255),
    "PHONE_NUMBER"   VARCHAR2(255),
    "MAIL_ADDRESS"   VARCHAR2(255),
    "CREATE_BY"      VARCHAR2(255),
    "CREATE_TIME"    DATE,
    "UPDATE_BY"      VARCHAR2(255),
    "UPDATE_TIME"    DATE,
    "REMARK"         VARCHAR2(255),
    "DEL_FLAG"       VARCHAR2(255) DEFAULT '0',
    PRIMARY KEY ("CUS_CONTACT_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE INDEX "SHIPPING"."INDEX_CUS_CONTACT_1" ON "SHIPPING"."CUS_CONTACT" ("CUS_MAIN_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010603" ON "SHIPPING"."CUS_CONTACT" ("CUS_CONTACT_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.CUS_CONTACT IS '客户联系人';
COMMENT
    ON COLUMN SHIPPING.CUS_CONTACT.CUS_CONTACT_ID IS '联系人id';
COMMENT
    ON COLUMN SHIPPING.CUS_CONTACT.CUS_MAIN_ID IS '客户id';
COMMENT
    ON COLUMN SHIPPING.CUS_CONTACT.ADDRESS IS '地址';
COMMENT
    ON COLUMN SHIPPING.CUS_CONTACT.NAME IS '姓名';
COMMENT
    ON COLUMN SHIPPING.CUS_CONTACT.PHONE_NUMBER IS '电话号码';
COMMENT
    ON COLUMN SHIPPING.CUS_CONTACT.MAIL_ADDRESS IS '邮箱地址';

CREATE TABLE "SHIPPING"."CUS_HISTORY"
(
    "CUS_HISTORY_ID" VARCHAR2(255) NOT NULL ENABLE,
    "CUS_MAIN_ID"    VARCHAR2(255) NOT NULL ENABLE,
    "CONTENT"        CLOB          NOT NULL ENABLE,
    "CREATE_BY"      VARCHAR2(255),
    "CREATE_TIME"    DATE,
    "UPDATE_BY"      VARCHAR2(255),
    "UPDATE_TIME"    DATE,
    "REMARK"         VARCHAR2(255),
    "DEL_FLAG"       VARCHAR2(255) DEFAULT '0',
    PRIMARY KEY ("CUS_HISTORY_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("CONTENT") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
    STORAGE
(
    INITIAL
    106496
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
));

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010684" ON "SHIPPING"."CUS_HISTORY" ("CUS_HISTORY_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077298C00003$$" ON "SHIPPING"."CUS_HISTORY" (
                                                                                       PCTFREE 10 INITRANS 2 MAXTRANS
                                                                                       255
                                                                                       STORAGE (INITIAL 65536 NEXT
                                                                                       1048576 MINEXTENTS 1 MAXEXTENTS
                                                                                       **********
                                                                                       PCTINCREASE 0 FREELISTS 1
                                                                                       FREELIST GROUPS 1
                                                                                       BUFFER_POOL DEFAULT FLASH_CACHE
                                                                                       DEFAULT CELL_FLASH_CACHE DEFAULT)
                                                                                       TABLESPACE "SHIPPING"
                                                                                       PARALLEL (DEGREE 0 INSTANCES 0);

COMMENT
    ON TABLE SHIPPING.CUS_HISTORY IS '客户变更历史表';
COMMENT
    ON COLUMN SHIPPING.CUS_HISTORY.CUS_HISTORY_ID IS '客户变更历史id';
COMMENT
    ON COLUMN SHIPPING.CUS_HISTORY.CUS_MAIN_ID IS '客户id';
COMMENT
    ON COLUMN SHIPPING.CUS_HISTORY.CONTENT IS '客户变更历史内容(JSON)';

CREATE TABLE "SHIPPING"."CUS_MAIN"
(
    "CUS_ID"                  VARCHAR2(255 CHAR) NOT NULL ENABLE,
    "CUS_NAME"                VARCHAR2(255 CHAR),
    "CUS_ABBREVIATION"        VARCHAR2(255 CHAR),
    "CUS_CODE"                VARCHAR2(255 CHAR),
    "CUS_CREDIT_CODE"         VARCHAR2(255 CHAR),
    "CUS_IDENTITY"            VARCHAR2(255 CHAR),
    "STATUS"                  VARCHAR2(255 CHAR),
    "INDUSTRY"                VARCHAR2(255 CHAR),
    "LEGAL_PERSON_NAME"       VARCHAR2(255 CHAR),
    "REG_STATUS"              VARCHAR2(255 CHAR),
    "REG_CAPITAL"             VARCHAR2(255 CHAR),
    "ACTUAL_CAPITAL"          VARCHAR2(255 CHAR),
    "ESTABLISH_TIME"          DATE,
    "APPROVED_TIME"           DATE,
    "FROM_TIME"               DATE,
    "STAFF_NUM_RANGE"         VARCHAR2(255 CHAR),
    "SOCIAL_STAFF_NUM"        NUMBER,
    "REG_INSTITUTE"           VARCHAR2(255 CHAR),
    "HISTORY_NAME"            VARCHAR2(255 CHAR),
    "REG_LOCATION"            VARCHAR2(255 CHAR),
    "BUSINESS_SCOPE"          VARCHAR2(255 CHAR),
    "INVOICE_HEADER"          VARCHAR2(255 CHAR),
    "INVOICE_NUMBER"          VARCHAR2(255 CHAR),
    "INVOICE_ADDRESS"         VARCHAR2(255 CHAR),
    "INVOICE_PHONE"           VARCHAR2(255 CHAR),
    "INVOICE_BANK"            VARCHAR2(255 CHAR),
    "INVOICE_BANK_ACCOUNT"    VARCHAR2(255 CHAR),
    "INVOICE_RECEIVE_ADDRESS" VARCHAR2(255 CHAR),
    "CREATE_BY"               VARCHAR2(255 CHAR),
    "CREATE_TIME"             DATE,
    "UPDATE_BY"               VARCHAR2(255 CHAR),
    "UPDATE_TIME"             DATE,
    "REMARK"                  VARCHAR2(255 CHAR),
    "DEL_FLAG"                VARCHAR2(255 CHAR) DEFAULT '0',
    "CONTAINER_OWNER"         VARCHAR2(255 CHAR),
    "CUS_AREA"                VARCHAR2(4000 CHAR),
    "CUS_ROUTE"               VARCHAR2(4000 CHAR),
    CONSTRAINT "SYS_C0010288" PRIMARY KEY ("CUS_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010287" CHECK ("CUS_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010288" ON "SHIPPING"."CUS_MAIN" ("CUS_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_ID IS '客户id';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_NAME IS '客户名称';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_ABBREVIATION IS '客户简称';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_CODE IS '客户代码';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_CREDIT_CODE IS '唯一识别码';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_IDENTITY IS '客户属性 ';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.STATUS IS '状态';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INDUSTRY IS '行业';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.LEGAL_PERSON_NAME IS '法人';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.REG_STATUS IS '经营状态';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.REG_CAPITAL IS '注册资本';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.ACTUAL_CAPITAL IS '实缴资本';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.ESTABLISH_TIME IS '成立日期';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.APPROVED_TIME IS '核准日期';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.FROM_TIME IS '核营业开始日期';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.STAFF_NUM_RANGE IS '人员规模';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.SOCIAL_STAFF_NUM IS '参保人数';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.REG_INSTITUTE IS '登记机关';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.HISTORY_NAME IS '曾用名';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.REG_LOCATION IS '注册地址';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.BUSINESS_SCOPE IS '经营范围';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INVOICE_HEADER IS '发票抬头';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INVOICE_NUMBER IS '税号';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INVOICE_ADDRESS IS '单位地址';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INVOICE_PHONE IS '电话';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INVOICE_BANK IS '开户银行';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INVOICE_BANK_ACCOUNT IS '银行账户';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.INVOICE_RECEIVE_ADDRESS IS '收票邮箱';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CONTAINER_OWNER IS '箱属';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_AREA IS '服务辖区';
COMMENT
    ON COLUMN SHIPPING.CUS_MAIN.CUS_ROUTE IS '合作服务航线';

CREATE TABLE "SHIPPING"."CUS_TYPE"
(
    "CUS_MAIN_ID" VARCHAR2(255),
    "CUS_TYPE"    VARCHAR2(255),
    PRIMARY KEY ("CUS_MAIN_ID", "CUS_TYPE")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010670" ON "SHIPPING"."CUS_TYPE" ("CUS_MAIN_ID", "CUS_TYPE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.CUS_TYPE.CUS_MAIN_ID IS '客户表主键';
COMMENT
    ON COLUMN SHIPPING.CUS_TYPE.CUS_TYPE IS '客户类型';

CREATE TABLE "SHIPPING"."FEE_CATEGORY"
(
    "CATEGORY_ID"   VARCHAR2(50) NOT NULL ENABLE,
    "CATEGORY_NAME" VARCHAR2(255),
    "FEE_TYPE"      VARCHAR2(255),
    "TAX_RATE"      NUMBER(10, 2),
    "ACCOUNT_CODE"  VARCHAR2(255),
    "CREATE_BY"     VARCHAR2(50),
    "CREATE_TIME"   DATE,
    "UPDATE_BY"     VARCHAR2(50),
    "UPDATE_TIME"   DATE,
    "DEL_FLAG"      VARCHAR2(10)  DEFAULT '0',
    "VERSION"       NUMBER(20, 0) DEFAULT 0,
    "REMARK"        VARCHAR2(255),
    CONSTRAINT "SYS_C0010289" PRIMARY KEY ("CATEGORY_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010289" ON "SHIPPING"."FEE_CATEGORY" ("CATEGORY_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.FEE_CATEGORY.CATEGORY_NAME IS '费项分类名称';
COMMENT
    ON COLUMN SHIPPING.FEE_CATEGORY.FEE_TYPE IS '费目';
COMMENT
    ON COLUMN SHIPPING.FEE_CATEGORY.TAX_RATE IS '税点';
COMMENT
    ON COLUMN SHIPPING.FEE_CATEGORY.ACCOUNT_CODE IS '财务代码';

CREATE TABLE "SHIPPING"."GEN_TABLE"
(
    "TABLE_ID"          NUMBER(20, 0) NOT NULL ENABLE,
    "TABLE_NAME"        VARCHAR2(200) DEFAULT '',
    "TABLE_COMMENT"     VARCHAR2(500) DEFAULT '',
    "SUB_TABLE_NAME"    VARCHAR2(64)  DEFAULT NULL,
    "SUB_TABLE_FK_NAME" VARCHAR2(64)  DEFAULT NULL,
    "CLASS_NAME"        VARCHAR2(100) DEFAULT '',
    "TPL_CATEGORY"      VARCHAR2(200) DEFAULT 'crud',
    "TPL_WEB_TYPE"      VARCHAR2(30)  DEFAULT '',
    "PACKAGE_NAME"      VARCHAR2(100),
    "MODULE_NAME"       VARCHAR2(30),
    "BUSINESS_NAME"     VARCHAR2(30),
    "FUNCTION_NAME"     VARCHAR2(50),
    "FUNCTION_AUTHOR"   VARCHAR2(50),
    "GEN_TYPE"          CHAR(1)       DEFAULT '0',
    "GEN_PATH"          VARCHAR2(200) DEFAULT '/',
    "OPTIONS"           VARCHAR2(1000),
    "CREATE_BY"         VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME"       DATE,
    "UPDATE_BY"         VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME"       DATE,
    "REMARK"            VARCHAR2(500) DEFAULT NULL,
    CONSTRAINT "PK_GEN_TABLE" PRIMARY KEY ("TABLE_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010117" CHECK ("TABLE_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_GEN_TABLE" ON "SHIPPING"."GEN_TABLE" ("TABLE_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.GEN_TABLE IS '代码生成业务表';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.TABLE_ID IS '编号';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.TABLE_NAME IS '表名称';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.TABLE_COMMENT IS '表描述';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.SUB_TABLE_NAME IS '关联子表的表名';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.SUB_TABLE_FK_NAME IS '子表关联的外键名';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.CLASS_NAME IS '实体类名称';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.TPL_CATEGORY IS '使用的模板（crud单表操作 tree树表操作）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.TPL_WEB_TYPE IS '前端模板类型（element-ui模版 element-plus模版）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.PACKAGE_NAME IS '生成包路径';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.MODULE_NAME IS '生成模块名';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.BUSINESS_NAME IS '生成业务名';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.FUNCTION_NAME IS '生成功能名';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.FUNCTION_AUTHOR IS '生成功能作者';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.GEN_TYPE IS '生成代码方式（0zip压缩包 1自定义路径）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.GEN_PATH IS '生成路径（不填默认项目路径）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE."OPTIONS" IS '其它生成选项';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE.REMARK IS '备注';

CREATE TABLE "SHIPPING"."GEN_TABLE_COLUMN"
(
    "COLUMN_ID"      NUMBER(20, 0) NOT NULL ENABLE,
    "TABLE_ID"       NUMBER(20, 0),
    "COLUMN_NAME"    VARCHAR2(200),
    "COLUMN_COMMENT" VARCHAR2(500),
    "COLUMN_TYPE"    VARCHAR2(100),
    "JAVA_TYPE"      VARCHAR2(500),
    "JAVA_FIELD"     VARCHAR2(200),
    "IS_PK"          CHAR(1),
    "IS_INCREMENT"   CHAR(1),
    "IS_REQUIRED"    CHAR(1),
    "IS_INSERT"      CHAR(1),
    "IS_EDIT"        CHAR(1),
    "IS_LIST"        CHAR(1),
    "IS_QUERY"       CHAR(1),
    "QUERY_TYPE"     VARCHAR2(200) DEFAULT 'EQ',
    "HTML_TYPE"      VARCHAR2(200),
    "DICT_TYPE"      VARCHAR2(200) DEFAULT '',
    "SORT"           NUMBER(4, 0),
    "CREATE_BY"      VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME"    DATE,
    "UPDATE_BY"      VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME"    DATE,
    CONSTRAINT "PK_GEN_TABLE_COLUMN" PRIMARY KEY ("COLUMN_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010119" CHECK ("COLUMN_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_GEN_TABLE_COLUMN" ON "SHIPPING"."GEN_TABLE_COLUMN" ("COLUMN_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.GEN_TABLE_COLUMN IS '代码生成业务表字段';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.COLUMN_ID IS '编号';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.TABLE_ID IS '归属表编号';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.COLUMN_NAME IS '列名称';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.COLUMN_COMMENT IS '列描述';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.COLUMN_TYPE IS '列类型';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.JAVA_TYPE IS 'JAVA类型';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.JAVA_FIELD IS 'JAVA字段名';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.IS_PK IS '是否主键（1是）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.IS_INCREMENT IS '是否自增（1是）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.IS_REQUIRED IS '是否必填（1是）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.IS_INSERT IS '是否为插入字段（1是）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.IS_EDIT IS '是否编辑字段（1是）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.IS_LIST IS '是否列表字段（1是）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.IS_QUERY IS '是否查询字段（1是）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.QUERY_TYPE IS '查询方式（等于、不等于、大于、小于、范围）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.HTML_TYPE IS '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.DICT_TYPE IS '字典类型';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.SORT IS '排序';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.GEN_TABLE_COLUMN.UPDATE_TIME IS '更新时间';

CREATE TABLE "SHIPPING"."LOGISTICS_MAIN"
(
    "ID"                        VARCHAR2(32),
    "BOOKING_ID"                VARCHAR2(32) NOT NULL ENABLE,
    "BUSINESS_TYPE"             VARCHAR2(32),
    "SUPPLIER_ID"               VARCHAR2(32),
    "SUPPLIER_NAME"             VARCHAR2(128),
    "ORIGIN_LOCATION_ID"        VARCHAR2(32),
    "ORIGIN_LOCATION_NAME"      VARCHAR2(128),
    "DESTINATION_LOCATION_ID"   VARCHAR2(32),
    "DESTINATION_LOCATION_NAME" VARCHAR2(128),
    "CONTAINER_DETAILS"         CLOB,
    "TERMS_CONDITIONS"          VARCHAR2(256),
    "REQUIRED_DEPARTURE_DATE"   DATE,
    "REQUIRED_ARRIVAL_DATE"     DATE,
    "REMARK"                    VARCHAR2(500),
    "CREATE_BY"                 VARCHAR2(64),
    "CREATE_TIME"               DATE          DEFAULT SYSDATE,
    "UPDATE_BY"                 VARCHAR2(64),
    "UPDATE_TIME"               DATE          DEFAULT SYSDATE,
    "DEL_FLAG"                  VARCHAR2(1)   DEFAULT '0',
    "VERSION"                   NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("CONTAINER_DETAILS") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
    STORAGE
(
    INITIAL
    106496
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
));

ALTER TABLE "SHIPPING"."LOGISTICS_MAIN"
    ADD CONSTRAINT "FK_LOGISTICS_BOOKING" FOREIGN KEY ("BOOKING_ID")
        REFERENCES "SHIPPING"."BOOKING_MAIN" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0011078" ON "SHIPPING"."LOGISTICS_MAIN" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077726C00010$$" ON "SHIPPING"."LOGISTICS_MAIN" (
                                                                                          PCTFREE 10 INITRANS 2 MAXTRANS
                                                                                          255
                                                                                          STORAGE (INITIAL 65536 NEXT
                                                                                          1048576 MINEXTENTS 1
                                                                                          MAXEXTENTS **********
                                                                                          PCTINCREASE 0 FREELISTS 1
                                                                                          FREELIST GROUPS 1
                                                                                          BUFFER_POOL DEFAULT
                                                                                          FLASH_CACHE DEFAULT
                                                                                          CELL_FLASH_CACHE DEFAULT)
                                                                                          TABLESPACE "SHIPPING"
                                                                                          PARALLEL (DEGREE 0 INSTANCES
                                                                                          0);
CREATE INDEX "SHIPPING"."IDX_LOGISTICS_BOOKING" ON "SHIPPING"."LOGISTICS_MAIN" ("BOOKING_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.LOGISTICS_MAIN IS '物流主表（三层架构第三层）- 物流过程的公共信息，严格按项目经理运输环节表字段设计';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.ID IS '主键，雪花ID全局唯一标识符';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.BOOKING_ID IS '关联订舱主表ID，建立订舱与物流的关系';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.BUSINESS_TYPE IS '业务类型：EXPORT（出口）/IMPORT（进口）/DOMESTIC（内贸）';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.SUPPLIER_ID IS '供应商ID，关联船务供应商基础资料表';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.SUPPLIER_NAME IS '供应商名称（冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.ORIGIN_LOCATION_ID IS '启运地ID（使用terminal Options，支持用户自创option时为空）';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.ORIGIN_LOCATION_NAME IS '启运地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.DESTINATION_LOCATION_ID IS '目的地ID（使用terminal Options，支持用户自创option时为空）';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.DESTINATION_LOCATION_NAME IS '目的地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.CONTAINER_DETAILS IS '箱量明细（JSON格式存储），包含箱型、数量、重量等详细信息';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.TERMS_CONDITIONS IS '条款，运输合同条款或特殊约定';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.REQUIRED_DEPARTURE_DATE IS '要求启运日期，客户要求的发货时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.REQUIRED_ARRIVAL_DATE IS '要求到达日期，客户要求的到货时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.REMARK IS '备注信息，特殊要求或注意事项';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.CREATE_BY IS '创建人，操作员工号';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.UPDATE_BY IS '更新人，最后修改操作员工号';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.DEL_FLAG IS '逻辑删除标志：0正常 2删除';
COMMENT
    ON COLUMN SHIPPING.LOGISTICS_MAIN.VERSION IS '乐观锁版本号，防止并发修改';

CREATE TABLE "SHIPPING"."LOGISTIC_RAILWAY"
(
    "ID"                 VARCHAR2(32),
    "LOGISTICS_ID"       VARCHAR2(32) NOT NULL ENABLE,
    "TRAIN_NO"           VARCHAR2(32),
    "DEPARTURE_STATION"  VARCHAR2(128),
    "ARRIVAL_STATION"    VARCHAR2(128),
    "RAILWAY_COMPANY"    VARCHAR2(128),
    "WAGON_NO"           VARCHAR2(32),
    "WAGON_TYPE"         VARCHAR2(32),
    "WAGON_CAPACITY"     NUMBER(18, 4),
    "DEPARTURE_TIME"     DATE,
    "ARRIVAL_TIME"       DATE,
    "TRANSIT_TIME"       NUMBER(10, 0),
    "CARGO_WEIGHT"       NUMBER(18, 4),
    "CONTAINER_QUANTITY" NUMBER(10, 0),
    "RAILWAY_FREIGHT"    NUMBER(18, 4),
    "LOADING_FEE"        NUMBER(18, 4),
    "UNLOADING_FEE"      NUMBER(18, 4),
    "TOTAL_COST"         NUMBER(18, 4),
    "RAILWAY_STATUS"     VARCHAR2(32)  DEFAULT 'PENDING',
    "REMARK"             VARCHAR2(500),
    "CREATE_BY"          VARCHAR2(64),
    "CREATE_TIME"        DATE          DEFAULT SYSDATE,
    "UPDATE_BY"          VARCHAR2(64),
    "UPDATE_TIME"        DATE          DEFAULT SYSDATE,
    "DEL_FLAG"           VARCHAR2(1)   DEFAULT '0',
    "VERSION"            NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010874" ON "SHIPPING"."LOGISTIC_RAILWAY" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_RAILWAY_LOGISTICS" ON "SHIPPING"."LOGISTIC_RAILWAY" ("LOGISTICS_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_RAILWAY_TRAIN" ON "SHIPPING"."LOGISTIC_RAILWAY" ("TRAIN_NO") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_RAILWAY_STATUS" ON "SHIPPING"."LOGISTIC_RAILWAY" ("RAILWAY_STATUS") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.LOGISTIC_RAILWAY IS '铁路运输子表';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.TRAIN_NO IS '车次号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.DEPARTURE_STATION IS '发站';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.ARRIVAL_STATION IS '到站';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.WAGON_NO IS '车皮号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.RAILWAY_STATUS IS '铁路运输状态';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.REMARK IS '备注信息';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.CREATE_BY IS '创建人，操作员工号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.UPDATE_BY IS '更新人，最后修改操作员工号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.DEL_FLAG IS '逻辑删除标志(0正常 2删除)';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_RAILWAY.VERSION IS '乐观锁版本号，防止并发修改';

CREATE TABLE "SHIPPING"."LOGISTIC_TRUCKING"
(
    "ID"                    VARCHAR2(32),
    "LOGISTICS_ID"          VARCHAR2(32) NOT NULL ENABLE,
    "TRUCK_ID"              VARCHAR2(32),
    "TRUCK_LICENSE"         VARCHAR2(32),
    "DRIVER_ID"             VARCHAR2(32),
    "DRIVER_NAME"           VARCHAR2(64),
    "DRIVER_PHONE"          VARCHAR2(32),
    "PICKUP_ADDRESS"        VARCHAR2(256),
    "PICKUP_CONTACT"        VARCHAR2(64),
    "PICKUP_PHONE"          VARCHAR2(32),
    "DELIVERY_ADDRESS"      VARCHAR2(256),
    "DELIVERY_CONTACT"      VARCHAR2(64),
    "DELIVERY_PHONE"        VARCHAR2(32),
    "PLANNED_PICKUP_TIME"   DATE,
    "ACTUAL_PICKUP_TIME"    DATE,
    "PLANNED_DELIVERY_TIME" DATE,
    "ACTUAL_DELIVERY_TIME"  DATE,
    "CARGO_WEIGHT"          NUMBER(18, 4),
    "CARGO_VOLUME"          NUMBER(18, 4),
    "CARGO_PIECES"          NUMBER(10, 0),
    "DISTANCE_KM"           NUMBER(10, 2),
    "FUEL_COST"             NUMBER(18, 4),
    "TOLL_COST"             NUMBER(18, 4),
    "OTHER_COST"            NUMBER(18, 4),
    "TOTAL_COST"            NUMBER(18, 4),
    "TRUCKING_STATUS"       VARCHAR2(32)  DEFAULT 'PENDING',
    "GPS_TRACKING"          VARCHAR2(1)   DEFAULT '0',
    "REMARK"                VARCHAR2(500),
    "CREATE_BY"             VARCHAR2(64),
    "CREATE_TIME"           DATE          DEFAULT SYSDATE,
    "UPDATE_BY"             VARCHAR2(64),
    "UPDATE_TIME"           DATE          DEFAULT SYSDATE,
    "DEL_FLAG"              VARCHAR2(1)   DEFAULT '0',
    "VERSION"               NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010871" ON "SHIPPING"."LOGISTIC_TRUCKING" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_TRUCKING_LOGISTICS" ON "SHIPPING"."LOGISTIC_TRUCKING" ("LOGISTICS_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_TRUCKING_TRUCK" ON "SHIPPING"."LOGISTIC_TRUCKING" ("TRUCK_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_TRUCKING_DRIVER" ON "SHIPPING"."LOGISTIC_TRUCKING" ("DRIVER_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_TRUCKING_STATUS" ON "SHIPPING"."LOGISTIC_TRUCKING" ("TRUCKING_STATUS") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.LOGISTIC_TRUCKING IS '汽运子表';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.TRUCK_LICENSE IS '车牌号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.DRIVER_NAME IS '司机姓名';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.PICKUP_ADDRESS IS '提货地址';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.DELIVERY_ADDRESS IS '送货地址';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.TRUCKING_STATUS IS '汽运状态';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.GPS_TRACKING IS '是否GPS跟踪';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.REMARK IS '备注信息';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.CREATE_BY IS '创建人，操作员工号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.UPDATE_BY IS '更新人，最后修改操作员工号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.DEL_FLAG IS '逻辑删除标志(0正常 2删除)';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_TRUCKING.VERSION IS '乐观锁版本号，防止并发修改';

CREATE TABLE "SHIPPING"."LOGISTIC_WATERWAY"
(
    "ID"                      VARCHAR2(36),
    "LOGISTICS_ID"            VARCHAR2(32) NOT NULL ENABLE,
    "ROUTE_ID"                VARCHAR2(32),
    "ROUTE_NAME"              VARCHAR2(128),
    "VESSEL_ID"               VARCHAR2(32),
    "VESSEL_NAME"             VARCHAR2(128),
    "VOYAGE_NO"               VARCHAR2(32),
    "LOADING_TERMINAL_ID"     VARCHAR2(32),
    "LOADING_TERMINAL_NAME"   VARCHAR2(128),
    "UNLOADING_TERMINAL_ID"   VARCHAR2(32),
    "UNLOADING_TERMINAL_NAME" VARCHAR2(128),
    "ETD"                     DATE,
    "ETA"                     DATE,
    "ATD"                     DATE,
    "ATA"                     DATE,
    "SEQUENCE_NO"             NUMBER(10, 0) DEFAULT 1,
    "CONTAINER_ALLOCATION"    CLOB,
    "STATUS"                  VARCHAR2(32)  DEFAULT 'pending',
    "SPLIT_BY"                VARCHAR2(64),
    "SPLIT_TIME"              DATE,
    "STOWAGE_PLAN_ID"         VARCHAR2(32),
    "REMARK"                  VARCHAR2(500),
    "CREATE_BY"               VARCHAR2(64),
    "CREATE_TIME"             DATE          DEFAULT SYSDATE,
    "UPDATE_BY"               VARCHAR2(64),
    "UPDATE_TIME"             DATE          DEFAULT SYSDATE,
    "DEL_FLAG"                VARCHAR2(1)   DEFAULT '0',
    "VERSION"                 NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("CONTAINER_ALLOCATION") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
    STORAGE
(
    INITIAL
    106496
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
));

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010966" ON "SHIPPING"."LOGISTIC_WATERWAY" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077627C00017$$" ON "SHIPPING"."LOGISTIC_WATERWAY" (
                                                                                             PCTFREE 10 INITRANS 2
                                                                                             MAXTRANS 255
                                                                                             STORAGE (INITIAL 65536 NEXT
                                                                                             1048576 MINEXTENTS 1
                                                                                             MAXEXTENTS **********
                                                                                             PCTINCREASE 0 FREELISTS 1
                                                                                             FREELIST GROUPS 1
                                                                                             BUFFER_POOL DEFAULT
                                                                                             FLASH_CACHE DEFAULT
                                                                                             CELL_FLASH_CACHE DEFAULT)
                                                                                             TABLESPACE "SHIPPING"
                                                                                             PARALLEL (DEGREE 0
                                                                                             INSTANCES 0);

COMMENT
    ON TABLE SHIPPING.LOGISTIC_WATERWAY IS '物流水路运输信息表';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.ID IS '主键ID（雪花ID）';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.LOGISTICS_ID IS '关联主物流表ID';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.ROUTE_ID IS '航线ID';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.ROUTE_NAME IS '航线名称';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.VESSEL_ID IS '船舶ID';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.VESSEL_NAME IS '船舶名称';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.VOYAGE_NO IS '航次号';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.LOADING_TERMINAL_ID IS '装货港ID';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.LOADING_TERMINAL_NAME IS '装货港名称';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.UNLOADING_TERMINAL_ID IS '卸货港ID';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.UNLOADING_TERMINAL_NAME IS '卸货港名称';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.ETD IS '预计离港时间(Estimated Time of Departure)';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.ETA IS '预计到港时间(Estimated Time of Arrival)';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.ATD IS '实际离港时间(Actual Time of Departure)';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.ATA IS '实际到港时间(Actual Time of Arrival)';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.SEQUENCE_NO IS '运输段序号（多段运输时使用）';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.CONTAINER_ALLOCATION IS '集装箱分配信息（JSON格式）';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.STATUS IS '状态：pending待处理/split已拆解/allocated已分配/cancel已取消';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.SPLIT_BY IS '拆单操作员';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.SPLIT_TIME IS '拆单时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.STOWAGE_PLAN_ID IS '配载计划ID';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.REMARK IS '备注信息';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.DEL_FLAG IS '删除标志（0-未删除，1-已删除）';
COMMENT
    ON COLUMN SHIPPING.LOGISTIC_WATERWAY.VERSION IS '版本号（乐观锁）';

CREATE TABLE "SHIPPING"."ORDER_MAIN"
(
    "ID"                 VARCHAR2(32),
    "ORDER_NO"           VARCHAR2(64)  NOT NULL ENABLE,
    "SHIPPER_ID"         VARCHAR2(32)  NOT NULL ENABLE,
    "SHIPPER_NAME"       VARCHAR2(128) NOT NULL ENABLE,
    "ORDER_DATE"         DATE          DEFAULT SYSDATE,
    "BUSINESS_TYPE"      VARCHAR2(32),
    "TRADE_TYPE"         VARCHAR2(32),
    "CONSORTIUM"         VARCHAR2(64),
    "CUSTOMER_AGREEMENT" VARCHAR2(128),
    "SETTLEMENT_METHOD"  VARCHAR2(32),
    "TOTAL_CONTAINERS"   NUMBER(10, 0) DEFAULT 0,
    "TOTAL_WEIGHT"       NUMBER(18, 4) DEFAULT 0,
    "STATUS"             VARCHAR2(32)  DEFAULT 'DRAFT',
    "REMARK"             VARCHAR2(500),
    "CREATE_BY"          VARCHAR2(64),
    "CREATE_TIME"        DATE          DEFAULT SYSDATE,
    "UPDATE_BY"          VARCHAR2(64),
    "UPDATE_TIME"        DATE          DEFAULT SYSDATE,
    "DEL_FLAG"           VARCHAR2(1)   DEFAULT '0',
    "VERSION"            NUMBER(10, 0) DEFAULT 1,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    UNIQUE ("ORDER_NO")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "CHK_ORDER_STATUS" CHECK (status IN ('DRAFT', 'SUBMITTED', 'CONFIRMED', 'COMPLETED', 'CANCELLED')) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0011070" ON "SHIPPING"."ORDER_MAIN" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_C0011071" ON "SHIPPING"."ORDER_MAIN" ("ORDER_NO") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.ORDER_MAIN IS '委托主表（三层架构第一层）- 客户委托的顶层抽象，一个委托可包含多个不同业务类型的订舱';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.ID IS '主键，雪花ID全局唯一标识符';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.ORDER_NO IS '委托编号，格式：WT+日期+6位序号，如WT202507220000001';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.SHIPPER_ID IS '托运单位ID，关联客户基础资料表';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.SHIPPER_NAME IS '托运单位名称（冗余存储便于查询）';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.ORDER_DATE IS '委托创建日期，业务发生日期';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.BUSINESS_TYPE IS '业务类型：EXPORT（出口）/IMPORT（进口）/DOMESTIC（内贸）';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.TRADE_TYPE IS '贸易类型：DOMESTIC（内贸）/FOREIGN（外贸）';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.CONSORTIUM IS '所属共同体：中山共同体、黄埔共同体（支持联合运营业务）';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.CUSTOMER_AGREEMENT IS '客户协议号，运输合同编号';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.SETTLEMENT_METHOD IS '结算方式：MONTHLY（月结）/PREPAID（预付）/COD（货到付款）';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.TOTAL_CONTAINERS IS '总箱量，所有订舱的箱量汇总（统计字段）';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.TOTAL_WEIGHT IS '总重量（吨），所有订舱的重量汇总';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.STATUS IS '委托状态：DRAFT（草稿）/SUBMITTED（已提交）/CONFIRMED（已确认）/COMPLETED（已完成）/CANCELLED（已取消）';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.REMARK IS '备注信息，客户特殊要求或注意事项';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.CREATE_BY IS '创建人，操作员工号';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.UPDATE_BY IS '更新人，最后修改操作员工号';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.DEL_FLAG IS '逻辑删除标志：0正常 2删除';
COMMENT
    ON COLUMN SHIPPING.ORDER_MAIN.VERSION IS '乐观锁版本号，防止并发修改';

CREATE TABLE "SHIPPING"."QRTZ_BLOB_TRIGGERS"
(
    "SCHED_NAME"    VARCHAR2(120) NOT NULL ENABLE,
    "TRIGGER_NAME"  VARCHAR2(200) NOT NULL ENABLE,
    "TRIGGER_GROUP" VARCHAR2(200) NOT NULL ENABLE,
    "BLOB_DATA"     BLOB,
    CONSTRAINT "QRTZ_BLOB_TRIG_PK" PRIMARY KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010161" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010162" CHECK ("TRIGGER_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010163" CHECK ("TRIGGER_GROUP" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("BLOB_DATA") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
);

ALTER TABLE "SHIPPING"."QRTZ_BLOB_TRIGGERS"
    ADD CONSTRAINT "QRTZ_BLOB_TRIG_TO_TRIG_FK" FOREIGN KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        REFERENCES "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077076C00004$$" ON "SHIPPING"."QRTZ_BLOB_TRIGGERS" (
                                                                                              PCTFREE 10 INITRANS 2
                                                                                              MAXTRANS 255
                                                                                              STORAGE (INITIAL 65536
                                                                                              NEXT 1048576 MAXEXTENTS
                                                                                              **********
                                                                                              BUFFER_POOL DEFAULT
                                                                                              FLASH_CACHE DEFAULT
                                                                                              CELL_FLASH_CACHE DEFAULT)
                                                                                              TABLESPACE "SHIPPING"
                                                                                              PARALLEL (DEGREE 0
                                                                                              INSTANCES 0);
CREATE UNIQUE INDEX "SHIPPING"."QRTZ_BLOB_TRIG_PK" ON "SHIPPING"."QRTZ_BLOB_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_BLOB_TRIGGERS IS 'Blob类型的触发器表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_BLOB_TRIGGERS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_BLOB_TRIGGERS.TRIGGER_NAME IS 'qrtz_triggers表trigger_name的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_BLOB_TRIGGERS.TRIGGER_GROUP IS 'qrtz_triggers表trigger_group的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_BLOB_TRIGGERS.BLOB_DATA IS '存放持久化Trigger对象';

CREATE TABLE "SHIPPING"."QRTZ_CALENDARS"
(
    "SCHED_NAME"    VARCHAR2(120) NOT NULL ENABLE,
    "CALENDAR_NAME" VARCHAR2(200) NOT NULL ENABLE,
    "CALENDAR"      BLOB          NOT NULL ENABLE,
    CONSTRAINT "QRTZ_CALENDARS_PK" PRIMARY KEY ("SCHED_NAME", "CALENDAR_NAME")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010166" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010167" CHECK ("CALENDAR_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010168" CHECK ("CALENDAR" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("CALENDAR") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
);

CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077079C00003$$" ON "SHIPPING"."QRTZ_CALENDARS" (
                                                                                          PCTFREE 10 INITRANS 2 MAXTRANS
                                                                                          255
                                                                                          STORAGE (INITIAL 65536 NEXT
                                                                                          1048576 MAXEXTENTS **********
                                                                                          BUFFER_POOL DEFAULT
                                                                                          FLASH_CACHE DEFAULT
                                                                                          CELL_FLASH_CACHE DEFAULT)
                                                                                          TABLESPACE "SHIPPING"
                                                                                          PARALLEL (DEGREE 0 INSTANCES
                                                                                          0);
CREATE UNIQUE INDEX "SHIPPING"."QRTZ_CALENDARS_PK" ON "SHIPPING"."QRTZ_CALENDARS" ("SCHED_NAME", "CALENDAR_NAME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_CALENDARS IS '日历信息表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CALENDARS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CALENDARS.CALENDAR_NAME IS '日历名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CALENDARS.CALENDAR IS '存放持久化calendar对象';

CREATE TABLE "SHIPPING"."QRTZ_CRON_TRIGGERS"
(
    "SCHED_NAME"      VARCHAR2(120) NOT NULL ENABLE,
    "TRIGGER_NAME"    VARCHAR2(200) NOT NULL ENABLE,
    "TRIGGER_GROUP"   VARCHAR2(200) NOT NULL ENABLE,
    "CRON_EXPRESSION" VARCHAR2(120) NOT NULL ENABLE,
    "TIME_ZONE_ID"    VARCHAR2(80),
    CONSTRAINT "QRTZ_CRON_TRIG_PK" PRIMARY KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010155" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010156" CHECK ("TRIGGER_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010157" CHECK ("TRIGGER_GROUP" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010158" CHECK ("CRON_EXPRESSION" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."QRTZ_CRON_TRIGGERS"
    ADD CONSTRAINT "QRTZ_CRON_TRIG_TO_TRIG_FK" FOREIGN KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        REFERENCES "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_CRON_TRIG_PK" ON "SHIPPING"."QRTZ_CRON_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_CRON_TRIGGERS IS 'Cron类型的触发器表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CRON_TRIGGERS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CRON_TRIGGERS.TRIGGER_NAME IS 'qrtz_triggers表trigger_name的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CRON_TRIGGERS.TRIGGER_GROUP IS 'qrtz_triggers表trigger_group的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CRON_TRIGGERS.CRON_EXPRESSION IS 'cron表达式';
COMMENT
    ON COLUMN SHIPPING.QRTZ_CRON_TRIGGERS.TIME_ZONE_ID IS '时区';

CREATE TABLE "SHIPPING"."QRTZ_FIRED_TRIGGERS"
(
    "SCHED_NAME"        VARCHAR2(120) NOT NULL ENABLE,
    "ENTRY_ID"          VARCHAR2(95)  NOT NULL ENABLE,
    "TRIGGER_NAME"      VARCHAR2(200) NOT NULL ENABLE,
    "TRIGGER_GROUP"     VARCHAR2(200) NOT NULL ENABLE,
    "INSTANCE_NAME"     VARCHAR2(200) NOT NULL ENABLE,
    "FIRED_TIME"        NUMBER(13, 0) NOT NULL ENABLE,
    "SCHED_TIME"        NUMBER(13, 0) NOT NULL ENABLE,
    "PRIORITY"          NUMBER(13, 0) NOT NULL ENABLE,
    "STATE"             VARCHAR2(16)  NOT NULL ENABLE,
    "JOB_NAME"          VARCHAR2(200),
    "JOB_GROUP"         VARCHAR2(200),
    "IS_NONCONCURRENT"  VARCHAR2(1),
    "REQUESTS_RECOVERY" VARCHAR2(1),
    CONSTRAINT "QRTZ_FIRED_TRIGGER_PK" PRIMARY KEY ("SCHED_NAME", "ENTRY_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010173" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010174" CHECK ("ENTRY_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010175" CHECK ("TRIGGER_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010176" CHECK ("TRIGGER_GROUP" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010177" CHECK ("INSTANCE_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010178" CHECK ("FIRED_TIME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010179" CHECK ("SCHED_TIME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010180" CHECK ("PRIORITY" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010181" CHECK ("STATE" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_FIRED_TRIGGER_PK" ON "SHIPPING"."QRTZ_FIRED_TRIGGERS" ("SCHED_NAME", "ENTRY_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_FT_INST_JOB_REQ_RCVRY" ON "SHIPPING"."QRTZ_FIRED_TRIGGERS" ("SCHED_NAME", "INSTANCE_NAME", "REQUESTS_RECOVERY") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_FT_JG" ON "SHIPPING"."QRTZ_FIRED_TRIGGERS" ("SCHED_NAME", "JOB_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_FT_J_G" ON "SHIPPING"."QRTZ_FIRED_TRIGGERS" ("SCHED_NAME", "JOB_NAME", "JOB_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_FT_TG" ON "SHIPPING"."QRTZ_FIRED_TRIGGERS" ("SCHED_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_FT_TRIG_INST_NAME" ON "SHIPPING"."QRTZ_FIRED_TRIGGERS" ("SCHED_NAME", "INSTANCE_NAME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_FT_T_G" ON "SHIPPING"."QRTZ_FIRED_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_FIRED_TRIGGERS IS '已触发的触发器表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.ENTRY_ID IS '调度器实例id';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.TRIGGER_NAME IS 'qrtz_triggers表trigger_name的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.TRIGGER_GROUP IS 'qrtz_triggers表trigger_group的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.INSTANCE_NAME IS '调度器实例名';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.FIRED_TIME IS '触发的时间';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.SCHED_TIME IS '定时器制定的时间';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.PRIORITY IS '优先级';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.STATE IS '状态';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.JOB_NAME IS '任务名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.JOB_GROUP IS '任务组名';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.IS_NONCONCURRENT IS '是否并发';
COMMENT
    ON COLUMN SHIPPING.QRTZ_FIRED_TRIGGERS.REQUESTS_RECOVERY IS '是否接受恢复执行';

CREATE TABLE "SHIPPING"."QRTZ_JOB_DETAILS"
(
    "SCHED_NAME"        VARCHAR2(120) NOT NULL ENABLE,
    "JOB_NAME"          VARCHAR2(200) NOT NULL ENABLE,
    "JOB_GROUP"         VARCHAR2(200) NOT NULL ENABLE,
    "DESCRIPTION"       VARCHAR2(250),
    "JOB_CLASS_NAME"    VARCHAR2(250) NOT NULL ENABLE,
    "IS_DURABLE"        VARCHAR2(1)   NOT NULL ENABLE,
    "IS_NONCONCURRENT"  VARCHAR2(1)   NOT NULL ENABLE,
    "IS_UPDATE_DATA"    VARCHAR2(1)   NOT NULL ENABLE,
    "REQUESTS_RECOVERY" VARCHAR2(1)   NOT NULL ENABLE,
    "JOB_DATA"          BLOB,
    CONSTRAINT "QRTZ_JOB_DETAILS_PK" PRIMARY KEY ("SCHED_NAME", "JOB_NAME", "JOB_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    TABLESPACE "SHIPPING"
    LOB ("JOB_DATA") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
);

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_JOB_DETAILS_PK" ON "SHIPPING"."QRTZ_JOB_DETAILS" ("SCHED_NAME", "JOB_NAME", "JOB_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000076851C00010$$" ON "SHIPPING"."QRTZ_JOB_DETAILS" (
                                                                                            PCTFREE 10 INITRANS 2
                                                                                            MAXTRANS 255
                                                                                            STORAGE (INITIAL 65536 NEXT
                                                                                            1048576 MAXEXTENTS
                                                                                            **********
                                                                                            BUFFER_POOL DEFAULT
                                                                                            FLASH_CACHE DEFAULT
                                                                                            CELL_FLASH_CACHE DEFAULT)
                                                                                            TABLESPACE "SHIPPING"
                                                                                            PARALLEL (DEGREE 0 INSTANCES
                                                                                            0);
CREATE INDEX "SHIPPING"."IDX_QRTZ_J_REQ_RECOVERY" ON "SHIPPING"."QRTZ_JOB_DETAILS" ("SCHED_NAME", "REQUESTS_RECOVERY") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_J_GRP" ON "SHIPPING"."QRTZ_JOB_DETAILS" ("SCHED_NAME", "JOB_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_JOB_DETAILS IS '任务详细信息表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.JOB_NAME IS '任务名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.JOB_GROUP IS '任务组名';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.DESCRIPTION IS '相关介绍';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.JOB_CLASS_NAME IS '执行任务类名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.IS_DURABLE IS '是否持久化';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.IS_NONCONCURRENT IS '是否并发';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.IS_UPDATE_DATA IS '是否更新数据';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.REQUESTS_RECOVERY IS '是否接受恢复执行';
COMMENT
    ON COLUMN SHIPPING.QRTZ_JOB_DETAILS.JOB_DATA IS '存放持久化job对象';

CREATE TABLE "SHIPPING"."QRTZ_LOCKS"
(
    "SCHED_NAME" VARCHAR2(120) NOT NULL ENABLE,
    "LOCK_NAME"  VARCHAR2(40)  NOT NULL ENABLE,
    CONSTRAINT "QRTZ_LOCKS_PK" PRIMARY KEY ("SCHED_NAME", "LOCK_NAME")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010188" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010189" CHECK ("LOCK_NAME" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_LOCKS_PK" ON "SHIPPING"."QRTZ_LOCKS" ("SCHED_NAME", "LOCK_NAME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_LOCKS IS '存储的悲观锁信息表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_LOCKS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_LOCKS.LOCK_NAME IS '悲观锁名称';

CREATE TABLE "SHIPPING"."QRTZ_PAUSED_TRIGGER_GRPS"
(
    "SCHED_NAME"    VARCHAR2(120) NOT NULL ENABLE,
    "TRIGGER_GROUP" VARCHAR2(200) NOT NULL ENABLE,
    CONSTRAINT "QRTZ_PAUSED_TRIG_GRPS_PK" PRIMARY KEY ("SCHED_NAME", "TRIGGER_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010170" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010171" CHECK ("TRIGGER_GROUP" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_PAUSED_TRIG_GRPS_PK" ON "SHIPPING"."QRTZ_PAUSED_TRIGGER_GRPS" ("SCHED_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_PAUSED_TRIGGER_GRPS IS '暂停的触发器表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_PAUSED_TRIGGER_GRPS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_PAUSED_TRIGGER_GRPS.TRIGGER_GROUP IS 'qrtz_triggers表trigger_group的外键';

CREATE TABLE "SHIPPING"."QRTZ_SCHEDULER_STATE"
(
    "SCHED_NAME"        VARCHAR2(120) NOT NULL ENABLE,
    "INSTANCE_NAME"     VARCHAR2(200) NOT NULL ENABLE,
    "LAST_CHECKIN_TIME" NUMBER(13, 0) NOT NULL ENABLE,
    "CHECKIN_INTERVAL"  NUMBER(13, 0) NOT NULL ENABLE,
    CONSTRAINT "QRTZ_SCHEDULER_STATE_PK" PRIMARY KEY ("SCHED_NAME", "INSTANCE_NAME")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010183" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010184" CHECK ("INSTANCE_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010185" CHECK ("LAST_CHECKIN_TIME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010186" CHECK ("CHECKIN_INTERVAL" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_SCHEDULER_STATE_PK" ON "SHIPPING"."QRTZ_SCHEDULER_STATE" ("SCHED_NAME", "INSTANCE_NAME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_SCHEDULER_STATE IS '调度器状态表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SCHEDULER_STATE.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SCHEDULER_STATE.INSTANCE_NAME IS '实例名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SCHEDULER_STATE.LAST_CHECKIN_TIME IS '上次检查时间';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SCHEDULER_STATE.CHECKIN_INTERVAL IS '检查间隔时间';

CREATE TABLE "SHIPPING"."QRTZ_SIMPLE_TRIGGERS"
(
    "SCHED_NAME"      VARCHAR2(120) NOT NULL ENABLE,
    "TRIGGER_NAME"    VARCHAR2(200) NOT NULL ENABLE,
    "TRIGGER_GROUP"   VARCHAR2(200) NOT NULL ENABLE,
    "REPEAT_COUNT"    NUMBER(7, 0)  NOT NULL ENABLE,
    "REPEAT_INTERVAL" NUMBER(12, 0) NOT NULL ENABLE,
    "TIMES_TRIGGERED" NUMBER(10, 0) NOT NULL ENABLE,
    CONSTRAINT "QRTZ_SIMPLE_TRIG_PK" PRIMARY KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010147" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010148" CHECK ("TRIGGER_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010149" CHECK ("TRIGGER_GROUP" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010150" CHECK ("REPEAT_COUNT" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010151" CHECK ("REPEAT_INTERVAL" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010152" CHECK ("TIMES_TRIGGERED" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."QRTZ_SIMPLE_TRIGGERS"
    ADD CONSTRAINT "QRTZ_SIMPLE_TRIG_TO_TRIG_FK" FOREIGN KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        REFERENCES "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_SIMPLE_TRIG_PK" ON "SHIPPING"."QRTZ_SIMPLE_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_SIMPLE_TRIGGERS IS '简单触发器的信息表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPLE_TRIGGERS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPLE_TRIGGERS.TRIGGER_NAME IS 'qrtz_triggers表trigger_name的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPLE_TRIGGERS.TRIGGER_GROUP IS 'qrtz_triggers表trigger_group的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPLE_TRIGGERS.REPEAT_COUNT IS '重复的次数统计';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPLE_TRIGGERS.REPEAT_INTERVAL IS '重复的间隔时间';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPLE_TRIGGERS.TIMES_TRIGGERED IS '已经触发的次数';

CREATE TABLE "SHIPPING"."QRTZ_SIMPROP_TRIGGERS"
(
    "SCHED_NAME"    VARCHAR2(120) NOT NULL ENABLE,
    "TRIGGER_NAME"  VARCHAR2(200) NOT NULL ENABLE,
    "TRIGGER_GROUP" VARCHAR2(200) NOT NULL ENABLE,
    "STR_PROP_1"    VARCHAR2(512),
    "STR_PROP_2"    VARCHAR2(512),
    "STR_PROP_3"    VARCHAR2(512),
    "INT_PROP_1"    NUMBER(10, 0),
    "INT_PROP_2"    NUMBER(10, 0),
    "LONG_PROP_1"   NUMBER(13, 0),
    "LONG_PROP_2"   NUMBER(13, 0),
    "DEC_PROP_1"    NUMBER(13, 4),
    "DEC_PROP_2"    NUMBER(13, 4),
    "BOOL_PROP_1"   VARCHAR2(1),
    "BOOL_PROP_2"   VARCHAR2(1),
    CONSTRAINT "QRTZ_SIMPROP_TRIG_PK" PRIMARY KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010191" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010192" CHECK ("TRIGGER_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010193" CHECK ("TRIGGER_GROUP" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."QRTZ_SIMPROP_TRIGGERS"
    ADD CONSTRAINT "QRTZ_SIMPROP_TRIG_TO_TRIG_FK" FOREIGN KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        REFERENCES "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."QRTZ_SIMPROP_TRIG_PK" ON "SHIPPING"."QRTZ_SIMPROP_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_SIMPROP_TRIGGERS IS '同步机制的行锁表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.TRIGGER_NAME IS 'qrtz_triggers表trigger_name的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.TRIGGER_GROUP IS 'qrtz_triggers表trigger_group的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.STR_PROP_1 IS 'String类型的trigger的第一个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.STR_PROP_2 IS 'String类型的trigger的第二个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.STR_PROP_3 IS 'String类型的trigger的第三个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.INT_PROP_1 IS 'int类型的trigger的第一个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.INT_PROP_2 IS 'int类型的trigger的第二个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.LONG_PROP_1 IS 'long类型的trigger的第一个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.LONG_PROP_2 IS 'long类型的trigger的第二个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.DEC_PROP_1 IS 'decimal类型的trigger的第一个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.DEC_PROP_2 IS 'decimal类型的trigger的第二个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.BOOL_PROP_1 IS 'Boolean类型的trigger的第一个参数';
COMMENT
    ON COLUMN SHIPPING.QRTZ_SIMPROP_TRIGGERS.BOOL_PROP_2 IS 'Boolean类型的trigger的第二个参数';

CREATE TABLE "SHIPPING"."QRTZ_TRIGGERS"
(
    "SCHED_NAME"     VARCHAR2(120) NOT NULL ENABLE,
    "TRIGGER_NAME"   VARCHAR2(200) NOT NULL ENABLE,
    "TRIGGER_GROUP"  VARCHAR2(200) NOT NULL ENABLE,
    "JOB_NAME"       VARCHAR2(200) NOT NULL ENABLE,
    "JOB_GROUP"      VARCHAR2(200) NOT NULL ENABLE,
    "DESCRIPTION"    VARCHAR2(250),
    "NEXT_FIRE_TIME" NUMBER(13, 0),
    "PREV_FIRE_TIME" NUMBER(13, 0),
    "PRIORITY"       NUMBER(13, 0),
    "TRIGGER_STATE"  VARCHAR2(16)  NOT NULL ENABLE,
    "TRIGGER_TYPE"   VARCHAR2(8)   NOT NULL ENABLE,
    "START_TIME"     NUMBER(13, 0) NOT NULL ENABLE,
    "END_TIME"       NUMBER(13, 0),
    "CALENDAR_NAME"  VARCHAR2(200),
    "MISFIRE_INSTR"  NUMBER(2, 0),
    "JOB_DATA"       BLOB,
    CONSTRAINT "QRTZ_TRIGGERS_PK" PRIMARY KEY ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010137" CHECK ("SCHED_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010138" CHECK ("TRIGGER_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010139" CHECK ("TRIGGER_GROUP" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010140" CHECK ("JOB_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010141" CHECK ("JOB_GROUP" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010142" CHECK ("TRIGGER_STATE" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010143" CHECK ("TRIGGER_TYPE" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010144" CHECK ("START_TIME" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("JOB_DATA") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
);

CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077089C00016$$" ON "SHIPPING"."QRTZ_TRIGGERS" (
                                                                                         PCTFREE 10 INITRANS 2 MAXTRANS
                                                                                         255
                                                                                         STORAGE (INITIAL 65536 NEXT
                                                                                         1048576 MAXEXTENTS **********
                                                                                         BUFFER_POOL DEFAULT FLASH_CACHE
                                                                                         DEFAULT CELL_FLASH_CACHE
                                                                                         DEFAULT)
                                                                                         TABLESPACE "SHIPPING"
                                                                                         PARALLEL (DEGREE 0 INSTANCES
                                                                                         0);
CREATE UNIQUE INDEX "SHIPPING"."QRTZ_TRIGGERS_PK" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_C" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "CALENDAR_NAME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_G" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_J" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "JOB_NAME", "JOB_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_JG" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "JOB_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_NEXT_FIRE_TIME" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "NEXT_FIRE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_NFT_MISFIRE" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "MISFIRE_INSTR", "NEXT_FIRE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_NFT_ST" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_STATE", "NEXT_FIRE_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_NFT_ST_MISFIRE" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "MISFIRE_INSTR", "NEXT_FIRE_TIME", "TRIGGER_STATE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_NFT_ST_MISFIRE_GRP" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "MISFIRE_INSTR",
                                                                                       "NEXT_FIRE_TIME",
                                                                                       "TRIGGER_GROUP",
                                                                                       "TRIGGER_STATE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_N_G_STATE" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_GROUP", "TRIGGER_STATE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_N_STATE" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP", "TRIGGER_STATE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_QRTZ_T_STATE" ON "SHIPPING"."QRTZ_TRIGGERS" ("SCHED_NAME", "TRIGGER_STATE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.QRTZ_TRIGGERS IS '触发器详细信息表';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.SCHED_NAME IS '调度名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.TRIGGER_NAME IS '触发器的名字';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.TRIGGER_GROUP IS '触发器所属组的名字';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.JOB_NAME IS 'qrtz_job_details表job_name的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.JOB_GROUP IS 'qrtz_job_details表job_group的外键';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.DESCRIPTION IS '相关介绍';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.NEXT_FIRE_TIME IS '上一次触发时间（毫秒）';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.PREV_FIRE_TIME IS '下一次触发时间（默认为-1表示不触发）';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.PRIORITY IS '优先级';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.TRIGGER_STATE IS '触发器状态';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.TRIGGER_TYPE IS '触发器的类型';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.START_TIME IS '开始时间';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.END_TIME IS '结束时间';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.CALENDAR_NAME IS '日程表名称';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.MISFIRE_INSTR IS '补偿执行的策略';
COMMENT
    ON COLUMN SHIPPING.QRTZ_TRIGGERS.JOB_DATA IS '存放持久化job对象';

CREATE TABLE "SHIPPING"."RELATION_BOOKING_VOYAGE_CNTR_COARSE"
(
    "ID"                              VARCHAR2(255) NOT NULL ENABLE,
    "RELATION_BOOKING_VOYAGE_MAIN_ID" VARCHAR2(255),
    "CNTR_SIZE"                       NUMBER,
    "FULL_EMPTY_FLAG"                 VARCHAR2(255),
    "QUANTITY"                        NUMBER,
    "DANGER_LEVEL"                    VARCHAR2(255),
    "IS_REEFER"                       VARCHAR2(255),
    "REMARK"                          VARCHAR2(255),
    "CREATE_BY"                       VARCHAR2(64),
    "CREATE_TIME"                     DATE,
    "UPDATE_BY"                       VARCHAR2(64),
    "UPDATE_TIME"                     DATE,
    "DEL_FLAG"                        VARCHAR2(1),
    "VERSION"                         NUMBER(10, 0),
    "BOOKING_CNTR_COARSE_ID"          VARCHAR2(255),
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010666" ON "SHIPPING"."RELATION_BOOKING_VOYAGE_CNTR_COARSE" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.ID IS '主键(雪花)';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.RELATION_BOOKING_VOYAGE_MAIN_ID IS '货船匹配主表ID';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.CNTR_SIZE IS '箱尺寸';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.FULL_EMPTY_FLAG IS '空重';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.QUANTITY IS '箱量';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.DANGER_LEVEL IS '危险品等级';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.IS_REEFER IS '冷藏标志';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.DEL_FLAG IS '逻辑删除标志';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_CNTR_COARSE.BOOKING_CNTR_COARSE_ID IS 'BOOKING_CNTR_COARSE_ID';

CREATE TABLE "SHIPPING"."RELATION_BOOKING_VOYAGE_MAIN"
(
    "ID"           VARCHAR2(255) NOT NULL ENABLE,
    "BOOKING_ID"   VARCHAR2(255),
    "VOYAGE_ID"    VARCHAR2(255),
    "START_SEQ_ID" VARCHAR2(255),
    "END_SEQ_ID"   VARCHAR2(255),
    "REMARK"       VARCHAR2(255),
    "CREATE_BY"    VARCHAR2(64),
    "CREATE_TIME"  DATE,
    "UPDATE_BY"    VARCHAR2(64),
    "UPDATE_TIME"  DATE,
    "DEL_FLAG"     VARCHAR2(1),
    "VERSION"      NUMBER(10, 0),
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010664" ON "SHIPPING"."RELATION_BOOKING_VOYAGE_MAIN" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.ID IS 'ID';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.BOOKING_ID IS '航运需求ID';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.VOYAGE_ID IS '航线ID';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.START_SEQ_ID IS '启运停靠港';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.END_SEQ_ID IS '结束停靠港';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.DEL_FLAG IS '逻辑删除标志';
COMMENT
    ON COLUMN SHIPPING.RELATION_BOOKING_VOYAGE_MAIN.VERSION IS '乐观锁';

CREATE TABLE "SHIPPING"."ROUTE_BRANCH"
(
    "BRANCH_ID"   VARCHAR2(32)  NOT NULL ENABLE,
    "BRANCH_NAME" VARCHAR2(255) NOT NULL ENABLE,
    "MAIN_ID"     VARCHAR2(32)  NOT NULL ENABLE,
    "REMARK"      VARCHAR2(255),
    "CREATE_BY"   VARCHAR2(50),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(50),
    "UPDATE_TIME" DATE,
    "DEL_FLAG"    VARCHAR2(1)   DEFAULT '0',
    "VERSION"     NUMBER(10, 0) DEFAULT 0,
    CONSTRAINT "PK_ROUTE_BRANCH" PRIMARY KEY ("BRANCH_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."ROUTE_BRANCH"
    ADD CONSTRAINT "FK_BRANCH_MAIN" FOREIGN KEY ("MAIN_ID")
        REFERENCES "SHIPPING"."ROUTE_MAIN" ("MAIN_ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."PK_ROUTE_BRANCH" ON "SHIPPING"."ROUTE_BRANCH" ("BRANCH_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.ROUTE_BRANCH IS '航线支线表';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.BRANCH_ID IS '支线ID（主键），雪花ID';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.BRANCH_NAME IS '支线名称';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.MAIN_ID IS '所属主线ID（外键）';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.DEL_FLAG IS '逻辑删除标志';
COMMENT
    ON COLUMN SHIPPING.ROUTE_BRANCH.VERSION IS '乐观锁';

CREATE TABLE "SHIPPING"."ROUTE_MAIN"
(
    "MAIN_ID"     VARCHAR2(32)  NOT NULL ENABLE,
    "MAIN_NAME"   VARCHAR2(255) NOT NULL ENABLE,
    "REMARK"      VARCHAR2(255),
    "CREATE_BY"   VARCHAR2(50),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(50),
    "UPDATE_TIME" DATE,
    "DEL_FLAG"    VARCHAR2(1)   DEFAULT '0',
    "VERSION"     NUMBER(10, 0) DEFAULT 0,
    CONSTRAINT "PK_ROUTE_MAIN" PRIMARY KEY ("MAIN_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_ROUTE_MAIN" ON "SHIPPING"."ROUTE_MAIN" ("MAIN_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.ROUTE_MAIN IS '航线主线表';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.MAIN_ID IS '主线ID（主键），雪花ID';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.MAIN_NAME IS '主线名称';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.DEL_FLAG IS '逻辑删除标志';
COMMENT
    ON COLUMN SHIPPING.ROUTE_MAIN.VERSION IS '乐观锁';

CREATE TABLE "SHIPPING"."ROUTE_RELATION"
(
    "RELATION_ID"    VARCHAR2(32) NOT NULL ENABLE,
    "BRANCH_ID"      VARCHAR2(32) NOT NULL ENABLE,
    "START_POINT_ID" VARCHAR2(50) NOT NULL ENABLE,
    "END_POINT_ID"   VARCHAR2(50) NOT NULL ENABLE,
    "DISTANCE_KM"    NUMBER(10, 2),
    "DURATION_HOUR"  NUMBER(10, 2),
    "REMARK"         VARCHAR2(255),
    "CREATE_BY"      VARCHAR2(50),
    "CREATE_TIME"    DATE,
    "UPDATE_BY"      VARCHAR2(50),
    "UPDATE_TIME"    DATE,
    "DEL_FLAG"       VARCHAR2(1)   DEFAULT '0',
    "VERSION"        NUMBER(10, 0) DEFAULT 0,
    CONSTRAINT "PK_ROUTE_RELATION" PRIMARY KEY ("RELATION_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

ALTER TABLE "SHIPPING"."ROUTE_RELATION"
    ADD CONSTRAINT "FK_RELATION_BRANCH" FOREIGN KEY ("BRANCH_ID")
        REFERENCES "SHIPPING"."ROUTE_BRANCH" ("BRANCH_ID") ENABLE;
ALTER TABLE "SHIPPING"."ROUTE_RELATION"
    ADD CONSTRAINT "FK_RELATION_START_TERMINAL" FOREIGN KEY ("START_POINT_ID")
        REFERENCES "SHIPPING"."BASIC_TERMINAL" ("ID") ENABLE;
ALTER TABLE "SHIPPING"."ROUTE_RELATION"
    ADD CONSTRAINT "FK_RELATION_END_TERMINAL" FOREIGN KEY ("END_POINT_ID")
        REFERENCES "SHIPPING"."BASIC_TERMINAL" ("ID") ENABLE;

CREATE UNIQUE INDEX "SHIPPING"."PK_ROUTE_RELATION" ON "SHIPPING"."ROUTE_RELATION" ("RELATION_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.ROUTE_RELATION IS '码头连接关系表';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.RELATION_ID IS '连接ID（主键），雪花ID';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.BRANCH_ID IS '关联支线表ID（外键）';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.START_POINT_ID IS '起点码头ID（外键，关联BASIC_TERMINAL表）';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.END_POINT_ID IS '终点码头ID（外键，关联BASIC_TERMINAL表）';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.DISTANCE_KM IS '距离（公里）';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.DURATION_HOUR IS '时间（小时）';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.DEL_FLAG IS '逻辑删除标志';
COMMENT
    ON COLUMN SHIPPING.ROUTE_RELATION.VERSION IS '乐观锁';

CREATE TABLE "SHIPPING"."SYS_CONFIG"
(
    "CONFIG_ID"    NUMBER(20, 0) NOT NULL ENABLE,
    "CONFIG_NAME"  VARCHAR2(100) DEFAULT '',
    "CONFIG_KEY"   VARCHAR2(100) DEFAULT '',
    "CONFIG_VALUE" VARCHAR2(100) DEFAULT '',
    "CONFIG_TYPE"  CHAR(1)       DEFAULT 'N',
    "CREATE_BY"    VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME"  DATE,
    "UPDATE_BY"    VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME"  DATE,
    "REMARK"       VARCHAR2(500) DEFAULT NULL,
    CONSTRAINT "PK_SYS_CONFIG" PRIMARY KEY ("CONFIG_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010101" CHECK ("CONFIG_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_CONFIG" ON "SHIPPING"."SYS_CONFIG" ("CONFIG_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_CONFIG IS '参数配置表';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.CONFIG_ID IS '参数主键seq_sys_config.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.CONFIG_NAME IS '参数名称';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.CONFIG_KEY IS '参数键名';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.CONFIG_VALUE IS '参数键值';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.CONFIG_TYPE IS '系统内置（Y是 N否）';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_CONFIG.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_DEPT"
(
    "DEPT_ID"     NUMBER(20, 0) NOT NULL ENABLE,
    "PARENT_ID"   NUMBER(20, 0) DEFAULT 0,
    "ANCESTORS"   VARCHAR2(50)  DEFAULT '',
    "DEPT_NAME"   VARCHAR2(30)  DEFAULT '',
    "ORDER_NUM"   NUMBER(4, 0)  DEFAULT 0,
    "LEADER"      VARCHAR2(20)  DEFAULT NULL,
    "PHONE"       VARCHAR2(11)  DEFAULT NULL,
    "EMAIL"       VARCHAR2(50)  DEFAULT NULL,
    "STATUS"      CHAR(1)       DEFAULT '0',
    "DEL_FLAG"    CHAR(1)       DEFAULT '0',
    "CREATE_BY"   VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME" DATE,
    "REMARK"      VARCHAR2(255),
    CONSTRAINT "PK_SYS_DEPT" PRIMARY KEY ("DEPT_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010063" CHECK ("DEPT_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_DEPT" ON "SHIPPING"."SYS_DEPT" ("DEPT_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_DEPT IS '部门信息表';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.DEPT_ID IS '部门主键seq_sys_dept.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.PARENT_ID IS '父部门id';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.ANCESTORS IS '祖级列表';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.DEPT_NAME IS '部门名称';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.ORDER_NUM IS '显示顺序';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.LEADER IS '负责人';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.PHONE IS '联系电话';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.EMAIL IS '邮箱';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.STATUS IS '部门状态（0正常 1停用）';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.DEL_FLAG IS '删除标志（0代表存在 2代表删除）';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_DEPT.UPDATE_TIME IS '更新时间';

CREATE TABLE "SHIPPING"."SYS_DICT_DATA"
(
    "DICT_CODE"   NUMBER(20, 0) NOT NULL ENABLE,
    "DICT_SORT"   NUMBER(4, 0)  DEFAULT 0,
    "DICT_LABEL"  VARCHAR2(100) DEFAULT '',
    "DICT_VALUE"  VARCHAR2(100) DEFAULT '',
    "DICT_TYPE"   VARCHAR2(100) DEFAULT '',
    "CSS_CLASS"   VARCHAR2(100) DEFAULT NULL,
    "LIST_CLASS"  VARCHAR2(100) DEFAULT NULL,
    "IS_DEFAULT"  CHAR(1)       DEFAULT 'N',
    "STATUS"      CHAR(1)       DEFAULT '0',
    "CREATE_BY"   VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME" DATE,
    "REMARK"      VARCHAR2(500) DEFAULT NULL,
    CONSTRAINT "PK_SYS_DICT_DATA" PRIMARY KEY ("DICT_CODE")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010099" CHECK ("DICT_CODE" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_DICT_DATA" ON "SHIPPING"."SYS_DICT_DATA" ("DICT_CODE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_DICT_DATA IS '字典数据表';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.DICT_CODE IS '字典主键seq_sys_dict_data.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.DICT_SORT IS '字典排序';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.DICT_LABEL IS '字典标签';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.DICT_VALUE IS '字典键值';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.DICT_TYPE IS '字典类型';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.CSS_CLASS IS '样式属性（其他样式扩展）';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.LIST_CLASS IS '表格回显样式';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.IS_DEFAULT IS '是否默认（Y是 N否）';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.STATUS IS '状态（0正常 1停用）';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_DATA.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_DICT_TYPE"
(
    "DICT_ID"     NUMBER(20, 0) NOT NULL ENABLE,
    "DICT_NAME"   VARCHAR2(100) DEFAULT '',
    "DICT_TYPE"   VARCHAR2(100) DEFAULT '',
    "STATUS"      CHAR(1)       DEFAULT '0',
    "CREATE_BY"   VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME" DATE,
    "REMARK"      VARCHAR2(500) DEFAULT NULL,
    CONSTRAINT "PK_SYS_DICT_TYPE" PRIMARY KEY ("DICT_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010097" CHECK ("DICT_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_DICT_TYPE" ON "SHIPPING"."SYS_DICT_TYPE" ("DICT_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE UNIQUE INDEX "SHIPPING"."SYS_DICT_TYPE_INDEX1" ON "SHIPPING"."SYS_DICT_TYPE" ("DICT_TYPE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_DICT_TYPE IS '字典类型表';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.DICT_ID IS '字典主键seq_sys_dict_type.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.DICT_NAME IS '字典名称';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.DICT_TYPE IS '字典类型';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.STATUS IS '状态（0正常 1停用）';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_DICT_TYPE.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_FILE_MANAGE"
(
    "FILE_ID"          VARCHAR2(50) NOT NULL ENABLE,
    "FILE_NAME"        VARCHAR2(255),
    "FILE_PATH"        VARCHAR2(4000),
    "FILE_TYPE_ID"     VARCHAR2(255),
    "FILE_BUSINESS_ID" VARCHAR2(255),
    "DEL_FLAG"         VARCHAR2(10) DEFAULT '0',
    "CREATE_BY"        VARCHAR2(255),
    "CREATE_TIME"      DATE,
    "UPDATE_BY"        VARCHAR2(255),
    "UPDATE_TIME"      DATE,
    "REMARK"           VARCHAR2(255),
    CONSTRAINT "SYS_C0010197" PRIMARY KEY ("FILE_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010197" ON "SHIPPING"."SYS_FILE_MANAGE" ("FILE_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.SYS_FILE_MANAGE.FILE_NAME IS '文件名';
COMMENT
    ON COLUMN SHIPPING.SYS_FILE_MANAGE.FILE_PATH IS '文件路径';
COMMENT
    ON COLUMN SHIPPING.SYS_FILE_MANAGE.FILE_TYPE_ID IS '关联文件类型ID';
COMMENT
    ON COLUMN SHIPPING.SYS_FILE_MANAGE.FILE_BUSINESS_ID IS '业务ID';

CREATE TABLE "SHIPPING"."SYS_FILE_TYPE"
(
    "FILE_TYPE"          VARCHAR2(255),
    "FILE_BUSINESS_TYPE" VARCHAR2(255),
    "DEL_FLAG"           VARCHAR2(10) DEFAULT '0',
    "LIMIT_NUM"          NUMBER(10, 0),
    "FILE_SORT"          NUMBER(10, 0),
    "CREATE_BY"          VARCHAR2(255),
    "CREATE_TIME"        DATE,
    "UPDATE_BY"          VARCHAR2(255),
    "UPDATE_TIME"        DATE,
    "REMARK"             VARCHAR2(255),
    "FILE_TYPE_ID"       VARCHAR2(255),
    PRIMARY KEY ("FILE_TYPE_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010678" ON "SHIPPING"."SYS_FILE_TYPE" ("FILE_TYPE_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.SYS_FILE_TYPE.FILE_TYPE IS '文件类型 一级';
COMMENT
    ON COLUMN SHIPPING.SYS_FILE_TYPE.FILE_BUSINESS_TYPE IS '文件类型 二级';
COMMENT
    ON COLUMN SHIPPING.SYS_FILE_TYPE.LIMIT_NUM IS '文件限制数量 0表示不限制';
COMMENT
    ON COLUMN SHIPPING.SYS_FILE_TYPE.FILE_SORT IS '文件类型排序';
COMMENT
    ON COLUMN SHIPPING.SYS_FILE_TYPE.CREATE_BY IS '创建人';

CREATE TABLE "SHIPPING"."SYS_JOB"
(
    "JOB_ID"          NUMBER(20, 0)            NOT NULL ENABLE,
    "JOB_NAME"        VARCHAR2(64)  DEFAULT '' NOT NULL ENABLE,
    "JOB_GROUP"       VARCHAR2(64)  DEFAULT '' NOT NULL ENABLE,
    "INVOKE_TARGET"   VARCHAR2(500)            NOT NULL ENABLE,
    "CRON_EXPRESSION" VARCHAR2(255) DEFAULT '',
    "MISFIRE_POLICY"  VARCHAR2(20)  DEFAULT '3',
    "CONCURRENT"      CHAR(1)       DEFAULT '1',
    "STATUS"          CHAR(1)       DEFAULT '0',
    "CREATE_BY"       VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME"     DATE,
    "UPDATE_BY"       VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME"     DATE,
    "REMARK"          VARCHAR2(500) DEFAULT '',
    CONSTRAINT "PK_SYS_JOB" PRIMARY KEY ("JOB_ID", "JOB_NAME", "JOB_GROUP")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010105" CHECK ("JOB_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010106" CHECK ("INVOKE_TARGET" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_JOB" ON "SHIPPING"."SYS_JOB" ("JOB_ID", "JOB_NAME", "JOB_GROUP") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_JOB IS '定时任务调度表';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.JOB_ID IS '任务主键seq_sys_job.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.JOB_NAME IS '任务名称';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.JOB_GROUP IS '任务组名';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.INVOKE_TARGET IS '调用目标字符串';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.CRON_EXPRESSION IS 'cron执行表达式';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.MISFIRE_POLICY IS '计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.CONCURRENT IS '是否并发执行（0允许 1禁止）';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.STATUS IS '状态（0正常 1暂停）';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB.REMARK IS '备注信息';

CREATE TABLE "SHIPPING"."SYS_JOB_LOG"
(
    "JOB_LOG_ID"     NUMBER(20, 0) NOT NULL ENABLE,
    "JOB_NAME"       VARCHAR2(64)  NOT NULL ENABLE,
    "JOB_GROUP"      VARCHAR2(64)  NOT NULL ENABLE,
    "INVOKE_TARGET"  VARCHAR2(500) NOT NULL ENABLE,
    "JOB_MESSAGE"    VARCHAR2(500),
    "STATUS"         CHAR(1)        DEFAULT '0',
    "EXCEPTION_INFO" VARCHAR2(2000) DEFAULT '',
    "CREATE_TIME"    DATE,
    CONSTRAINT "PK_SYS_JOB_LOG" PRIMARY KEY ("JOB_LOG_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010108" CHECK ("JOB_LOG_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010109" CHECK ("JOB_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010110" CHECK ("JOB_GROUP" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010111" CHECK ("INVOKE_TARGET" IS NOT NULL) ENABLE
) SEGMENT CREATION DEFERRED
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_JOB_LOG" ON "SHIPPING"."SYS_JOB_LOG" ("JOB_LOG_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_JOB_LOG IS '定时任务调度日志表';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.JOB_LOG_ID IS '日志主键seq_sys_job_log.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.JOB_NAME IS '任务名称';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.JOB_GROUP IS '任务组名';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.INVOKE_TARGET IS '调用目标字符串';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.JOB_MESSAGE IS '日志信息';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.STATUS IS '执行状态（0正常 1失败）';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.EXCEPTION_INFO IS '异常信息';
COMMENT
    ON COLUMN SHIPPING.SYS_JOB_LOG.CREATE_TIME IS '创建时间';

CREATE TABLE "SHIPPING"."SYS_LOGININFOR"
(
    "INFO_ID"        NUMBER(20, 0) NOT NULL ENABLE,
    "USER_NAME"      VARCHAR2(50)  DEFAULT '',
    "IPADDR"         VARCHAR2(128) DEFAULT '',
    "LOGIN_LOCATION" VARCHAR2(255) DEFAULT '',
    "BROWSER"        VARCHAR2(50)  DEFAULT '',
    "OS"             VARCHAR2(50)  DEFAULT '',
    "STATUS"         CHAR(1)       DEFAULT '0',
    "MSG"            VARCHAR2(255) DEFAULT '',
    "LOGIN_TIME"     DATE,
    CONSTRAINT "PK_SYS_LOGININFOR" PRIMARY KEY ("INFO_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010103" CHECK ("INFO_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_LOGININFOR" ON "SHIPPING"."SYS_LOGININFOR" ("INFO_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_SYS_LOGININFOR_LT" ON "SHIPPING"."SYS_LOGININFOR" ("LOGIN_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_SYS_LOGININFOR_S" ON "SHIPPING"."SYS_LOGININFOR" ("STATUS") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_LOGININFOR IS '系统访问记录';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.INFO_ID IS '访问主键seq_seq_sys_logininfor.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.USER_NAME IS '登录账号';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.IPADDR IS '登录IP地址';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.LOGIN_LOCATION IS '登录地点';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.BROWSER IS '浏览器类型';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.OS IS '操作系统';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.STATUS IS '登录状态（0成功 1失败）';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.MSG IS '提示消息';
COMMENT
    ON COLUMN SHIPPING.SYS_LOGININFOR.LOGIN_TIME IS '访问时间';

CREATE TABLE "SHIPPING"."SYS_MENU"
(
    "MENU_ID"     NUMBER(20, 0) NOT NULL ENABLE,
    "MENU_NAME"   VARCHAR2(50)  NOT NULL ENABLE,
    "PARENT_ID"   NUMBER(20, 0) DEFAULT 0,
    "ORDER_NUM"   NUMBER(4, 0)  DEFAULT 0,
    "PATH"        VARCHAR2(200) DEFAULT '',
    "COMPONENT"   VARCHAR2(255) DEFAULT NULL,
    "QUERY"       VARCHAR2(255) DEFAULT NULL,
    "ROUTE_NAME"  VARCHAR2(50)  DEFAULT '',
    "IS_FRAME"    NUMBER(1, 0)  DEFAULT 1,
    "IS_CACHE"    NUMBER(1, 0)  DEFAULT 0,
    "MENU_TYPE"   CHAR(1)       DEFAULT '',
    "VISIBLE"     CHAR(1)       DEFAULT 0,
    "STATUS"      CHAR(1)       DEFAULT 0,
    "PERMS"       VARCHAR2(100) DEFAULT NULL,
    "ICON"        VARCHAR2(100) DEFAULT '#',
    "CREATE_BY"   VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME" DATE,
    "REMARK"      VARCHAR2(500) DEFAULT '',
    CONSTRAINT "PK_SYS_MENU" PRIMARY KEY ("MENU_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010080" CHECK ("MENU_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010081" CHECK ("MENU_NAME" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_MENU" ON "SHIPPING"."SYS_MENU" ("MENU_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_MENU IS '菜单权限表';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.MENU_ID IS '菜单主键seq_sys_post.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.MENU_NAME IS '菜单名称';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.PARENT_ID IS '父菜单ID';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.ORDER_NUM IS '显示顺序';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU."PATH" IS '请求地址';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.COMPONENT IS '路由地址';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.QUERY IS '路由参数';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.ROUTE_NAME IS '路由名称';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.IS_FRAME IS '是否为外链（0是 1否）';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.IS_CACHE IS '是否缓存（0缓存 1不缓存）';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.MENU_TYPE IS '菜单类型（M目录 C菜单 F按钮）';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.VISIBLE IS '菜单状态（0显示 1隐藏）';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.STATUS IS '菜单状态（0正常 1停用）';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.PERMS IS '权限标识';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.ICON IS '菜单图标';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_MENU.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_NOTICE"
(
    "NOTICE_ID"      NUMBER(20, 0) NOT NULL ENABLE,
    "NOTICE_TITLE"   VARCHAR2(50)  NOT NULL ENABLE,
    "NOTICE_TYPE"    CHAR(1)       NOT NULL ENABLE,
    "NOTICE_CONTENT" CLOB          DEFAULT NULL,
    "STATUS"         CHAR(1)       DEFAULT '0',
    "CREATE_BY"      VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME"    DATE,
    "UPDATE_BY"      VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME"    DATE,
    "REMARK"         VARCHAR2(255) DEFAULT NULL,
    CONSTRAINT "PK_SYS_NOTICE" PRIMARY KEY ("NOTICE_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010113" CHECK ("NOTICE_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010114" CHECK ("NOTICE_TITLE" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010115" CHECK ("NOTICE_TYPE" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("NOTICE_CONTENT") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
    STORAGE
(
    INITIAL
    106496
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
));

CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077102C00004$$" ON "SHIPPING"."SYS_NOTICE" (
                                                                                      PCTFREE 10 INITRANS 2 MAXTRANS 255
                                                                                      STORAGE (INITIAL 65536 NEXT
                                                                                      1048576 MINEXTENTS 1 MAXEXTENTS
                                                                                      **********
                                                                                      PCTINCREASE 0 FREELISTS 1 FREELIST
                                                                                      GROUPS 1
                                                                                      BUFFER_POOL DEFAULT FLASH_CACHE
                                                                                      DEFAULT CELL_FLASH_CACHE DEFAULT)
                                                                                      TABLESPACE "SHIPPING"
                                                                                      PARALLEL (DEGREE 0 INSTANCES 0);
CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_NOTICE" ON "SHIPPING"."SYS_NOTICE" ("NOTICE_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_NOTICE IS '通知公告表';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.NOTICE_ID IS '公告主键seq_sys_notice.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.NOTICE_TITLE IS '公告标题';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.NOTICE_TYPE IS '公告类型（1通知 2公告）';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.NOTICE_CONTENT IS '公告内容';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.STATUS IS '公告状态（0正常 1关闭）';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_NOTICE.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_OPER_LOG"
(
    "OPER_ID"        NUMBER(20, 0) NOT NULL ENABLE,
    "TITLE"          VARCHAR2(50)  DEFAULT '',
    "BUSINESS_TYPE"  NUMBER(2, 0)  DEFAULT 0,
    "METHOD"         VARCHAR2(200) DEFAULT '',
    "REQUEST_METHOD" VARCHAR2(10)  DEFAULT '',
    "OPERATOR_TYPE"  NUMBER(1, 0)  DEFAULT 0,
    "OPER_NAME"      VARCHAR2(50)  DEFAULT '',
    "DEPT_NAME"      VARCHAR2(50)  DEFAULT '',
    "OPER_URL"       VARCHAR2(255) DEFAULT '',
    "OPER_IP"        VARCHAR2(128) DEFAULT '',
    "OPER_LOCATION"  VARCHAR2(255) DEFAULT '',
    "OPER_PARAM"     CLOB          DEFAULT '',
    "JSON_RESULT"    CLOB          DEFAULT '',
    "STATUS"         NUMBER(1, 0)  DEFAULT 0,
    "ERROR_MSG"      CLOB          DEFAULT '',
    "COST_TIME"      NUMBER(20, 0) DEFAULT 0,
    "OPER_TIME"      DATE,
    CONSTRAINT "PK_SYS_OPER_LOG" PRIMARY KEY ("OPER_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010202" CHECK ("OPER_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING"
    LOB ("OPER_PARAM") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
    STORAGE
(
    INITIAL
    106496
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
))
    LOB ("JSON_RESULT") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
    STORAGE
(
    INITIAL
    106496
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
))
    LOB ("ERROR_MSG") STORE AS SECUREFILE
(
    TABLESPACE
    "SHIPPING"
    ENABLE
    STORAGE
    IN
    ROW
    CHUNK
    8192
    NOCACHE
    LOGGING
    NOCOMPRESS
    KEEP_DUPLICATES
    STORAGE
(
    INITIAL
    106496
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
));

CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077105C00015$$" ON "SHIPPING"."SYS_OPER_LOG" (
                                                                                        PCTFREE 10 INITRANS 2 MAXTRANS
                                                                                        255
                                                                                        STORAGE (INITIAL 65536 NEXT
                                                                                        1048576 MINEXTENTS 1 MAXEXTENTS
                                                                                        **********
                                                                                        PCTINCREASE 0 FREELISTS 1
                                                                                        FREELIST GROUPS 1
                                                                                        BUFFER_POOL DEFAULT FLASH_CACHE
                                                                                        DEFAULT CELL_FLASH_CACHE
                                                                                        DEFAULT)
                                                                                        TABLESPACE "SHIPPING"
                                                                                        PARALLEL (DEGREE 0 INSTANCES 0);
CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077105C00013$$" ON "SHIPPING"."SYS_OPER_LOG" (
                                                                                        PCTFREE 10 INITRANS 2 MAXTRANS
                                                                                        255
                                                                                        STORAGE (INITIAL 65536 NEXT
                                                                                        1048576 MINEXTENTS 1 MAXEXTENTS
                                                                                        **********
                                                                                        PCTINCREASE 0 FREELISTS 1
                                                                                        FREELIST GROUPS 1
                                                                                        BUFFER_POOL DEFAULT FLASH_CACHE
                                                                                        DEFAULT CELL_FLASH_CACHE
                                                                                        DEFAULT)
                                                                                        TABLESPACE "SHIPPING"
                                                                                        PARALLEL (DEGREE 0 INSTANCES 0);
CREATE UNIQUE INDEX "SHIPPING"."SYS_IL0000077105C00012$$" ON "SHIPPING"."SYS_OPER_LOG" (
                                                                                        PCTFREE 10 INITRANS 2 MAXTRANS
                                                                                        255
                                                                                        STORAGE (INITIAL 65536 NEXT
                                                                                        1048576 MINEXTENTS 1 MAXEXTENTS
                                                                                        **********
                                                                                        PCTINCREASE 0 FREELISTS 1
                                                                                        FREELIST GROUPS 1
                                                                                        BUFFER_POOL DEFAULT FLASH_CACHE
                                                                                        DEFAULT CELL_FLASH_CACHE
                                                                                        DEFAULT)
                                                                                        TABLESPACE "SHIPPING"
                                                                                        PARALLEL (DEGREE 0 INSTANCES 0);
CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_OPER_LOG" ON "SHIPPING"."SYS_OPER_LOG" ("OPER_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_SYS_OPER_LOG_BT" ON "SHIPPING"."SYS_OPER_LOG" ("BUSINESS_TYPE") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_SYS_OPER_LOG_OT" ON "SHIPPING"."SYS_OPER_LOG" ("OPER_TIME") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";
CREATE INDEX "SHIPPING"."IDX_SYS_OPER_LOG_S" ON "SHIPPING"."SYS_OPER_LOG" ("STATUS") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_OPER_LOG IS '操作日志记录';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPER_ID IS '日志主键seq_sys_oper_log.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.TITLE IS '模块标题';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.BUSINESS_TYPE IS '业务类型（0其它 1新增 2修改 3删除）';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG."METHOD" IS '方法名称';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.REQUEST_METHOD IS '请求方式';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPERATOR_TYPE IS '操作类别（0其它 1后台用户 2手机端用户）';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPER_NAME IS '操作人员';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.DEPT_NAME IS '部门名称';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPER_URL IS '请求URL';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPER_IP IS '主机地址';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPER_LOCATION IS '操作地点';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPER_PARAM IS '请求参数';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.JSON_RESULT IS '返回参数';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.STATUS IS '操作状态（0正常 1异常）';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.ERROR_MSG IS '错误消息';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.COST_TIME IS '消耗时间';
COMMENT
    ON COLUMN SHIPPING.SYS_OPER_LOG.OPER_TIME IS '操作时间';

CREATE TABLE "SHIPPING"."SYS_POST"
(
    "POST_ID"     NUMBER(20, 0) NOT NULL ENABLE,
    "POST_CODE"   VARCHAR2(64)  NOT NULL ENABLE,
    "POST_NAME"   VARCHAR2(50)  NOT NULL ENABLE,
    "POST_SORT"   NUMBER(4, 0)  NOT NULL ENABLE,
    "STATUS"      CHAR(1)       NOT NULL ENABLE,
    "CREATE_BY"   VARCHAR2(64) DEFAULT '',
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(64) DEFAULT '',
    "UPDATE_TIME" DATE,
    "REMARK"      VARCHAR2(500),
    CONSTRAINT "PK_SYS_POST" PRIMARY KEY ("POST_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010068" CHECK ("POST_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010069" CHECK ("POST_CODE" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010070" CHECK ("POST_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010071" CHECK ("POST_SORT" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010072" CHECK ("STATUS" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_POST" ON "SHIPPING"."SYS_POST" ("POST_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_POST IS '岗位信息表';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.POST_ID IS '岗位主键seq_sys_post.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.POST_CODE IS '岗位编码';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.POST_NAME IS '岗位名称';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.POST_SORT IS '显示顺序';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.STATUS IS '状态（0正常 1停用）';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_POST.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_ROLE"
(
    "ROLE_ID"             NUMBER(20, 0) NOT NULL ENABLE,
    "ROLE_NAME"           VARCHAR2(30)  NOT NULL ENABLE,
    "ROLE_KEY"            VARCHAR2(100) NOT NULL ENABLE,
    "ROLE_SORT"           NUMBER(4, 0)  NOT NULL ENABLE,
    "DATA_SCOPE"          CHAR(1)       DEFAULT '1',
    "MENU_CHECK_STRICTLY" NUMBER(1, 0)  DEFAULT 1,
    "DEPT_CHECK_STRICTLY" NUMBER(1, 0)  DEFAULT 1,
    "STATUS"              CHAR(1)       NOT NULL ENABLE,
    "DEL_FLAG"            CHAR(1)       DEFAULT '0',
    "CREATE_BY"           VARCHAR2(64)  DEFAULT '',
    "CREATE_TIME"         DATE,
    "UPDATE_BY"           VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME"         DATE,
    "REMARK"              VARCHAR2(500) DEFAULT NULL,
    CONSTRAINT "PK_SYS_ROLE" PRIMARY KEY ("ROLE_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010074" CHECK ("ROLE_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010075" CHECK ("ROLE_NAME" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010076" CHECK ("ROLE_KEY" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010077" CHECK ("ROLE_SORT" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010078" CHECK ("STATUS" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_ROLE" ON "SHIPPING"."SYS_ROLE" ("ROLE_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_ROLE IS '角色信息表';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.ROLE_ID IS '角色主键seq_sys_post.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.ROLE_NAME IS '角色名称';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.ROLE_KEY IS '角色权限字符串';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.ROLE_SORT IS '显示顺序';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.DATA_SCOPE IS '数据范围（1：全部数据权限 2：自定数据权限）';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.MENU_CHECK_STRICTLY IS '菜单树选择项是否关联显示';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.DEPT_CHECK_STRICTLY IS '部门树选择项是否关联显示';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.STATUS IS '角色状态（0正常 1停用）';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.DEL_FLAG IS '删除标志（0代表存在 2代表删除）';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_ROLE_DEPT"
(
    "ROLE_ID" NUMBER(20, 0) NOT NULL ENABLE,
    "DEPT_ID" NUMBER(20, 0) NOT NULL ENABLE,
    CONSTRAINT "PK_SYS_ROLE_DEPT" PRIMARY KEY ("ROLE_ID", "DEPT_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010089" CHECK ("ROLE_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010090" CHECK ("DEPT_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_ROLE_DEPT" ON "SHIPPING"."SYS_ROLE_DEPT" ("ROLE_ID", "DEPT_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_ROLE_DEPT IS '角色和部门关联表';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE_DEPT.ROLE_ID IS '角色ID';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE_DEPT.DEPT_ID IS '部门ID';

CREATE TABLE "SHIPPING"."SYS_ROLE_MENU"
(
    "ROLE_ID" NUMBER(20, 0) NOT NULL ENABLE,
    "MENU_ID" NUMBER(20, 0) NOT NULL ENABLE,
    CONSTRAINT "PK_SYS_ROLE_MENU" PRIMARY KEY ("ROLE_ID", "MENU_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010086" CHECK ("ROLE_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010087" CHECK ("MENU_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_ROLE_MENU" ON "SHIPPING"."SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_ROLE_MENU IS '角色和菜单关联表';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE_MENU.ROLE_ID IS '角色ID';
COMMENT
    ON COLUMN SHIPPING.SYS_ROLE_MENU.MENU_ID IS '菜单ID';

CREATE TABLE "SHIPPING"."SYS_USER"
(
    "USER_ID"     NUMBER(20, 0) NOT NULL ENABLE,
    "DEPT_ID"     NUMBER(20, 0) DEFAULT NULL,
    "USER_NAME"   VARCHAR2(30)  NOT NULL ENABLE,
    "NICK_NAME"   VARCHAR2(30)  DEFAULT '',
    "USER_TYPE"   VARCHAR2(2)   DEFAULT '00',
    "EMAIL"       VARCHAR2(50)  DEFAULT '',
    "PHONENUMBER" VARCHAR2(11)  DEFAULT '',
    "SEX"         CHAR(1)       DEFAULT '0',
    "AVATAR"      VARCHAR2(255) DEFAULT '',
    "PASSWORD"    VARCHAR2(100) DEFAULT '',
    "STATUS"      CHAR(1)       DEFAULT '0',
    "DEL_FLAG"    CHAR(1)       DEFAULT '0',
    "LOGIN_IP"    VARCHAR2(128) DEFAULT '',
    "LOGIN_DATE"  DATE,
    "CREATE_BY"   VARCHAR2(64),
    "CREATE_TIME" DATE,
    "UPDATE_BY"   VARCHAR2(64)  DEFAULT '',
    "UPDATE_TIME" DATE,
    "REMARK"      VARCHAR2(500) DEFAULT '',
    CONSTRAINT "PK_SYS_USER" PRIMARY KEY ("USER_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010065" CHECK ("USER_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010066" CHECK ("USER_NAME" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_USER" ON "SHIPPING"."SYS_USER" ("USER_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_USER IS '用户信息表';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.USER_ID IS '用户主键seq_sys_user.nextval';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.DEPT_ID IS '部门ID';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.USER_NAME IS '用户账号';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.NICK_NAME IS '用户昵称';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.USER_TYPE IS '用户类型（00系统用户 01注册用户）';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.EMAIL IS '用户邮箱';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.PHONENUMBER IS '手机号码';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.SEX IS '用户性别（0男 1女 2未知）';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.AVATAR IS '头像路径';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.PASSWORD IS '密码';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.STATUS IS '帐号状态（0正常 1停用）';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.DEL_FLAG IS '删除标志（0代表存在 2代表删除）';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.LOGIN_IP IS '最后登录IP';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.LOGIN_DATE IS '最后登录时间';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.CREATE_BY IS '创建者';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.UPDATE_BY IS '更新者';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.SYS_USER.REMARK IS '备注';

CREATE TABLE "SHIPPING"."SYS_USER_POST"
(
    "USER_ID" NUMBER(20, 0) NOT NULL ENABLE,
    "POST_ID" NUMBER(20, 0) NOT NULL ENABLE,
    CONSTRAINT "PK_SYS_USER_POST" PRIMARY KEY ("USER_ID", "POST_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010092" CHECK ("USER_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010093" CHECK ("POST_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_USER_POST" ON "SHIPPING"."SYS_USER_POST" ("USER_ID", "POST_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_USER_POST IS '用户与岗位关联表';
COMMENT
    ON COLUMN SHIPPING.SYS_USER_POST.USER_ID IS '用户ID';
COMMENT
    ON COLUMN SHIPPING.SYS_USER_POST.POST_ID IS '岗位ID';

CREATE TABLE "SHIPPING"."SYS_USER_ROLE"
(
    "USER_ID" NUMBER(20, 0) NOT NULL ENABLE,
    "ROLE_ID" NUMBER(20, 0) NOT NULL ENABLE,
    CONSTRAINT "PK_SYS_USER_ROLE" PRIMARY KEY ("USER_ID", "ROLE_ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE,
    CONSTRAINT "SYS_C0010083" CHECK ("USER_ID" IS NOT NULL) ENABLE,
    CONSTRAINT "SYS_C0010084" CHECK ("ROLE_ID" IS NOT NULL) ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."PK_SYS_USER_ROLE" ON "SHIPPING"."SYS_USER_ROLE" ("USER_ID", "ROLE_ID") PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON TABLE SHIPPING.SYS_USER_ROLE IS '用户和角色关联表';
COMMENT
    ON COLUMN SHIPPING.SYS_USER_ROLE.USER_ID IS '用户ID';
COMMENT
    ON COLUMN SHIPPING.SYS_USER_ROLE.ROLE_ID IS '角色ID';

CREATE TABLE "SHIPPING"."VOYAGE_MAIN"
(
    "ID"                 VARCHAR2(32) NOT NULL ENABLE,
    "VOYAGE_NO"          VARCHAR2(50),
    "DISPATCH_SHIP_DATE" DATE,
    "ORDER_STATUS"       VARCHAR2(255),
    "REMARK"             VARCHAR2(255),
    "CREATE_BY"          VARCHAR2(64),
    "CREATE_TIME"        DATE,
    "UPDATE_BY"          VARCHAR2(64),
    "UPDATE_TIME"        DATE,
    "DEL_FLAG"           VARCHAR2(1),
    "VERSION"            NUMBER(10, 0),
    "SHIP_ID"            VARCHAR2(255),
    "VOYAGE_TYPE"        VARCHAR2(255),
    "REAL_START_TIME"    DATE,
    "REAL_END_TIME"      DATE,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010630" ON "SHIPPING"."VOYAGE_MAIN" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.ID IS '主键，雪花ID';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.VOYAGE_NO IS '航次号';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.DISPATCH_SHIP_DATE IS '派船日期';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.ORDER_STATUS IS '指令状态(字典值：NEW新建航次，COMMAND 指令下发，WORKING 作业中，END 作业完成)';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.DEL_FLAG IS '逻辑删除标志';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.SHIP_ID IS '船id';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.VOYAGE_TYPE IS '航次类型(字典值：NORMAL普通运输，EMPTY吉航，REPAIR维修 ，OIL加油，BULK调运散货)';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.REAL_START_TIME IS '实际开始时间';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_MAIN.REAL_END_TIME IS '实际完成时间';

CREATE TABLE "SHIPPING"."VOYAGE_SEQ"
(
    "ID"            VARCHAR2(32) NOT NULL ENABLE,
    "VOYAGE_ID"     VARCHAR2(32),
    "SEQ"           NUMBER(5, 0),
    "TERMINAL_ID"   VARCHAR2(32),
    "AGENT_ID"      VARCHAR2(32),
    "SUB_VOYAGE_ID" VARCHAR2(32),
    "REMARK"        VARCHAR2(255),
    "CREATE_BY"     VARCHAR2(64),
    "CREATE_TIME"   DATE,
    "UPDATE_BY"     VARCHAR2(64),
    "UPDATE_TIME"   DATE,
    "DEL_FLAG"      VARCHAR2(1),
    "VERSION"       NUMBER(10, 0),
    "OPERATION"     VARCHAR2(255),
    "BEGIN_TIME"    DATE,
    "END_TIME"      DATE,
    PRIMARY KEY ("ID")
        USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
            STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
            PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
            BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
            TABLESPACE "SHIPPING" ENABLE
) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
    NOCOMPRESS LOGGING
    STORAGE
(
    INITIAL
    65536
    NEXT
    1048576
    MINEXTENTS
    1
    MAXEXTENTS
    **********
    PCTINCREASE
    0
    FREELISTS
    1
    FREELIST
    GROUPS
    1
    BUFFER_POOL
    DEFAULT
    FLASH_CACHE
    DEFAULT
    CELL_FLASH_CACHE
    DEFAULT
)
    TABLESPACE "SHIPPING";

CREATE UNIQUE INDEX "SHIPPING"."SYS_C0010632" ON "SHIPPING"."VOYAGE_SEQ" ("ID") PCTFREE 10 INITRANS 2 MAXTRANS 255
    STORAGE (INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "SHIPPING";

COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.ID IS 'ID';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.VOYAGE_ID IS '航线id';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.SEQ IS '靠序';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.TERMINAL_ID IS '港口ID';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.AGENT_ID IS '代理ID';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.SUB_VOYAGE_ID IS '子航次ID';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.REMARK IS '备注';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.CREATE_BY IS '创建人';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.CREATE_TIME IS '创建时间';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.UPDATE_BY IS '更新人';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.UPDATE_TIME IS '更新时间';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.DEL_FLAG IS '逻辑删除标志';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.VERSION IS '乐观锁';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.OPERATION IS '作业(装/卸)';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.BEGIN_TIME IS '到达时间';
COMMENT
    ON COLUMN SHIPPING.VOYAGE_SEQ.END_TIME IS '离开时间';

CREATE OR REPLACE FORCE EDITIONABLE VIEW "SHIPPING"."V_BOOKING_ENTRUST"
            ("ORDER_ID", "ORDER_NO", "CUSTOMER_NAME", "ORDER_STATUS", "ORDER_DATE", "TOTAL_CONTAINERS",
             "ORDER_BUSINESS_TYPE", "ORDER_TRADE_TYPE", "CONSORTIUM", "CUSTOMER_AGREEMENT", "ORDER_SETTLEMENT_METHOD",
             "BOOKING_ID", "ENTRUST_NO", "POL", "POD", "LOADING_TERMINAL", "UNLOADING_TERMINAL", "LOADING_AGENT_NAME",
             "UNLOADING_AGENT_NAME", "BOOKING_TRADE_TYPE", "TRANSPORT_MODE", "CUSTOMS_TYPE",
             "BOOKING_SETTLEMENT_METHOD", "CONSIGNMENT_SOURCE", "ENTRUST_STATUS", "BOOKING_DATE", "ETD", "ETA",
             "CONTAINER_COUNT", "CONTAINER_SUMMARY", "TOTAL_WEIGHT", "DANGEROUS_COUNT", "REFRIGERATED_COUNT",
             "OVERSIZE_COUNT", "OVERALL_STATUS", "PRIORITY_LEVEL", "SOURCE_TYPE", "COMBINED_REMARK", "CREATE_TIME",
             "UPDATE_TIME", "CREATE_BY", "UPDATE_BY", "DEL_FLAG", "VERSION")
AS
SELECT
    -- 委托主表字段 (ORDER_MAIN)
    om.id                                            as order_id,                  -- 委托主表ID
    om.order_no,                                                                   -- 委托编号
    om.shipper_name                                  as customer_name,             -- 客户名称（托运单位）
    om.status                                        as order_status,              -- 委托状态
    om.order_date,                                                                 -- 委托日期
    om.total_containers,                                                           -- 总箱量
    om.business_type                                 as order_business_type,       -- 委托业务类型
    om.trade_type                                    as order_trade_type,          -- 委托贸易类型
    om.consortium,                                                                 -- 所属共同体
    om.customer_agreement,                                                         -- 客户协议
    om.settlement_method                             as order_settlement_method,   -- 委托结算方式

    -- 订舱主表字段 (BOOKING_MAIN)
    bm.id                                            as booking_id,                -- 订舱主表ID
    bm.booking_number                                as entrust_no,                -- 订舱号（委托号）
    bm.origin_location_name                          as pol,                       -- 起运地
    bm.destination_location_name                     as pod,                       -- 目的地
    bm.loading_terminal_name                         as loading_terminal,          -- 装货码头
    bm.unloading_terminal_name                       as unloading_terminal,        -- 卸货码头
    bm.loading_agent_name,                                                         -- 装货代理
    bm.unloading_agent_name,                                                       -- 卸货代理
    bm.trade_type                                    as booking_trade_type,        -- 订舱贸易类型
    bm.transport_mode,                                                             -- 运输模式
    bm.customs_type,                                                               -- 报关类型
    bm.settlement_method                             as booking_settlement_method, -- 订舱结算方式
    bm.consignment_source,                                                         -- 委托来源
    bm.status                                        as entrust_status,            -- 订舱状态
    bm.booking_date,                                                               -- 订舱日期
    bm.departure_date                                as etd,                       -- 启运日期（预计开船时间）
    bm.delivery_date                                 as eta,                       -- 交货日期（预计到港时间）

    -- 柜量统计字段（从BOOKING_CNTR_NUM聚合）
    NVL(bcn_summary.container_count, 0)              as container_count,           -- 总柜数
    NVL(bcn_summary.container_summary, '-')          as container_summary,         -- 柜量汇总描述
    NVL(bcn_summary.total_weight, 0)                 as total_weight,              -- 总重量
    NVL(bcn_summary.dangerous_count, 0)              as dangerous_count,           -- 危险品柜数
    NVL(bcn_summary.refrigerated_count, 0)           as refrigerated_count,        -- 冷藏柜数
    NVL(bcn_summary.oversize_count, 0)               as oversize_count,            -- 超限柜数

    -- 业务状态判断字段
    CASE
        WHEN om.status = 'DRAFT' OR bm.status = 'DRAFT' THEN 'DRAFT'
        WHEN om.status = 'SUBMITTED' OR bm.status = 'SUBMITTED' THEN 'SUBMITTED'
        WHEN om.status = 'CONFIRMED' AND bm.status = 'CONFIRMED' THEN 'CONFIRMED'
        WHEN om.status = 'CANCELLED' OR bm.status = 'CANCELLED' THEN 'CANCELLED'
        WHEN bm.status = 'REJECTED' THEN 'REJECTED'
        ELSE 'UNKNOWN'
        END                                          as overall_status,            -- 整体状态

    -- 优先级判断字段
    CASE
        WHEN UPPER(NVL(om.remark, '')) LIKE '%紧急%'
            OR UPPER(NVL(om.remark, '')) LIKE '%优先%'
            OR UPPER(NVL(om.remark, '')) LIKE '%加急%'
            OR UPPER(NVL(bm.remark, '')) LIKE '%紧急%'
            OR UPPER(NVL(bm.remark, '')) LIKE '%优先%'
            OR UPPER(NVL(bm.remark, '')) LIKE '%加急%'
            THEN 'HIGH'
        ELSE 'NORMAL'
        END                                          as priority_level,            -- 优先级

    -- 来源标识字段
    CASE
        WHEN bm.consignment_source = 'ONLINE' THEN 'customer'
        WHEN bm.consignment_source IN ('PHONE', 'ONSITE', 'EMAIL') THEN 'clerk'
        ELSE 'clerk'
        END                                          as source_type,               -- 来源类型

    -- 备注信息
    CASE
        WHEN om.remark IS NOT NULL AND bm.remark IS NOT NULL
            THEN om.remark || ' | ' || bm.remark
        WHEN om.remark IS NOT NULL THEN om.remark
        WHEN bm.remark IS NOT NULL THEN bm.remark
        ELSE NULL
        END                                          as combined_remark,           -- 合并备注

    -- 系统字段
    om.create_time,                                                                -- 创建时间（以委托创建时间为准）
    GREATEST(om.update_time, bm.update_time)         as update_time,               -- 更新时间（取最新）
    om.create_by,                                                                  -- 创建人
    CASE
        WHEN bm.update_time > om.update_time THEN bm.update_by
        ELSE om.update_by
        END                                          as update_by,                 -- 更新人（取最新更新者）

    -- 删除标志（两表都正常才显示）
    CASE
        WHEN om.del_flag = '0' AND bm.del_flag = '0' THEN '0'
        ELSE '2'
        END                                          as del_flag,                  -- 删除标志

    -- 版本字段（用于乐观锁，取较大值）
    GREATEST(NVL(om.version, 1), NVL(bm.version, 1)) as version                    -- 版本号

FROM order_main om
         INNER JOIN booking_main bm ON om.id = bm.order_id

-- 左连接柜量汇总子查询
         LEFT JOIN (SELECT booking_id,
                           COUNT(*)                                                           as container_count,
                           -- 柜量描述：使用友好显示字段，优先显示名称，回退到代码
                           LISTAGG(
                                   NVL(container_size_name, container_size_code) || -- 优先显示尺寸名称（如"20'"），回退到代码（如"20"）
                                   NVL(container_type_code, container_type_name) || -- 优先显示类型代码（如"GP"），回退到名称
                                   CASE WHEN is_empty = '1' THEN '(吉)' ELSE '' END ||
                                   '×' || quantity,
                                   ', '
                           ) WITHIN GROUP (ORDER BY container_size_name, container_type_code) as container_summary,

                           -- 总重量（所有柜子的总重量之和）
                           SUM(NVL(total_weight, 0))                                          as total_weight,

                           -- 危险品柜数
                           SUM(CASE WHEN is_dangerous = '1' THEN quantity ELSE 0 END)         as dangerous_count,

                           -- 冷藏柜数
                           SUM(CASE WHEN is_refrigerated = '1' THEN quantity ELSE 0 END)      as refrigerated_count,

                           -- 超限柜数
                           SUM(CASE WHEN is_oversize = '1' THEN quantity ELSE 0 END)          as oversize_count

                    FROM booking_cntr_num
                    WHERE del_flag = '0'
                    GROUP BY booking_id) bcn_summary ON bm.id = bcn_summary.booking_id

-- 过滤条件：只显示未删除的记录
WHERE om.del_flag = '0'
  AND bm.del_flag = '0';

COMMENT
    ON TABLE SHIPPING.V_BOOKING_ENTRUST IS '订舱委托统一视图 - 整合三层架构数据，为单证员工作台提供高性能列表查询';

CREATE OR REPLACE FORCE EDITIONABLE VIEW "SHIPPING"."V_SHIP_VOYA_INFO"
            ("ID", "船舶ID", "航次ID", "靠港顺序ID", "港口ID", "靠港港口ID", "船名", "当前航次", "靠港顺序", "港口名称",
             "到达时间", "离开时间", "作业类型", "装载20空箱", "装载20重箱", "装载40空箱", "装载40重箱", "卸载20空箱",
             "卸载20重箱", "卸载40空箱", "卸载40重箱")
AS
SELECT
    -- 唯一主键ID (航次ID+靠港顺序)
    main.ID || '-' || seq.SEQ                                                                              AS "ID",

    -- ============= ID字段区 =============
    ship.ID                                                                                                AS "船舶ID",     -- 船舶唯一标识
    main.ID                                                                                                AS "航次ID",     -- 航次唯一标识
    seq.ID                                                                                                 AS "靠港顺序ID", -- 靠港记录唯一标识
    terminal.ID                                                                                            AS "港口ID",     -- 港口唯一标识
    seq.TERMINAL_ID                                                                                        AS "靠港港口ID", -- 靠港记录中的港口ID（与港口ID相同，但保留用于验证）

    -- ============= 业务信息区 =============
    -- 船舶信息
    ship.SHIP_CHINESE_NAME                                                                                 AS "船名",
    main.VOYAGE_NO                                                                                         AS "当前航次",

    -- 港口信息
    seq.SEQ                                                                                                AS "靠港顺序",
    terminal.TERMINAL_NAME                                                                                 AS "港口名称",
    TO_CHAR(seq.BEGIN_TIME, 'YYYY-MM-DD HH24:MI:SS')                                                       AS "到达时间",
    TO_CHAR(seq.END_TIME, 'YYYY-MM-DD HH24:MI:SS')                                                         AS "离开时间",
    seq.OPERATION                                                                                          AS "作业类型",   -- 新增作业类型字段

    -- 装载货物详情
    NVL(SUM(CASE WHEN lw.LOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_20E END),
        0)                                                                                                 AS "装载20空箱",
    NVL(SUM(CASE WHEN lw.LOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_20F END),
        0)                                                                                                 AS "装载20重箱",
    NVL(SUM(CASE WHEN lw.LOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_40E END),
        0)                                                                                                 AS "装载40空箱",
    NVL(SUM(CASE WHEN lw.LOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_40F END),
        0)                                                                                                 AS "装载40重箱",

    -- 卸载货物详情
    NVL(SUM(CASE WHEN lw.UNLOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_20E END),
        0)                                                                                                 AS "卸载20空箱",
    NVL(SUM(CASE WHEN lw.UNLOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_20F END),
        0)                                                                                                 AS "卸载20重箱",
    NVL(SUM(CASE WHEN lw.UNLOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_40E END),
        0)                                                                                                 AS "卸载40空箱",
    NVL(SUM(CASE WHEN lw.UNLOADING_TERMINAL_ID = seq.TERMINAL_ID THEN lw.CONTAINER_ALLOCATION_40F END), 0) AS "卸载40重箱"

FROM VOYAGE_MAIN main
         JOIN BASIC_SHIP_MAIN ship ON main.SHIP_ID = ship.ID
         JOIN VOYAGE_SEQ seq ON main.ID = seq.VOYAGE_ID
         JOIN BASIC_TERMINAL terminal ON seq.TERMINAL_ID = terminal.ID
         LEFT JOIN (SELECT LOADING_TERMINAL_ID,
                           UNLOADING_TERMINAL_ID,
                           VOYAGE_NO,
                           COALESCE(TO_NUMBER(REGEXP_SUBSTR(CONTAINER_ALLOCATION, '"20Empty":(\d+)', 1, 1, NULL, 1)),
                                    0) AS CONTAINER_ALLOCATION_20E,
                           COALESCE(TO_NUMBER(REGEXP_SUBSTR(CONTAINER_ALLOCATION, '"20Full":(\d+)', 1, 1, NULL, 1)),
                                    0) AS CONTAINER_ALLOCATION_20F,
                           COALESCE(TO_NUMBER(REGEXP_SUBSTR(CONTAINER_ALLOCATION, '"40Empty":(\d+)', 1, 1, NULL, 1)),
                                    0) AS CONTAINER_ALLOCATION_40E,
                           COALESCE(TO_NUMBER(REGEXP_SUBSTR(CONTAINER_ALLOCATION, '"40Full":(\d+)', 1, 1, NULL, 1)),
                                    0) AS CONTAINER_ALLOCATION_40F
                    FROM LOGISTIC_WATERWAY
                    WHERE DEL_FLAG = 0) lw ON lw.VOYAGE_NO = main.VOYAGE_NO
    AND (lw.LOADING_TERMINAL_ID = seq.TERMINAL_ID
        OR lw.UNLOADING_TERMINAL_ID = seq.TERMINAL_ID)

-- 更新GROUP BY子句包含所有新增ID字段
GROUP BY ship.ID,
         main.ID,
         seq.ID,
         seq.SUB_VOYAGE_ID,
         terminal.ID,
         seq.TERMINAL_ID,
         ship.SHIP_CHINESE_NAME,
         main.VOYAGE_NO,
         seq.SEQ,
         terminal.TERMINAL_NAME,
         seq.BEGIN_TIME,
         seq.END_TIME,
         seq.OPERATION -- 新增作业类型

ORDER BY main.VOYAGE_NO, seq.SEQ;
;

CREATE OR REPLACE function SHIPPING.find_in_set(arg1 in varchar2, arg2 in varchar)
    return number is
    Result number;
begin
    select instr(',' || arg2 || ',', ',' || arg1 || ',')
    into Result
    from dual;
    return (Result);
end find_in_set;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_CONSIGNMENT INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_CONTAINER INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_NO INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_STATUS_LOG INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_SUPPLEMENT INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_SUPPLEMENT_ATTACHMENT INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_TRANSIT_PORT INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_BOOKING_TRANSPORT_PROCESS INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_FILE_TYPE INCREMENT BY 1 MINVALUE 2000 MAXVALUE 999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_GEN_TABLE INCREMENT BY 1 MINVALUE 2000 MAXVALUE 999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_GEN_TABLE_COLUMN INCREMENT BY 1 MINVALUE 2000 MAXVALUE 9999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_LOGISTICS_NO INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_ORDER_NO INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_CONFIG INCREMENT BY 1 MINVALUE 2000 MAXVALUE 99999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_DEPT INCREMENT BY 1 MINVALUE 2000 MAXVALUE 99999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_DICT_DATA INCREMENT BY 1 MINVALUE 2000 MAXVALUE 9999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_DICT_TYPE INCREMENT BY 1 MINVALUE 2000 MAXVALUE 9999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_JOB INCREMENT BY 1 MINVALUE 100 MAXVALUE 999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_JOB_LOG INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_LOGININFOR INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_MENU INCREMENT BY 1 MINVALUE 2000 MAXVALUE 99999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_NOTICE INCREMENT BY 1 MINVALUE 100 MAXVALUE 99999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_OPER_LOG INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_POST INCREMENT BY 1 MINVALUE 100 MAXVALUE 9999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_ROLE INCREMENT BY 1 MINVALUE 100 MAXVALUE 9999999999999999999 NOCYCLE NOCACHE NOORDER;

CREATE SEQUENCE SHIPPING.SEQ_SYS_USER INCREMENT BY 1 MINVALUE 100 MAXVALUE 999999999999999999 NOCYCLE NOCACHE NOORDER;