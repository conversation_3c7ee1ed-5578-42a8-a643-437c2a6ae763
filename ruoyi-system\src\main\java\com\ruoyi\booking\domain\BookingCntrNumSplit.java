package com.ruoyi.booking.domain;

import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.core.domain.BaseEntity;
import java.math.BigDecimal;
import com.mybatisflex.core.keygen.KeyGenerators;

/**
 * 箱量拆分明细表
 * 记录 BOOKING_CNTR_NUM 中箱量信息的拆分明细，用于策划员进行配船等操作，并与运输环节关联
 */
@Data
@Table("BOOKING_CNTR_NUM_SPLIT")
@EqualsAndHashCode(callSuper = false)
public class BookingCntrNumSplit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键，雪花ID全局唯一标识符 */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /** 关联 BOOKING_CNTR_NUM 表的ID，表示这条拆分明细属于哪条原始箱量数据 */
    @Column("BOOKING_CNTR_NUM_ID")
    private String bookingCntrNumId;

    // 继承自 BOOKING_CNTR_NUM 的箱量属性，保持一致性
    /** 尺寸ID，关联BASIC_CNTR_SIZE.id */
    @Column("CONTAINER_SIZE_ID")
    private String containerSizeId;

    /** 尺寸代码（冗余） */
    @Column("CONTAINER_SIZE_CODE")
    private String containerSizeCode;

    /** 尺寸显示名称（冗余） */
    @Column("CONTAINER_SIZE_NAME")
    private String containerSizeName;

    /** 箱型ID，关联BASIC_CNTR_TYPE.id */
    @Column("CONTAINER_TYPE_ID")
    private String containerTypeId;

    /** 箱型代码（冗余） */
    @Column("CONTAINER_TYPE_CODE")
    private String containerTypeCode;

    /** 箱型显示名称（冗余） */
    @Column("CONTAINER_TYPE_NAME")
    private String containerTypeName;

    /** 重吉：0重箱 1吉箱 */
    @Column("IS_EMPTY")
    private String isEmpty;

    /** 是否危险品：0否 1是 */
    @Column("IS_DANGEROUS")
    private String isDangerous;

    /** 危险品等级ID，关联BASIC_DANGER.id */
    @Column("DANGEROUS_LEVEL_ID")
    private String dangerousLevelId;

    /** 危险品等级代码（冗余） */
    @Column("DANGEROUS_LEVEL_CODE")
    private String dangerousLevelCode;

    /** 危险品等级名称（冗余） */
    @Column("DANGEROUS_LEVEL_NAME")
    private String dangerousLevelName;

    /** 是否冷藏：0否 1是 */
    @Column("IS_REFRIGERATED")
    private String isRefrigerated;

    /** 货类ID，关联basic_cargo_type表主键 */
    @Column("CARGO_TYPE_ID")
    private String cargoTypeId;

    /** 货类代码（冗余） */
    @Column("CARGO_TYPE_CODE")
    private String cargoTypeCode;

    /** 货类名称（冗余） */
    @Column("CARGO_TYPE_NAME")
    private String cargoTypeName;

    /** 是否超限：0否 1是 */
    @Column("IS_OVERSIZE")
    private String isOversize;

    /** 超限尺寸 */
    @Column("OVERSIZE_DIMENSIONS")
    private String oversizeDimensions;

    // 拆分后的具体箱量和重量
    /** 拆分后的箱量 */
    @Column("SPLIT_QUANTITY")
    private Integer splitQuantity;

    /** 拆分后的总重 (KG，可能不准确) */
    @Column("SPLIT_TOTAL_WEIGHT")
    private BigDecimal splitTotalWeight;

    // 拆分相关业务字段
    /** 关联水路运输ID（关联 logistic_waterway.id） */
    @Column("RELATED_WATERWAY_ID")
    private String relatedWaterwayId;

    /** 备注信息 */
    @Column("REMARK")
    private String remark;

    /** 逻辑删除标志：0正常 2删除 */
    @Column(value = "DEL_FLAG", isLogicDelete = true)
    private String delFlag;

    /** 乐观锁版本号，防止并发修改 */
    @Column(value = "VERSION", version = true)
    private Long version;
}