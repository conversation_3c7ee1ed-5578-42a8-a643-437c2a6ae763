package com.ruoyi.customer.service.impl;


import org.springframework.stereotype.Service;
import com.ruoyi.customer.service.CusBusinessTypeService;
import com.ruoyi.customer.domain.CusBusinessType;
import com.ruoyi.customer.mapper.CusBusinessTypeMapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.mybatisflex.core.query.QueryWrapper;

import java.util.List;

import static com.ruoyi.customer.domain.table.CusBusinessTypeTableDef.CUS_BUSINESS_TYPE;

/**
 * 服务层实现。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Service
public class CusBusinessTypeServiceImpl extends ServiceImpl<CusBusinessTypeMapper, CusBusinessType> implements CusBusinessTypeService {

    @Override
    public List<CusBusinessType> selectCusBusinessTypeListByCusId(String cusMainId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(CUS_BUSINESS_TYPE)
                .where(CUS_BUSINESS_TYPE.CUS_MAIN_ID.eq(cusMainId));
        return list(queryWrapper);
    }

}