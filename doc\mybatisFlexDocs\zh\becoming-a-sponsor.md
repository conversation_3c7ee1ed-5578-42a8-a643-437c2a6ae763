# 成为赞助者

MyBatis-Flex 是一个优雅的 MyBatis 增强框架，其开源于 2023 年，由于其友好的 API 设计和出色的性能，在极短的时间内被大众所知。 我们为此投入了大量的时间和无限的热爱。

由于 MyBatis-Flex 基于 Apache 开源协议，任何个人、企业和机构免费商用，但 MyBatis-Flex 的开发、维护和网站服务器的开支，对于我们的热爱造成了许多阻碍， 为此，我们寻求赞助，也为您的产品或品牌提供一个通过
MyBatis-Flex 展示的机会。

## 展示位赞助

在本站对应的位置放置您的 LOGO 及跳转链接。

| 位置              | 素材              | 宽高       | 赞助费       | 名额 |
| ---------------- | ---------------- |----------| ---------- |----|
| 全站右侧边栏 1     | 图片+链接         | 105*50px | ￥200/月     | 8  |
| 全站右侧边栏 2     | 图片+链接         | 222*50px | ￥400/月     | 1  |

您可以联系微信 `fuh99777` 就展示位的详细事宜。

## 无偿捐赠

您也可以通过 Gitee 平台为 MyBatis-Flex 提供无偿捐赠（请量力而行）：

<a href="https://gitee.com/mybatis-flex/mybatis-flex?donate=true" class="VPButton medium brand" target="_blank">
    <img src="https://gitee.com/static/images/logo.svg" style="width: 80px;margin: 10px 0 -5px 6px" />
    通过 Gitee 捐赠
</a>


<style>

.VPButton {
    display: inline-block;
    border: 1px solid transparent;
    text-align: center;
    font-weight: 600;
    white-space: nowrap;
    transition: color 0.25s, border-color 0.25s, background-color 0.25s;
}

.VPButton.medium {
    border-radius: 20px;
    padding: 0 20px;
    line-height: 38px;
    font-size: 14px;
}

.VPButton.brand {
    border-color: var(--vp-button-brand-border);
    color: var(--vp-button-brand-text);
    background-color: var(--vp-button-brand-bg);
}

.VPButton.brand:hover {
    border-color: var(--vp-button-brand-hover-border);
    color: var(--vp-button-brand-hover-text);
    background-color: var(--vp-button-brand-hover-bg);
    text-decoration: inherit;
}

</style>




