-- 先删除表，避免重复建表报错
BEGIN EXECUTE IMMEDIATE 'DROP TABLE BOOKING_CNTR_DETAIL CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE BOOKING_CNTR_COARSE CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE BOOKING_MAIN CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP TABLE ORDER_MAIN CASCADE CONSTRAINTS'; EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- 订单主表
CREATE TABLE ORDER_MAIN (
    id            VARCHAR2(32) PRIMARY KEY, -- 主键，雪花ID
    order_no      VARCHAR2(64),            -- 订单编号
    customer_id   VARCHAR2(64),            -- 客户ID
    order_date    DATE,                    -- 订单日期
    remark        VARCHAR2(255),           -- 备注
    create_by     VARCHAR2(64),            -- 创建人
    create_time   DATE,                    -- 创建时间
    update_by     VARCHAR2(64),            -- 更新人
    update_time   DATE,                    -- 更新时间
    del_flag      VARCHAR2(1),             -- 逻辑删除标志
    version       NUMBER(10)               -- 乐观锁
);

COMMENT ON TABLE ORDER_MAIN IS '订单主表';
COMMENT ON COLUMN ORDER_MAIN.id IS '主键，雪花ID';
COMMENT ON COLUMN ORDER_MAIN.order_no IS '订单编号';
COMMENT ON COLUMN ORDER_MAIN.customer_id IS '客户ID';
COMMENT ON COLUMN ORDER_MAIN.order_date IS '订单日期';
COMMENT ON COLUMN ORDER_MAIN.remark IS '备注';
COMMENT ON COLUMN ORDER_MAIN.create_by IS '创建人';
COMMENT ON COLUMN ORDER_MAIN.create_time IS '创建时间';
COMMENT ON COLUMN ORDER_MAIN.update_by IS '更新人';
COMMENT ON COLUMN ORDER_MAIN.update_time IS '更新时间';
COMMENT ON COLUMN ORDER_MAIN.del_flag IS '逻辑删除标志';
COMMENT ON COLUMN ORDER_MAIN.version IS '乐观锁';

-- 订舱委托主表
CREATE TABLE BOOKING_MAIN (
    id                 VARCHAR2(32) PRIMARY KEY, -- 主键，雪花ID
    order_id           VARCHAR2(32),             -- 关联ORDER_MAIN表ID
    booking_no         VARCHAR2(64),             -- 委托编号
    customer_id        VARCHAR2(32),             -- 订舱客户ID
    customer_code      VARCHAR2(64),             -- 订舱客户代码（冗余）
    customer_name      VARCHAR2(128),            -- 订舱客户名称（冗余）
    customer_abbreviation VARCHAR2(128),        -- 订舱客户简称（冗余）
    booking_date       DATE,                     -- 订舱日期
    contract_id        VARCHAR2(32),             -- 执行合同ID
    trade_type         VARCHAR2(32),             -- 贸易类型
    load_terminal_id   VARCHAR2(32),             -- 装货码头ID
    load_terminal_name VARCHAR2(128),            -- 装货码头名称
    load_terminal_code VARCHAR2(64),             -- 装货码头代码
    unload_terminal_id VARCHAR2(32),             -- 卸货码头ID
    unload_terminal_name VARCHAR2(128),          -- 卸货码头名称
    unload_terminal_code VARCHAR2(64),           -- 卸货码头代码
    load_agent         VARCHAR2(128),            -- 装货代理（字符串，无实体表）
    unload_agent       VARCHAR2(128),            -- 卸货代理（字符串，无实体表）
    vessel_schedule    DATE,                     -- 大船船期
    remark             VARCHAR2(255),            -- 备注
    create_by          VARCHAR2(64),             -- 创建人
    create_time        DATE,                     -- 创建时间
    update_by          VARCHAR2(64),             -- 更新人
    update_time        DATE,                     -- 更新时间
    del_flag           VARCHAR2(1),              -- 逻辑删除标志
    version            NUMBER(10),               -- 乐观锁
    CONSTRAINT fk_booking_order_id FOREIGN KEY (order_id) REFERENCES ORDER_MAIN(id)
);

COMMENT ON TABLE BOOKING_MAIN IS '订舱委托主表';
COMMENT ON COLUMN BOOKING_MAIN.id IS '主键，雪花ID';
COMMENT ON COLUMN BOOKING_MAIN.order_id IS '关联ORDER_MAIN表ID';
COMMENT ON COLUMN BOOKING_MAIN.booking_no IS '委托编号';
COMMENT ON COLUMN BOOKING_MAIN.customer_id IS '订舱客户ID';
COMMENT ON COLUMN BOOKING_MAIN.customer_code IS '订舱客户代码（冗余）';
COMMENT ON COLUMN BOOKING_MAIN.customer_name IS '订舱客户名称（冗余）';
COMMENT ON COLUMN BOOKING_MAIN.customer_abbreviation IS '订舱客户简称（冗余）';
COMMENT ON COLUMN BOOKING_MAIN.booking_date IS '订舱日期';
COMMENT ON COLUMN BOOKING_MAIN.contract_id IS '执行合同ID';
COMMENT ON COLUMN BOOKING_MAIN.trade_type IS '贸易类型';
COMMENT ON COLUMN BOOKING_MAIN.load_terminal_id IS '装货码头ID';
COMMENT ON COLUMN BOOKING_MAIN.load_terminal_name IS '装货码头名称';
COMMENT ON COLUMN BOOKING_MAIN.load_terminal_code IS '装货码头代码';
COMMENT ON COLUMN BOOKING_MAIN.unload_terminal_id IS '卸货码头ID';
COMMENT ON COLUMN BOOKING_MAIN.unload_terminal_name IS '卸货码头名称';
COMMENT ON COLUMN BOOKING_MAIN.unload_terminal_code IS '卸货码头代码';
COMMENT ON COLUMN BOOKING_MAIN.load_agent IS '装货代理（字符串，无实体表）';
COMMENT ON COLUMN BOOKING_MAIN.unload_agent IS '卸货代理（字符串，无实体表）';
COMMENT ON COLUMN BOOKING_MAIN.vessel_schedule IS '大船船期';
COMMENT ON COLUMN BOOKING_MAIN.remark IS '备注';
COMMENT ON COLUMN BOOKING_MAIN.create_by IS '创建人';
COMMENT ON COLUMN BOOKING_MAIN.create_time IS '创建时间';
COMMENT ON COLUMN BOOKING_MAIN.update_by IS '更新人';
COMMENT ON COLUMN BOOKING_MAIN.update_time IS '更新时间';
COMMENT ON COLUMN BOOKING_MAIN.del_flag IS '逻辑删除标志';
COMMENT ON COLUMN BOOKING_MAIN.version IS '乐观锁';

-- 粗略箱信息表
CREATE TABLE BOOKING_CNTR_COARSE (
    id           VARCHAR2(32) PRIMARY KEY, -- 主键，雪花ID
    booking_id   VARCHAR2(32),             -- 关联BOOKING_MAIN表ID
    cntr_size    VARCHAR2(16),             -- 尺寸
    full_empty_flag VARCHAR2(16),          -- 重吉（重箱/空箱/吉箱，吉箱即空箱）
    quantity     NUMBER(10),               -- 箱量
    weight       NUMBER(18,4),             -- 总重
    danger_level VARCHAR2(32),             -- 危险品等级
    remark       VARCHAR2(255),            -- 备注
    create_by    VARCHAR2(64),             -- 创建人
    create_time  DATE,                     -- 创建时间
    update_by    VARCHAR2(64),             -- 更新人
    update_time  DATE,                     -- 更新时间
    del_flag     VARCHAR2(1),              -- 逻辑删除标志
    version      NUMBER(10),               -- 乐观锁
    CONSTRAINT fk_cntr_coarse_booking_id FOREIGN KEY (booking_id) REFERENCES BOOKING_MAIN(id)
);

COMMENT ON TABLE BOOKING_CNTR_COARSE IS '粗略箱信息表';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.id IS '主键，雪花ID';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.booking_id IS '关联BOOKING_MAIN表ID';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.cntr_size IS '尺寸';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.full_empty_flag IS '重吉（重箱/空箱/吉箱，吉箱即空箱）';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.quantity IS '箱量';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.weight IS '总重';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.danger_level IS '危险品等级';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.remark IS '备注';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.create_by IS '创建人';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.create_time IS '创建时间';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.update_by IS '更新人';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.update_time IS '更新时间';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.del_flag IS '逻辑删除标志';
COMMENT ON COLUMN BOOKING_CNTR_COARSE.version IS '乐观锁';

-- 明细箱信息表
CREATE TABLE BOOKING_CNTR_DETAIL (
    id           VARCHAR2(32) PRIMARY KEY, -- 主键，雪花ID
    booking_id   VARCHAR2(32),             -- 关联BOOKING_MAIN表ID
    box_no       VARCHAR2(32),             -- 箱号
    cntr_size    VARCHAR2(16),             -- 尺寸
    full_empty_flag VARCHAR2(16),          -- 重吉（重箱/空箱/吉箱，吉箱即空箱）
    box_model    VARCHAR2(32),             -- 箱型
    weight       NUMBER(18,4),             -- 重量
    danger_level VARCHAR2(32),             -- 危险品等级
    remark       VARCHAR2(255),            -- 备注
    create_by    VARCHAR2(64),             -- 创建人
    create_time  DATE,                     -- 创建时间
    update_by    VARCHAR2(64),             -- 更新人
    update_time  DATE,                     -- 更新时间
    del_flag     VARCHAR2(1),              -- 逻辑删除标志
    version      NUMBER(10),               -- 乐观锁
    seal_no      VARCHAR2(32),             -- 封条号
    CONSTRAINT fk_cntr_detail_booking_id FOREIGN KEY (booking_id) REFERENCES BOOKING_MAIN(id)
);

COMMENT ON TABLE BOOKING_CNTR_DETAIL IS '明细箱信息表';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.id IS '主键，雪花ID';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.booking_id IS '关联BOOKING_MAIN表ID';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.box_no IS '箱号';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.cntr_size IS '尺寸';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.full_empty_flag IS '重吉（重箱/空箱/吉箱，吉箱即空箱）';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.box_model IS '箱型';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.weight IS '重量';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.danger_level IS '危险品等级';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.remark IS '备注';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.create_by IS '创建人';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.create_time IS '创建时间';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.update_by IS '更新人';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.update_time IS '更新时间';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.del_flag IS '逻辑删除标志';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.version IS '乐观锁';
COMMENT ON COLUMN BOOKING_CNTR_DETAIL.seal_no IS '封条号'; 