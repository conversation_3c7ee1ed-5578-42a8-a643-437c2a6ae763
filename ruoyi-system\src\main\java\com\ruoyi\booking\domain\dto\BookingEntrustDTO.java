package com.ruoyi.booking.domain.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import com.ruoyi.order.domain.OrderMain;
import com.ruoyi.booking.domain.BookingMain;
import com.ruoyi.booking.domain.BookingCntrNum;
import com.ruoyi.booking.domain.BookingTransitPort;
import com.ruoyi.booking.domain.LogisticsMain;
import java.util.List;

/**
 * 订舱委托数据传输对象
 * 直接使用Domain实体类作为DTO，因为前端字段结构与数据库表结构完全一致
 */
@Data
public class BookingEntrustDTO {
    
    /** 订单主表信息 - 直接使用OrderMain实体类 */
    @NotNull(message = "委托主表信息不能为空")
    private OrderMain orderMain;
    
    /** 订舱主表信息 - 直接使用BookingMain实体类 */
    @NotNull(message = "订舱主表信息不能为空")
    private BookingMain bookingMain;
    
    /** 箱量信息列表 - 直接使用BookingCntrNum实体类 */
    private List<BookingCntrNum> bookingCntrNumList;
    
    /** 中转港信息列表 - 直接使用BookingTransitPort实体类 */
    private List<BookingTransitPort> bookingTransitPortList;
    
    /** 物流明细列表 - 直接使用LogisticsMain实体类 */
    private List<LogisticsMain> logisticsMainList;
}