package com.ruoyi.customer.domain;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 客户变更历史表
 *
 * @TableName CUS_HISTORY
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "CUS_HISTORY")
public class CusHistory extends BaseEntity implements Serializable {
    /**
     * 客户变更历史id
     */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String cusHistoryId;

    /**
     * 客户id
     */
    private String cusMainId;

    /**
     * 客户变更历史内容(JSON)
     */
    private String content;
}