package com.ruoyi.order.service;

import com.mybatisflex.core.service.IService;
import com.mybatisflex.core.paginate.Page;
import com.ruoyi.order.domain.OrderMain;
import com.ruoyi.order.mapper.OrderMainMapper;

/**
 * 委托主表服务接口
 * 继承IService获得基础CRUD方法
 */
public interface IOrderMainService extends IService<OrderMain> {
    
    /**
     * 分页查询订单主表
     * 
     * @param pageNumber 页码
     * @param pageSize   每页大小
     * @param orderMain  查询条件
     * @return 分页结果
     */
    Page<OrderMain> selectOrderMainPage(int pageNumber, int pageSize, OrderMain orderMain);
    
    /**
     * 获取Mapper对象
     * 用于直接执行复杂查询
     */
    OrderMainMapper getMapper();
}