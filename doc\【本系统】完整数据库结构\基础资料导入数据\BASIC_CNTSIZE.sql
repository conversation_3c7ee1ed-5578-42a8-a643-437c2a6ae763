INSERT INTO "BASIC_CNTR_SIZE" ("ID", "SIZ<PERSON>_CODE", "SIZE_NAME", "SIZE_ISO", "SIZE_NET_WEIGHT", "SIZE_OVER_WEIGHT", "SIZ<PERSON>_CATEGORIZED", "CREATE_TIME", "DEL_FLAG") VALUES ('10020', '45', '45', NULL, NULL, NULL, NULL, TO_DATE('2019-12-31 09:31:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_SIZE" ("ID", "SIZE_CODE", "SIZE_NAME", "SIZE_ISO", "SIZE_NET_WEIGHT", "SIZE_OVER_WEIGHT", "SIZE_CATEGORIZED", "CREATE_TIME", "DEL_FLAG") VALUES ('10018', '20', '20', NULL, NULL, NULL, NULL, TO_DATE('2019-12-31 09:31:12', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_SIZE" ("ID", "SIZE_CODE", "SIZE_NAME", "SIZE_ISO", "SIZE_NET_WEIGHT", "SIZE_OVER_WEIGHT", "SIZE_CATEGORIZED", "CREATE_TIME", "DEL_FLAG") VALUES ('10019', '40', '40', NULL, NULL, NULL, NULL, TO_DATE('2019-12-31 09:31:18', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_SIZE" ("ID", "SIZE_CODE", "SIZE_NAME", "SIZE_ISO", "SIZE_NET_WEIGHT", "SIZE_OVER_WEIGHT", "SIZE_CATEGORIZED", "CREATE_TIME", "DEL_FLAG") VALUES ('10021', '11', '11', NULL, NULL, NULL, NULL, TO_DATE('2019-12-31 09:31:29', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_SIZE" ("ID", "SIZE_CODE", "SIZE_NAME", "SIZE_ISO", "SIZE_NET_WEIGHT", "SIZE_OVER_WEIGHT", "SIZE_CATEGORIZED", "CREATE_TIME", "DEL_FLAG") VALUES ('10022', '111', '2', '2', '2', '2', '2', TO_DATE('2020-07-10 10:23:13', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_SIZE" ("ID", "SIZE_CODE", "SIZE_NAME", "SIZE_ISO", "SIZE_NET_WEIGHT", "SIZE_OVER_WEIGHT", "SIZE_CATEGORIZED", "CREATE_TIME", "DEL_FLAG") VALUES ('10023', '48', '48', NULL, NULL, NULL, NULL, TO_DATE('2024-03-14 15:01:56', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_SIZE" ("ID", "SIZE_CODE", "SIZE_NAME", "SIZE_ISO", "SIZE_NET_WEIGHT", "SIZE_OVER_WEIGHT", "SIZE_CATEGORIZED", "CREATE_TIME", "DEL_FLAG") VALUES ('10024', '53', '53', NULL, NULL, NULL, NULL, TO_DATE('2024-03-14 15:02:05', 'SYYYY-MM-DD HH24:MI:SS'), '0');
UPDATE BASIC_CNTR_SIZE SET DEL_FLAG = '2' WHERE  DEL_FLAG != '0'