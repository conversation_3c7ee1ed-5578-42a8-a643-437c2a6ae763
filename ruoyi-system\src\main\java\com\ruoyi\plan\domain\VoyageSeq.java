package com.ruoyi.plan.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("VOYAGE_SEQ")
public class VoyageSeq extends BaseEntity {
    /**
     * 船舶主键ID
     */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;
    @Column("VOYAGE_ID")
    private String voyageId;
    @Column("SEQ")
    private Integer seq;
    @Column("TERMINAL_ID")
    private String terminalId;
    @Column("AGENT_ID")
    private String agentId;
    @Column("SUB_VOYAGE_ID")
    private String subVoyageId;
    @Column("OPERATION")
    private String operation;
}
