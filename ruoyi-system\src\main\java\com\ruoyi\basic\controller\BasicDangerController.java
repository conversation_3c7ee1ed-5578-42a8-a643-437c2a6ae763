package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicDanger;
import com.ruoyi.basic.service.IBasicDangerService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/system/danger")
public class BasicDangerController extends BaseController {
    @Autowired
    private IBasicDangerService basicDangerService;

    /**
     * 查询危险品等级列表
     */
    @PreAuthorize("@ss.hasPermi('system:danger:list')")
    @GetMapping("/list")
    public Page<BasicDanger> list(BasicDanger basicDanger) {
        // startPage();
        // List<BasicDanger> list =
        // basicDangerService.selectBasicDangerList(basicDanger);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.like(BasicDanger::getDangerLevel, basicDanger.getDangerLevel())
                .like(BasicDanger::getDangerName, basicDanger.getDangerName())
                .orderBy(BasicDanger::getCreateTime, true);
        return basicDangerService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出危险品等级列表
     */
    @PreAuthorize("@ss.hasPermi('system:danger:export')")
    @Log(title = "危险品等级", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicDanger basicDanger) {
        List<BasicDanger> list = basicDangerService.selectBasicDangerList(basicDanger);
        ExcelUtil<BasicDanger> util = new ExcelUtil<BasicDanger>(BasicDanger.class);
        util.exportExcel(response, list, "危险品等级数据");
    }

    /**
     * 获取危险品等级详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:danger:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(basicDangerService.selectBasicDangerById(id));
    }

    /**
     * 新增危险品等级
     */
    @PreAuthorize("@ss.hasPermi('system:danger:add')")
    @Log(title = "危险品等级", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicDanger basicDanger) {
        return toAjax(basicDangerService.insertBasicDanger(basicDanger));
    }

    /**
     * 修改危险品等级
     */
    @PreAuthorize("@ss.hasPermi('system:danger:edit')")
    @Log(title = "危险品等级", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicDanger basicDanger) {
        return toAjax(basicDangerService.updateBasicDanger(basicDanger));
    }

    /**
     * 删除危险品等级
     */
    @PreAuthorize("@ss.hasPermi('system:danger:remove')")
    @Log(title = "危险品等级", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids) {
        return toAjax(basicDangerService.deleteBasicDangerByIds(ids));
    }

    /**
     * 危险品等级下拉options接口
     */
    @GetMapping("/label")
    public AjaxResult label() {
        List<BasicDanger> list = basicDangerService.list(QueryWrapper.create()
                .eq(BasicDanger::getDelFlag, "0")
                .orderBy(BasicDanger::getCreateTime, true));
        return AjaxResult.success(list);
    }
}
