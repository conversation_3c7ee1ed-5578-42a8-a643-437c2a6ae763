package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCargoType;
import com.ruoyi.basic.service.IBasicCargoTypeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ruoyi.basic.domain.table.BasicCargoTypeTableDef.BASIC_CARGO_TYPE;

@RestController
@RequestMapping("/system/cargoType")
public class BasicCargoTypeController extends BaseController {
    @Autowired
    private IBasicCargoTypeService basicCargoTypeService;

    /**
     * 查询货物基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:cargoType:list')")
    @GetMapping("/list")
    public Page<BasicCargoType> list(BasicCargoType basicCargoType)
    {
        // startPage();
        // List<BasicCargoType> list = basicCargoTypeService.selectBasicCargoTypeList(basicCargoType);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create()
        .select()
        .from(BASIC_CARGO_TYPE)
        .where(BASIC_CARGO_TYPE.CARGO_CODE.eq(basicCargoType.getCargoCode()))
        .and(BASIC_CARGO_TYPE.CARGO_NAME.like(basicCargoType.getCargoName()))
        .orderBy(BASIC_CARGO_TYPE.CREATE_TIME.asc());
        return basicCargoTypeService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出货物基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:cargoType:export')")
    @Log(title = "货物基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicCargoType basicCargoType)
    {
        List<BasicCargoType> list = basicCargoTypeService.selectBasicCargoTypeList(basicCargoType);
        ExcelUtil<BasicCargoType> util = new ExcelUtil<BasicCargoType>(BasicCargoType.class);
        util.exportExcel(response, list, "货物基础信息数据");
    }

    /**
     * 获取货物基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:cargoType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicCargoTypeService.selectBasicCargoTypeById(id));
    }

    /**
     * 新增货物基础信息
     */
    @PreAuthorize("@ss.hasPermi('system:cargoType:add')")
    @Log(title = "货物基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicCargoType basicCargoType)
    {
        try {
            return toAjax(basicCargoTypeService.insertBasicCargoType(basicCargoType));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改货物基础信息
     */
    @PreAuthorize("@ss.hasPermi('system:cargoType:edit')")
    @Log(title = "货物基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicCargoType basicCargoType)
    {
        try {
            return toAjax(basicCargoTypeService.updateBasicCargoType(basicCargoType));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除货物基础信息
     */
    @PreAuthorize("@ss.hasPermi('system:cargoType:remove')")
    @Log(title = "货物基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicCargoTypeService.deleteBasicCargoTypeByIds(ids));
    }

    /**
     * 获取货物类型选项（专用于下拉选择）
     * 返回统一的label格式：{value: id, label: 货物名称}
     */
    @GetMapping("/label")
    public AjaxResult label() {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(BASIC_CARGO_TYPE)
                .where(BASIC_CARGO_TYPE.DEL_FLAG.eq("0"))
                .orderBy(BASIC_CARGO_TYPE.CREATE_TIME.asc());
        
        List<BasicCargoType> list = basicCargoTypeService.list(queryWrapper);
        
        // 转换为标准的label格式
        List<Map<String, Object>> labelList = list.stream()
                .map(cargo -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", cargo.getId());
                    map.put("label", cargo.getCargoName() != null ? cargo.getCargoName() : "");
                    map.put("cargoCode", cargo.getCargoCode() != null ? cargo.getCargoCode() : "");
                    map.put("cargoKind", cargo.getCargoKind() != null ? cargo.getCargoKind() : "");
                    return map;
                })
                .collect(Collectors.toList());
                
        return AjaxResult.success(labelList);
    }
}
