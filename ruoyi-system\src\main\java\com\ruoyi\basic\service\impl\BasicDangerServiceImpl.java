package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.mybatisflex.core.query.QueryChain;
import com.ruoyi.basic.domain.BasicDanger;
import com.ruoyi.basic.domain.BasicDangerUnNumber;
import com.ruoyi.basic.mapper.BasicDangerMapper;
import com.ruoyi.basic.mapper.BasicDangerUnNumberMapper;
import com.ruoyi.basic.service.IBasicDangerService;
import com.ruoyi.basic.service.IBasicDangerUnNumberService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicDangerServiceImpl extends ServiceImpl<BasicDangerMapper, BasicDanger> implements IBasicDangerService {

    @Autowired
    private BasicDangerUnNumberMapper basicDangerUnNumberMapper;

    @Autowired
    private IBasicDangerUnNumberService basicDangerUnNumberService;

    @Override
    public BasicDanger selectBasicDangerById(String id) {
        BasicDanger basicDanger = super.getById(id);
        List<BasicDangerUnNumber> list = basicDangerUnNumberService.list(QueryWrapper.create().eq(BasicDangerUnNumber::getDangerId,id));

        if(!list.isEmpty()){
            for (BasicDangerUnNumber number:list){
                number.setId("");
            }
        }

        basicDanger.setBasicDangerUnNumberList(list);
        return basicDanger;
    }

    @Override
    public List<BasicDanger> selectBasicDangerList(BasicDanger basicDanger) {
        return QueryChain.of(BasicDanger.class)
                .where("danger_level like ?", StringUtils.isNotEmpty(basicDanger.getDangerLevel()) ? "%" + basicDanger.getDangerLevel() + "%" : null)
                .and("danger_name like ?", StringUtils.isNotEmpty(basicDanger.getDangerName()) ? "%" + basicDanger.getDangerName() + "%" : null)
                .orderBy("create_time asc")
                .list();
    }

    @Override
    public boolean insertBasicDanger(BasicDanger basicDanger) {
        String level = basicDanger.getDangerLevel();
        String dangerClass = "";

        Pattern pattern = Pattern.compile("^([^.]+)");
        Matcher matcher = pattern.matcher(level);
        if (matcher.find()) {
            dangerClass = matcher.group(1);
        }

        basicDanger.setDangerClass(dangerClass);
        boolean rows = super.save(basicDanger);
        insertBasicDangerUnNumber(basicDanger);
        return rows;
    }

    @Override
    public boolean updateBasicDanger(BasicDanger basicDanger) {
        String level = basicDanger.getDangerLevel();
        String dangerClass = "";

        Pattern pattern = Pattern.compile("^([^.]+)");
        Matcher matcher = pattern.matcher(level);
        if (matcher.find()) {
            dangerClass = matcher.group(1);
        }

        basicDanger.setDangerClass(dangerClass);
        boolean rows = super.updateById(basicDanger);
//        basicDangerUnNumberMapper.deleteByDangerId(basicDanger.getId());
        basicDangerUnNumberMapper.deleteByQuery(QueryWrapper.create().eq(BasicDangerUnNumber::getDangerId,basicDanger.getId()));
        insertBasicDangerUnNumber(basicDanger);
        return rows;
    }

    @Override
    public boolean deleteBasicDangerByIds(List<String> ids) {
        int count = 0;
        for (String id : ids) {
            basicDangerUnNumberMapper.deleteByQuery(QueryWrapper.create().eq(BasicDangerUnNumber::getDangerId,id));
//            count += basicDangerMapper.deleteById(id);
        }
        return super.removeByIds(ids);
    }

    @Override
    public boolean deleteBasicDangerById(String id) {
//        basicDangerUnNumberMapper.deleteByDangerId(id);
        basicDangerUnNumberMapper.deleteByQuery(QueryWrapper.create().eq(BasicDangerUnNumber::getDangerId,id));

        return super.removeById(id);
    }

    public void insertBasicDangerUnNumber(BasicDanger basicDanger) {
        List<BasicDangerUnNumber> basicDangerUnNumberList = basicDanger.getBasicDangerUnNumberList();
        String id = basicDanger.getId();
        if (StringUtils.isNotNull(basicDangerUnNumberList)) {
            List<BasicDangerUnNumber> list = new ArrayList<>();
            for (BasicDangerUnNumber unNumber : basicDangerUnNumberList) {
                unNumber.setDangerId(id);
                list.add(unNumber);
            }
            if (list.size() > 0) {
                basicDangerUnNumberService.saveBatch(list);
            }
        }
    }
}
