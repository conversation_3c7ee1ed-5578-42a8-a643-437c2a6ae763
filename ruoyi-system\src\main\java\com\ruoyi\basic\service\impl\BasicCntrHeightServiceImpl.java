package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicCntrHeight;
import com.ruoyi.basic.mapper.BasicCntrHeightMapper;
import com.ruoyi.basic.service.IBasicCntrHeightService;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCntrHeightTableDef.BASIC_CNTR_HEIGHT;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicCntrHeightServiceImpl extends ServiceImpl<BasicCntrHeightMapper, BasicCntrHeight> implements IBasicCntrHeightService {

    @Autowired
    private BasicCntrHeightMapper basicCntrHeightMapper;

    @Override
    public BasicCntrHeightMapper getMapper() {
        return basicCntrHeightMapper;
    }

    /**
     * 查询箱高
     *
     * @param id 箱高主键
     * @return 箱高
     */
    public BasicCntrHeight selectBasicCntrHeightById(String id)
    {
        return basicCntrHeightMapper.selectOneById(id);
    }

    /**
     * 查询箱高列表
     *
     * @param basicCntrHeight 箱高
     * @return 箱高
     */
    public List<BasicCntrHeight> selectBasicCntrHeightList(BasicCntrHeight basicCntrHeight)
    {
        return QueryChain.of(BasicCntrHeight.class)
                .where(BASIC_CNTR_HEIGHT.HEIGHT_CODE.like(basicCntrHeight.getHeightCode(), StringUtils::isNotEmpty))
                .and(BASIC_CNTR_HEIGHT.HEIGHT_NAME.like(basicCntrHeight.getHeightName(), StringUtils::isNotEmpty))
                .list();
    }

    /**
     * 新增箱高
     *
     * @param basicCntrHeight 箱高
     * @return 结果
     */
    public int insertBasicCntrHeight(BasicCntrHeight basicCntrHeight)
    {
//        basicCntrHeight.setCreateTime(DateUtils.getNowDate());
        return basicCntrHeightMapper.insert(basicCntrHeight);
    }

    /**
     * 修改箱高
     *
     * @param basicCntrHeight 箱高
     * @return 结果
     */
    public int updateBasicCntrHeight(BasicCntrHeight basicCntrHeight)
    {
//        basicCntrHeight.setUpdateTime(DateUtils.getNowDate());
        return basicCntrHeightMapper.update(basicCntrHeight);
    }

    /**
     * 批量删除箱高
     *
     * @param ids 需要删除的箱高主键
     * @return 结果
     */
    public int deleteBasicCntrHeightByIds(List<String> ids)
    {
        return basicCntrHeightMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除箱高信息
     *
     * @param id 箱高主键
     * @return 结果
     */
    public int deleteBasicCntrHeightById(String id)
    {
        return basicCntrHeightMapper.deleteById(id);
    }
    
}
