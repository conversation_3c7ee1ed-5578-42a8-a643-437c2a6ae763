package com.ruoyi.plan.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;

import com.ruoyi.plan.domain.VoyageMain;
import com.ruoyi.plan.domain.VoyageSeq;
import java.util.List;

public interface IVoyageSeqService  extends IService<VoyageSeq> {

    VoyageSeq selectVoyageSeqById(String id);
    int insertVoyageSeq(VoyageSeq voyageSeq);
    int updateVoyageSeq(VoyageSeq voyageSeq);
    int deleteVoyageSeqByIds(String[] ids);
    int deleteVoyageSeqById(String id);
} 