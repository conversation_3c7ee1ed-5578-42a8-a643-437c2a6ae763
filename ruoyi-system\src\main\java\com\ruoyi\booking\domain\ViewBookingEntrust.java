package com.ruoyi.booking.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订舱委托统一视图实体类
 * 对应数据库视图: v_booking_entrust
 * 用途: 整合ORDER_MAIN、BOOKING_MAIN、BOOKING_CNTR_NUM三表数据，为单证员工作台提供高性能列表查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("v_booking_entrust")
public class ViewBookingEntrust extends BaseEntity {
    private static final long serialVersionUID = 1L;

    // ====================================================
    // 委托主表字段 (ORDER_MAIN)
    // ====================================================
    
    /** 委托主表ID */
    @Id
    @Column("order_id")
    private String orderId;

    /** 委托编号 */
    @Column("order_no")
    private String orderNo;

    /** 客户名称（托运单位） */
    @Column("customer_name")
    private String customerName;

    /** 委托状态 */
    @Column("order_status")
    private String orderStatus;

    /** 委托日期 */
    @Column("order_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    /** 总箱量 */
    @Column("total_containers")
    private Integer totalContainers;

    /** 委托业务类型 */
    @Column("order_business_type")
    private String orderBusinessType;

    /** 委托贸易类型 */
    @Column("order_trade_type")
    private String orderTradeType;

    /** 所属共同体 */
    @Column("consortium")
    private String consortium;

    /** 客户协议 */
    @Column("customer_agreement")
    private String customerAgreement;

    /** 委托结算方式 */
    @Column("order_settlement_method")
    private String orderSettlementMethod;

    // ====================================================
    // 订舱主表字段 (BOOKING_MAIN)
    // ====================================================

    /** 订舱主表ID */
    @Column("booking_id")
    private String bookingId;

    /** 订舱号（委托号） */
    @Column("entrust_no")
    private String entrustNo;

    /** 起运港 */
    @Column("pol")
    private String pol;

    /** 目的港 */
    @Column("pod")
    private String pod;

    /** 装货码头 */
    @Column("loading_terminal")
    private String loadingTerminal;

    /** 卸货码头 */
    @Column("unloading_terminal")
    private String unloadingTerminal;

    /** 装货代理 */
    @Column("loading_agent_name")
    private String loadingAgentName;

    /** 卸货代理 */
    @Column("unloading_agent_name")
    private String unloadingAgentName;

    /** 订舱贸易类型 */
    @Column("booking_trade_type")
    private String bookingTradeType;

    /** 运输模式 */
    @Column("transport_mode")
    private String transportMode;

    /** 报关类型 */
    @Column("customs_type")
    private String customsType;

    /** 订舱结算方式 */
    @Column("booking_settlement_method")
    private String bookingSettlementMethod;

    /** 委托来源 */
    @Column("consignment_source")
    private String consignmentSource;

    /** 订舱状态 */
    @Column("entrust_status")
    private String entrustStatus;

    /** 订舱日期 */
    @Column("booking_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date bookingDate;

    /** 启运日期（预计开船时间） */
    @Column("etd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date etd;

    /** 交货日期（预计到港时间） */
    @Column("eta")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date eta;

    // ====================================================
    // 柜量统计字段（从BOOKING_CNTR_NUM聚合）
    // ====================================================

    /** 总柜数 */
    @Column("container_count")
    private Integer containerCount;

    /** 柜量汇总描述 */
    @Column("container_summary")
    private String containerSummary;

    /** 总重量 */
    @Column("total_weight")
    private BigDecimal totalWeight;

    /** 危险品柜数 */
    @Column("dangerous_count")
    private Integer dangerousCount;

    /** 冷藏柜数 */
    @Column("refrigerated_count")
    private Integer refrigeratedCount;

    /** 超限柜数 */
    @Column("oversize_count")
    private Integer oversizeCount;

    // ====================================================
    // 业务状态判断字段
    // ====================================================

    /** 整体状态 */
    @Column("overall_status")
    private String overallStatus;

    /** 优先级 */
    @Column("priority_level")
    private String priorityLevel;

    /** 来源类型 */
    @Column("source_type")
    private String sourceType;

    /** 合并备注 */
    @Column("combined_remark")
    private String combinedRemark;

    // ====================================================
    // 系统字段（继承自BaseEntity，这里重新定义对应视图字段）
    // ====================================================

    /** 创建时间 */
    @Column("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新时间 */
    @Column("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /** 创建人 */
    @Column("create_by")
    private String createBy;

    /** 更新人 */
    @Column("update_by")
    private String updateBy;

    /** 删除标志（0代表存在 2代表删除） */
    @Column("del_flag")
    private String delFlag;

    /** 版本号（用于乐观锁） */
    @Column("version")
    private Long version;
}