-- ======================================================================
-- 单证员工作台V2版本 - 若依字典配置脚本
-- 用于配置订舱信息表单中的下拉选项
-- ======================================================================

-- ======================================================================
-- 1. 运输模式字典 (shipping_transport_mode)
-- ======================================================================

-- 插入字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES (100, '运输模式', 'shipping_transport_mode', '0', 'admin', SYSDATE, '', NULL, '集装箱运输模式类型');

-- 插入字典数据 (请手动录入以下数据)
/*
请在若依系统字典管理中手动录入以下数据：
字典类型: shipping_transport_mode

1. 字典标签: 吉出重回    字典键值: EMPTY_OUT_FULL_BACK    排序: 1    样式: primary    备注: 空箱出重箱回
2. 字典标签: 重出吉回    字典键值: FULL_OUT_EMPTY_BACK     排序: 2    样式: success    备注: 重箱出空箱回
3. 字典标签: 重出不回    字典键值: FULL_OUT_NO_BACK        排序: 3    样式: warning    备注: 重箱出不回程
4. 字典标签: WGO         字典键值: WGO                     排序: 4    样式: info       备注: WGO模式
5. 字典标签: 等待分配    字典键值: WAITING_ASSIGN          排序: 5    样式: default    备注: 等待分配模式
*/

-- ======================================================================
-- 2. 报关类型字典 (shipping_customs_type)
-- ======================================================================

-- 插入字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES (101, '报关类型', 'shipping_customs_type', '0', 'admin', SYSDATE, '', NULL, '报关业务类型');

-- 插入字典数据 (请手动录入以下数据)
/*
请在若依系统字典管理中手动录入以下数据：
字典类型: shipping_customs_type

1. 字典标签: 一般贸易    字典键值: GENERAL      排序: 1    样式: primary    默认: 是    备注: 一般贸易报关
2. 字典标签: 加工贸易    字典键值: PROCESSING   排序: 2    样式: success    默认: 否    备注: 加工贸易报关
*/

-- ======================================================================
-- 3. 结算方式字典 (shipping_settlement_method)
-- ======================================================================

-- 插入字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES (102, '结算方式', 'shipping_settlement_method', '0', 'admin', SYSDATE, '', NULL, '费用结算方式');

-- 插入字典数据 (请手动录入以下数据)
/*
请在若依系统字典管理中手动录入以下数据：
字典类型: shipping_settlement_method

1. 字典标签: 月结        字典键值: MONTHLY     排序: 1    样式: primary    默认: 是    备注: 月度结算
2. 字典标签: 预付        字典键值: PREPAID     排序: 2    样式: warning    默认: 否    备注: 预付费用  
3. 字典标签: 货到付款    字典键值: COD         排序: 3    样式: danger     默认: 否    备注: 货到付款
*/

-- ======================================================================
-- 4. 所属共同体字典 (shipping_consortium)
-- ======================================================================

-- 插入字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES (103, '所属共同体', 'shipping_consortium', '0', 'admin', SYSDATE, '', NULL, '船务共同体组织');

-- 插入字典数据 (请手动录入以下数据)
/*
请在若依系统字典管理中手动录入以下数据：
字典类型: shipping_consortium

1. 字典标签: 黄埔共同体    字典键值: HUANGPU_CONSORTIUM     排序: 1    样式: primary    备注: 黄埔共同体
2. 字典标签: 中山共同体    字典键值: ZHONGSHAN_CONSORTIUM   排序: 2    样式: success    备注: 中山共同体
3. 字典标签: 东莞共同体    字典键值: DONGGUAN_CONSORTIUM    排序: 3    样式: info       备注: 东莞共同体
4. 字典标签: 佛山共同体    字典键值: FOSHAN_CONSORTIUM      排序: 4    样式: warning    备注: 佛山共同体
*/

-- ======================================================================
-- 5. 委托来源字典 (shipping_consignment_source)
-- ======================================================================

-- 插入字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES (104, '委托来源', 'shipping_consignment_source', '0', 'admin', SYSDATE, '', NULL, '订舱委托来源渠道');

-- 插入字典数据 (请手动录入以下数据)
/*
请在若依系统字典管理中手动录入以下数据：
字典类型: shipping_consignment_source

1. 字典标签: 网上申请    字典键值: ONLINE     排序: 1    样式: primary    默认: 是    备注: 网上申请
2. 字典标签: 电话委托    字典键值: PHONE      排序: 2    样式: success    默认: 否    备注: 电话委托
3. 字典标签: 现场委托    字典键值: ONSITE     排序: 3    样式: warning    默认: 否    备注: 现场委托
4. 字典标签: 邮件委托    字典键值: EMAIL      排序: 4    样式: info       默认: 否    备注: 邮件委托

注意：单证员代录入功能通过系统其他机制实现，不需要在字典中配置
*/

-- ======================================================================
-- 6. 运输方式字典 (shipping_business_type)
-- 对应数据库字段：logistics_main.business_type
-- ======================================================================

-- 插入字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES (105, '运输方式', 'shipping_business_type', '0', 'admin', SYSDATE, '', NULL, '物流运输方式类型');

-- 插入字典数据 (请手动录入以下数据)
/*
请在若依系统字典管理中手动录入以下数据：
字典类型: shipping_business_type

1. 字典标签: 水路运输    字典键值: WATERWAY    排序: 1    样式: primary    默认: 是    备注: 水路运输
2. 字典标签: 陆路运输    字典键值: ROADWAY     排序: 2    样式: success    默认: 否    备注: 陆路运输
3. 字典标签: 航空运输    字典键值: AIRWAY      排序: 3    样式: info       默认: 否    备注: 航空运输
*/

-- ======================================================================
-- 执行结束提示
-- ======================================================================

-- 提交事务（如果使用事务）
COMMIT;

-- 验证插入结果
SELECT '若依字典配置完成' AS message FROM dual;

-- 查询验证 - 检查字典类型是否创建成功
SELECT dict_name, dict_type, status, remark 
FROM sys_dict_type 
WHERE dict_type LIKE 'shipping_%' 
ORDER BY dict_id;

-- 查询验证 - 检查字典数据是否创建成功
SELECT dt.dict_name, dd.dict_label, dd.dict_value, dd.list_class, dd.status
FROM sys_dict_type dt 
LEFT JOIN sys_dict_data dd ON dt.dict_type = dd.dict_type 
WHERE dt.dict_type LIKE 'shipping_%' 
ORDER BY dt.dict_id, dd.dict_sort;