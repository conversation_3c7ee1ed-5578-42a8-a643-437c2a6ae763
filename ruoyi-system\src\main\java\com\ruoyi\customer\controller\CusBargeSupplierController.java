package com.ruoyi.customer.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.customer.service.CusBargeSupplierService;
import com.ruoyi.customer.domain.CusBargeSupplier;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 控制层。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@RestController
@RequestMapping("/cusBargeSupplier")
public class CusBargeSupplierController {

    @Autowired
    private CusBargeSupplierService cusBargeSupplierService;

    /**
     * 添加
     *
     * @param cusBargeSupplier
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("/save")
    public boolean save(@RequestBody CusBargeSupplier cusBargeSupplier) {
        return cusBargeSupplierService.save(cusBargeSupplier);
    }


    /**
     * 根据主键删除
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("/remove/{id}")
    public boolean remove(@PathVariable Serializable id) {
        return cusBargeSupplierService.removeById(id);
    }


    /**
     * 根据主键更新
     *
     * @param cusBargeSupplier
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("/update")
    public boolean update(@RequestBody CusBargeSupplier cusBargeSupplier) {
        return cusBargeSupplierService.updateById(cusBargeSupplier);
    }


    /**
     * 查询所有
     *
     * @return 所有数据
     */
    @GetMapping("/list")
    public List<CusBargeSupplier> list() {
        return cusBargeSupplierService.list();
    }


    /**
     * 根据主键获取详细信息。
     *
     * @param id cusBargeSupplier主键
     * @return 详情
     */
    @GetMapping("/getInfo/{id}")
    public CusBargeSupplier getInfo(@PathVariable Serializable id) {
        return cusBargeSupplierService.getById(id);
    }


    /**
     * 分页查询
     *
     * @param page 分页对象
     * @return 分页对象
     */
    @GetMapping("/page")
    public Page<CusBargeSupplier> page(Page<CusBargeSupplier> page) {
        return cusBargeSupplierService.page(page);
    }

    /**
     * 获取驳船供应商选项（专用于下拉选择）
     * 返回统一的label格式：{value: id, label: 供应商名称}
     */
    @GetMapping("/label")
    public AjaxResult label(@RequestParam(value = "routeName", required = false) String routeName) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        // 如果指定了航线名称，则按航线筛选
        if (StringUtils.isNotEmpty(routeName)) {
            queryWrapper.eq(CusBargeSupplier::getRouteName, routeName);
        }
        
        // 按创建时间排序
        queryWrapper.orderBy(CusBargeSupplier::getCreateTime, true);
        
        List<CusBargeSupplier> list = cusBargeSupplierService.list(queryWrapper);
        
        // 转换为标准的label格式
        List<Map<String, Object>> labelList = list.stream()
                .map(supplier -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", supplier.getId());
                    map.put("label", supplier.getBargeSupplierName() != null ? supplier.getBargeSupplierName() : "");
                    map.put("routeName", supplier.getRouteName() != null ? supplier.getRouteName() : "");
                    return map;
                })
                .collect(Collectors.toList());
                
        return AjaxResult.success(labelList);
    }
}