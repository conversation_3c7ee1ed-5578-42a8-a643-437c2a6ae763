package com.ruoyi.plan.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.plan.domain.VoyageMain;
import com.ruoyi.plan.domain.VoyageMainVO;
import com.ruoyi.plan.mapper.VoyageMainMapper;
import com.ruoyi.plan.service.IVoyageMainService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

import static com.ruoyi.plan.domain.table.VoyageMainTableDef.VOYAGE_MAIN;
import static com.ruoyi.basic.domain.table.BasicShipMainTableDef.BASIC_SHIP_MAIN;

@Service
public class VoyageMainServiceImpl extends ServiceImpl<VoyageMainMapper, VoyageMain> implements IVoyageMainService {

    @Override
    public Page<VoyageMain> selectVoyageMainPage(int pageNumber, int pageSize, VoyageMain voyageMain) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(VOYAGE_MAIN.VOYAGE_NO.like(voyageMain.getVoyageNo(), StringUtils.isNotEmpty(voyageMain.getVoyageNo())))
                .and(VOYAGE_MAIN.SHIP_ID.eq(voyageMain.getShipId(), StringUtils.isNotEmpty(voyageMain.getShipId())))
                .and(VOYAGE_MAIN.VOYAGE_TYPE.eq(voyageMain.getVoyageType(), StringUtils.isNotEmpty(voyageMain.getVoyageType())))
                .and(VOYAGE_MAIN.ORDER_STATUS.eq(voyageMain.getOrderStatus(), StringUtils.isNotEmpty(voyageMain.getOrderStatus())))
                .orderBy(VOYAGE_MAIN.CREATE_TIME.desc());
        
        return this.page(new Page<>(pageNumber, pageSize), queryWrapper);
    }

    @Override
    public Page<VoyageMainVO> selectVoyageMainVOPage(int pageNumber, int pageSize, VoyageMain voyageMain) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                    VOYAGE_MAIN.ALL_COLUMNS,
                    BASIC_SHIP_MAIN.SHIP_CHINESE_NAME.as("shipChineseName")
                )
                .leftJoin(BASIC_SHIP_MAIN).on(VOYAGE_MAIN.SHIP_ID.eq(BASIC_SHIP_MAIN.ID))
                .where(VOYAGE_MAIN.VOYAGE_NO.like(voyageMain.getVoyageNo(), StringUtils.isNotEmpty(voyageMain.getVoyageNo())))
                .and(VOYAGE_MAIN.SHIP_ID.eq(voyageMain.getShipId(), StringUtils.isNotEmpty(voyageMain.getShipId())))
                .and(VOYAGE_MAIN.VOYAGE_TYPE.eq(voyageMain.getVoyageType(), StringUtils.isNotEmpty(voyageMain.getVoyageType())))
                .and(VOYAGE_MAIN.ORDER_STATUS.eq(voyageMain.getOrderStatus(), StringUtils.isNotEmpty(voyageMain.getOrderStatus())))
                .orderBy(VOYAGE_MAIN.CREATE_TIME.desc());
        
        return this.getMapper().paginateAs(new Page<>(pageNumber, pageSize), queryWrapper, VoyageMainVO.class);
    }

    @Override
    public VoyageMain selectVoyageMainById(String id) {
        return this.getById(id);
    }

    @Override
    public List<VoyageMain> selectVoyageMainList(VoyageMain voyageMain) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(VOYAGE_MAIN.VOYAGE_NO.like(voyageMain.getVoyageNo(), StringUtils.isNotEmpty(voyageMain.getVoyageNo())))
                .and(VOYAGE_MAIN.SHIP_ID.eq(voyageMain.getShipId(), StringUtils.isNotEmpty(voyageMain.getShipId())))
                .and(VOYAGE_MAIN.VOYAGE_TYPE.eq(voyageMain.getVoyageType(), StringUtils.isNotEmpty(voyageMain.getVoyageType())))
                .and(VOYAGE_MAIN.ORDER_STATUS.eq(voyageMain.getOrderStatus(), StringUtils.isNotEmpty(voyageMain.getOrderStatus())))
                .orderBy(VOYAGE_MAIN.CREATE_TIME.desc());
        
        return this.list(queryWrapper);
    }

    @Override
    public List<VoyageMainVO> selectVoyageMainVOList(VoyageMain voyageMain) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                    VOYAGE_MAIN.ALL_COLUMNS,
                    BASIC_SHIP_MAIN.SHIP_CHINESE_NAME.as("shipChineseName")
                )
                .leftJoin(BASIC_SHIP_MAIN).on(VOYAGE_MAIN.SHIP_ID.eq(BASIC_SHIP_MAIN.ID))
                .where(VOYAGE_MAIN.VOYAGE_NO.like(voyageMain.getVoyageNo(), StringUtils.isNotEmpty(voyageMain.getVoyageNo())))
                .and(VOYAGE_MAIN.SHIP_ID.eq(voyageMain.getShipId(), StringUtils.isNotEmpty(voyageMain.getShipId())))
                .and(VOYAGE_MAIN.VOYAGE_TYPE.eq(voyageMain.getVoyageType(), StringUtils.isNotEmpty(voyageMain.getVoyageType())))
                .and(VOYAGE_MAIN.ORDER_STATUS.eq(voyageMain.getOrderStatus(), StringUtils.isNotEmpty(voyageMain.getOrderStatus())))
                .orderBy(VOYAGE_MAIN.CREATE_TIME.desc());
        
        return this.getMapper().selectListByQueryAs(queryWrapper, VoyageMainVO.class);
    }

    @Override
    public int insertVoyageMain(VoyageMain voyageMain) {
        return this.save(voyageMain) ? 1 : 0;
    }

    @Override
    public int updateVoyageMain(VoyageMain voyageMain) {
        return this.updateById(voyageMain) ? 1 : 0;
    }

    @Override
    public int deleteVoyageMainByIds(String[] ids) {
        return this.removeByIds(Arrays.asList(ids)) ? ids.length : 0;
    }

    @Override
    public int deleteVoyageMainById(String id) {
        return this.removeById(id) ? 1 : 0;
    }

    @Override
    public String generateVoyageNo() {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        String dateStr = today.format(DateTimeFormatter.ofPattern("yyMMdd"));
        
        // 查询今天已生成的航次号数量
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(VOYAGE_MAIN.VOYAGE_NO.like(dateStr + "%"))
                .orderBy(VOYAGE_MAIN.VOYAGE_NO.desc());
        
        List<VoyageMain> todayVoyages = this.list(queryWrapper);
        
        // 生成序号
        int sequence = 1;
        if (!todayVoyages.isEmpty()) {
            String lastVoyageNo = todayVoyages.get(0).getVoyageNo();
            if (lastVoyageNo != null && lastVoyageNo.length() >= 9) {
                try {
                    sequence = Integer.parseInt(lastVoyageNo.substring(6)) + 1;
                } catch (NumberFormatException e) {
                    sequence = 1;
                }
            }
        }
        
        // 格式：YYMMDD序号（3位数字）
        return dateStr + String.format("%03d", sequence);
    }
} 