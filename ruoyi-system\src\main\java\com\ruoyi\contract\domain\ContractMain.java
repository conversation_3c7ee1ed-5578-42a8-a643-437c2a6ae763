package com.ruoyi.contract.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.*;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysDictData;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "contract_main")
public class ContractMain extends BaseEntity {
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String contractId;

    //合同号
    @NotEmpty(message = "合同编号不能为空")
    private String contractNo;

    //合同名
    @NotEmpty(message = "合同名称不能为空")
    private String contractName;

    //有效期开始日
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "合同有效期开始日不能为空")
    private Date validityStartDate;

    //有效期结束日
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "合同有效期结束日不能为空")
    private Date validityEndDate;

    //合同类型
    @NotEmpty(message = "合同类型不能为空")
    private String contractType;

    //部门
    @NotNull(message = "经办部门不能为空")
    private Long deptId;

    @Column(ignore = true)
    private String deptName;

    private String delFlag;

    //租户
//    private Long tenantId;

    @RelationManyToMany(
            joinTable = "contract_payment_relation",
            selfField = "contract_id",
            joinSelfColumn = "contract_id",
            joinTargetColumn = "relation_code",
            targetField = "dict_value"
    )
    private List<SysDictData> paymentRelation;

    @Column(ignore = true)
    @NotNull(message = "合同收付类型不能为空")
    private List<String> payRelation;

    //甲方查询
    @Column(ignore = true)
    private String firstCustom;

    @Column(ignore = true)
    @NotNull(message = "合同甲方不能为空")
    private List<String> firstCustoms;

    //乙方
    @Column(ignore = true)
    private String secondCustom;

    @Column(ignore = true)
    @NotNull(message = "合同乙方不能为空")
    private List<String> secondCustoms;

    //丙方
    @Column(ignore = true)
    private String thirdCustom;

    @Column(ignore = true)
    private List<String> thirdCustoms;

    //合同使用次数
    private BigDecimal usedTimes;

    //锁
    private Integer version;

    //合同是否生效 Y 是 N 否
    private String isValidity;
    
    // 当前客户在合同中的角色
    @Column(ignore = true)
    private String customerRole;
    
    /**
     * 合同流向列表
     */
    @Column(ignore = true)
    private List<ContractFlow> flows;
    
    /**
     * 流向ID列表（用于前端传参）
     */
    @Column(ignore = true)
    private List<String> flowIds;
}
