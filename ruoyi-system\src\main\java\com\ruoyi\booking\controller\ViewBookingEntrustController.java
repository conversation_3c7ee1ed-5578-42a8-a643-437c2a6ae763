package com.ruoyi.booking.controller;

import com.mybatisflex.core.paginate.Page;
import com.ruoyi.booking.domain.ViewBookingEntrust;
import com.ruoyi.booking.domain.vo.BookingEntrustQueryVO;
import com.ruoyi.booking.service.IViewBookingEntrustService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 订舱委托统一视图控制器
 * 对应数据库视图: v_booking_entrust
 * 用途: 为单证员工作台提供高性能的列表查询接口
 *
 * 注意: 视图只支持查询操作，不支持增删改操作
 */
@Slf4j
@RestController
@RequestMapping("/booking/entrust/view")
public class ViewBookingEntrustController extends BaseController {

    @Autowired
    private IViewBookingEntrustService viewBookingEntrustService;

    /**
     * 分页查询订舱委托视图列表
     * 新的列表查询接口，替代原有的复杂三表联查
     *
     * @param queryVO 查询条件
     * @return 分页数据
     */
    @PreAuthorize("@ss.hasPermi('booking:entrust:list')")
    @GetMapping("/list")
    public TableDataInfo list(BookingEntrustQueryVO queryVO) {
        log.debug("视图分页查询订舱委托列表 - 查询条件: {}", queryVO);

        // 获取分页参数
        int pageNumber = queryVO.getPageNum() != null ? queryVO.getPageNum() : 1;
        int pageSize = queryVO.getPageSize() != null ? queryVO.getPageSize() : 10;

        // 执行分页查询
        Page<ViewBookingEntrust> page = viewBookingEntrustService.selectViewEntrustPage(pageNumber, pageSize, queryVO);

        log.debug("视图分页查询完成 - 总记录数: {}, 当前页记录数: {}", page.getTotalRow(), page.getRecords().size());

        // 转换为若依框架的分页数据格式
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setRows(page.getRecords());
        dataTable.setTotal(page.getTotalRow());
        dataTable.setCode(200);
        dataTable.setMsg("查询成功");

        return dataTable;
    }
}