package com.ruoyi.booking.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.booking.domain.BookingCntrNumSplit;

/**
 * 箱量拆分明细表 Service 接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IBookingCntrNumSplitService extends IService<BookingCntrNumSplit> {
    /**
     * 分页查询箱量拆分明细
     * 
     * @param pageNumber 页码
     * @param pageSize   每页大小
     * @param bookingCntrNumSplit 查询条件
     * @return 分页结果
     */
    public Page<BookingCntrNumSplit> selectBookingCntrNumSplitPage(int pageNumber, int pageSize, BookingCntrNumSplit bookingCntrNumSplit);
} 