package com.ruoyi.system.mapper;

import com.mybatisflex.core.BaseMapper;
import com.ruoyi.system.domain.SysFileManage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SysFileManageMapper extends BaseMapper<SysFileManage> {
    @Select("SELECT\n" +
            "sfm.FILE_ID,\n" +
            "sfm.FILE_NAME,\n" +
            "sfm.FILE_PATH,\n" +
            "sfm.FILE_BUSINESS_ID,\n" +
            "sft.FILE_TYPE_ID,\n" +
            "sft.FILE_TYPE,\n" +
            "sft.FILE_BUSINESS_TYPE\n" +
            "FROM\n" +
            "SYS_FILE_TYPE sft\n" +
            "LEFT JOIN SYS_FILE_MANAGE sfm ON sft.FILE_TYPE_ID = sfm.FILE_TYPE_ID and sfm.DEL_FLAG = '0'\n" +
            "where \n" +
            "sft.FILE_TYPE_ID = #{fileTypeId}\n" +
            "and \n" +
            "sfm.FILE_BUSINESS_ID = #{fileBusinessId}\n" +
            "and sft.DEL_FLAG = '0'\n" +
            "ORDER BY sfm.FILE_ID DESC\n")
    List<SysFileManage> selectMyList(@Param("fileTypeId") Long fileTypeId, @Param("fileBusinessId") String fileBusinessId);
}
