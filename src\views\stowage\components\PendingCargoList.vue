<template>
  <div class="pending-cargo-list">
    <!-- 搜索过滤区域 -->
    <div class="search-filters">
      <el-input
        v-model="queryParams.searchValue"
        placeholder="搜索订舱号、客户、港口..."
        clearable
        prefix-icon="Search"
        style="width: 250px"
      />
      <el-select
        v-model="queryParams.status"
        placeholder="状态筛选"
        clearable
        style="width: 150px; margin-left: 10px"
      >
        <el-option label="全部" value="" />
        <el-option label="待处理" value="pending" />
        <el-option label="已拆解" value="split" />
        <el-option label="已分配" value="allocated" />
      </el-select>
      <div class="filter-actions">
        <el-button size="small" @click="clearFilters">清空筛选</el-button>
        <el-button size="small" type="primary" @click="selectAll"
          >全选当前页</el-button
        >
      </div>
    </div>

    <!-- 快速操作栏 -->
    <div class="quick-actions" v-if="stowageStore.selectedCargo.length > 0">
      <div class="selection-info">
        <el-tag type="primary"
          >已选择 {{ stowageStore.selectedCargo.length }} 项货物</el-tag
        >
        <el-tag type="info">总TEU: {{ selectedTotalTEU }}</el-tag>
        <el-tag type="warning" v-if="getSelectedRoutes().length > 1">
          涉及 {{ getSelectedRoutes().length }} 条航线
        </el-tag>
        <el-tag type="success" v-if="canMergeSelected"> 可合并 </el-tag>
        <el-tag
          type="danger"
          v-else-if="stowageStore.selectedCargo.length >= 2"
        >
          不可合并
        </el-tag>
      </div>
      <div class="action-buttons">
        <el-dropdown @command="handleBatchAction">
          <el-button size="small">
            批量操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="selectRoute"
                >选择相同航线</el-dropdown-item
              >
              <el-dropdown-item command="selectshipperName"
                >选择相同客户</el-dropdown-item
              >
              <el-dropdown-item command="selectProject"
                >选择相同项目</el-dropdown-item
              >
              <el-dropdown-item command="clear" divided
                >清空选择</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          size="small"
          type="primary"
          @click="batchSplit"
          :disabled="stowageStore.selectedCargo.length !== 1"
        >
          拆解选中
        </el-button>
        <el-button
          size="small"
          type="success"
          @click="batchMerge"
          :disabled="!canMergeSelected"
        >
          合并选中
        </el-button>
        <el-button-group>
          <el-button
            size="small"
            type="primary"
            @click="addToBuffer(1)"
            :style="{ backgroundColor: '#f44336', borderColor: '#f44336' }"
          >
            缓冲区1
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="addToBuffer(2)"
            :style="{ backgroundColor: '#4caf50', borderColor: '#4caf50' }"
          >
            缓冲区2
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="addToBuffer(3)"
            :style="{ backgroundColor: '#2196f3', borderColor: '#2196f3' }"
          >
            缓冲区3
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 数据表格（待运输货物） -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="filteredCargoList"
        :default-expand-all="false"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @selection-change="handleSelectionChange"
        height="100%"
        :row-style="getRowStyle"
        v-loading="stowageStore.ui.loading"
      >
        <el-table-column
          type="selection"
          width="30"
          :selectable="isRowSelectable"
        />
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;运输环节"
          width="180"
          fixed="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span
              class="draggable-booking-no"
              :draggable="row.status === 'pending'"
              @dragstart="handleRowDragStart(row, $event)"
              @dragend="handleRowDragEnd"
            >
              <el-icon v-if="row.status === 'pending'" class="drag-handle">
                <Rank />
              </el-icon>
              {{ row.logisticsMainId }}-{{ row.sequenceNo }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="loadingTerminalName" label="启运港" width="100" />
        <el-table-column prop="unloadingTerminalName" label="目的港" width="100" />
        <!-- TEU合计 -->
        <el-table-column label="TEU合计" width="80" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{
              stowageStore.calculateTEU(row.containers ?? {})
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="shipperName"
          label="客户"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="remark"
          label="备注"
          min-width="180"
          show-overflow-tooltip
        />
        <!-- 集装箱数量列 -->
        <el-table-column label="20E" width="60" align="center">
          <template #default="{ row }">
            <span
              :class="{
                'zero-quantity': row.containers?.['20Empty'] === 0,
              }"
            >
              {{ row.containers?.["20Empty"] ?? 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="20F" width="60" align="center">
          <template #default="{ row }">
            <span
              :class="{
                'zero-quantity': row.containers?.['20Full'] === 0,
              }"
            >
              {{ row.containers?.["20Full"] ?? 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="40E" width="60" align="center">
          <template #default="{ row }">
            <span
              :class="{
                'zero-quantity': row.containers?.['40Empty'] === 0,
              }"
            >
              {{ row.containers?.["40Empty"] ?? 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="40F" width="60" align="center">
          <template #default="{ row }">
            <span
              :class="{
                'zero-quantity': row.containers?.['40Full'] === 0,
              }"
            >
              {{ row.containers?.["40Full"] ?? 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="其它" width="60" align="center">
          <template #default="{ row }">
            <el-tooltip
              :content="row.containers?.['OtherDetail'] || '无详细信息'"
              placement="top"
              :disabled="!row.containers?.['OtherDetail']"
            >
              <span
                :class="{
                  'zero-quantity': row.containers?.['Other'] === 0,
                }"
              >
                {{ row.containers?.["Other"] ?? 0 }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="etd"
          label="要求启运日期"
          width="120"
          align="center"
        />
        <el-table-column
          prop="eta"
          label="要求到达日期"
          width="120"
          align="center"
        />
        <!-- 状态列 -->
        <el-table-column label="状态" width="90" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
              :color="row.bufferZone ? getBufferColor(row.bufferZone) : ''"
              :style="{ color: row.bufferZone ? 'white' : '' }"
            >
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 拆解对话框 -->
    <el-dialog
      v-model="splitDialogVisible"
      :title="`拆解货物: ${splitItem?.sequenceNo || ''}`"
      width="500px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="false"
    >
      <div v-if="splitItem" class="split-container">
        <!-- 原始信息 -->
        <div class="original-info">
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="环节序号">
              {{ splitItem.logisticsMainId }}-{{ splitItem.sequenceNo }}
            </el-descriptions-item>
            <el-descriptions-item label="要求起运日期">{{
              splitItem.etd
            }}</el-descriptions-item>
            <el-descriptions-item label="起运港">{{
              splitItem.loadingTerminalName
            }}</el-descriptions-item>
            <el-descriptions-item label="目的港">{{
              splitItem.unloadingTerminalName
            }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 拆解设置 -->
        <div class="split-settings">
          <div class="split-header">
            <h4>拆解设置</h4>
            <el-button size="small" type="primary" @click="addSplitRecord">
              添加拆解记录
            </el-button>
          </div>

          <!-- 拆解记录列表 -->
          <div class="split-records">
            <div
              v-for="(record, index) in splitRecords"
              :key="index"
              class="split-record"
              :class="{ 'is-invalid': !isRecordValid(record) }"
            >
              <div class="record-header">
                <span class="record-title">拆解记录 {{ index + 1 }}</span>
                <el-button
                  size="small"
                  type="danger"
                  @click="removeSplitRecord(index)"
                  :disabled="splitRecords.length <= 1"
                >
                  删除
                </el-button>
              </div>

              <div class="record-content">
                <div class="container-inputs">
                  <div class="input-group">
                    <label>20E:</label>
                    <el-input-number
                      :model-value="record.containers['20Empty']"
                      @update:model-value="
                        (val) => handleContainerChange(index, '20Empty', val)
                      "
                      :min="0"
                      :max="splitItem.containers['20Empty']"
                      size="small"
                      style="width: 80px"
                    />
                  </div>

                  <div class="input-group">
                    <label>20F:</label>
                    <el-input-number
                      :model-value="record.containers['20Full']"
                      @update:model-value="
                        (val) => handleContainerChange(index, '20Full', val)
                      "
                      :min="0"
                      :max="splitItem.containers['20Full']"
                      size="small"
                      style="width: 80px"
                    />
                  </div>

                  <div class="input-group">
                    <label>40E:</label>
                    <el-input-number
                      :model-value="record.containers['40Empty']"
                      @update:model-value="
                        (val) => handleContainerChange(index, '40Empty', val)
                      "
                      :min="0"
                      :max="splitItem.containers['40Empty']"
                      size="small"
                      style="width: 80px"
                    />
                  </div>

                  <div class="input-group">
                    <label>40F:</label>
                    <el-input-number
                      :model-value="record.containers['40Full']"
                      @update:model-value="
                        (val) => handleContainerChange(index, '40Full', val)
                      "
                      :min="0"
                      :max="splitItem.containers['40Full']"
                      size="small"
                      style="width: 80px"
                    />
                  </div>

                  <div class="input-group">
                    <label>其他:</label>
                    <el-input-number
                      :model-value="record.containers['Other']"
                      @update:model-value="
                        (val) => handleContainerChange(index, 'Other', val)
                      "
                      :min="0"
                      :max="splitItem.containers['Other']"
                      size="small"
                      style="width: 80px"
                    />
                  </div>
                </div>

                <div class="record-summary">
                  <span
                    >TEU:
                    {{ stowageStore.calculateTEU(record.containers) }}</span
                  >
                  <span v-if="!isRecordValid(record)" class="error-text"
                    >数量超出限制</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 验证信息 -->
        <div class="validation-info">
          <el-alert
            v-if="!isValidSplit"
            title="拆解数量不匹配"
            type="error"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="validation-details">
                <div
                  v-for="containerType in getContainerTypes()"
                  :key="containerType"
                  class="validation-item"
                >
                  <span class="container-label"
                    >{{ getContainerLabel(containerType) }}:</span
                  >
                  <span class="quantity-info">
                    <span class="current">{{
                      getSplitSum(containerType)
                    }}</span>
                    <span class="separator">/</span>
                    <span class="total">{{
                      splitItem.containers[containerType]
                    }}</span>
                  </span>
                  <span
                    v-if="
                      getSplitSum(containerType) <
                      splitItem.containers[containerType]
                    "
                    class="status-missing"
                  >
                    缺少
                    {{
                      splitItem.containers[containerType] -
                      getSplitSum(containerType)
                    }}
                  </span>
                  <span
                    v-else-if="
                      getSplitSum(containerType) >
                      splitItem.containers[containerType]
                    "
                    class="status-excess"
                  >
                    超出
                    {{
                      getSplitSum(containerType) -
                      splitItem.containers[containerType]
                    }}
                  </span>
                  <span v-else class="status-match">✓ 匹配</span>
                </div>
              </div>
            </template>
          </el-alert>

          <el-alert
            v-else
            title="拆解数量匹配"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="validation-details">
                <div
                  v-for="containerType in getContainerTypes()"
                  :key="containerType"
                  class="validation-item"
                >
                  <span class="container-label"
                    >{{ getContainerLabel(containerType) }}:</span
                  >
                  <span class="quantity-info">
                    <span class="current">{{
                      getSplitSum(containerType)
                    }}</span>
                    <span class="separator">/</span>
                    <span class="total">{{
                      splitItem.containers[containerType]
                    }}</span>
                  </span>
                  <span class="status-match">✓ 匹配</span>
                </div>
              </div>
            </template>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="splitDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmSplit"
            :disabled="!isValidSplit"
            >确认拆解</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { useStowageStore, containerTypes } from "@/stores/stowage";
// import { containerTypes, calculateTEU } from "../mockData.js";
import {
  getPendingCargoList,
  splitCargo,
  mergeCargo
} from "@/api/booking/stowage"; // 导入API函数
const stowageStore = useStowageStore();

// 响应式数据
const tableRef = ref(null);
const searchTimeout = ref(null);
// 分页相关数据
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  searchValue: "",
  status: ""
});

// 分页相关方法
// 分页相关方法
const handleSizeChange = (val) => {
  queryParams.pageSize = val;
  queryParams.pageNum = 1;
};

const handleCurrentChange = (val) => {
  queryParams.pageNum = val;
};
const fetchData = async (newPage, newSize, newSearch, newStatus) => {
  try {
    const params = {
      pageNum: newPage,
      pageSize: newSize,
    };

    if (newSearch) {
      params.searchValue = newSearch;
    }
    if (newStatus) {
      params.status = newStatus;
    }

    const response = await getPendingCargoList(params);
    total.value = response.total;
    // 解析 containerAllocation 字段
    stowageStore.pendingCargoList = response.rows.map((item) => {
      try {
        return {
          ...item,
          containers: item?.containerAllocation
            ? JSON.parse(item.containerAllocation)
            : {},
        };
      } catch (e) {
        console.error("解析containerAllocation失败:", e);
        return {
          ...item,
          containers: {},
        };
      }
    });
  } catch (error) {
    console.error("请求失败:", error);
  }
};
//仅处理搜索框值改变时进行防抖搜索
watch(
  () => queryParams.searchValue,
  (newSearch) => {
    // 清除之前的定时器
    if (searchTimeout.value) {
      clearTimeout(searchTimeout.value);
      searchTimeout.value = null;
    }
    searchTimeout.value = setTimeout(() => {
      fetchData(
        queryParams.pageNum,
        queryParams.pageSize,
        newSearch,
        queryParams.status
      );
      searchTimeout.value = null;
    }, 1000); // 延迟1秒
  },
  { immediate: true }
);
// 监听 statusFilter、pageNum、pageSize 变化
watch(
  () => [queryParams.status, queryParams.pageNum, queryParams.pageSize],
  ([newStatus, newPage, newSize]) => {
    fetchData(newPage, newSize, queryParams.searchValue, newStatus);
  }
);
// 拖拽状态
const draggingRow = ref(null);

// 拆解相关数据
const splitDialogVisible = ref(false);
const splitItem = ref(null);
const splitRecords = ref([]);

// 搜索和过滤
const filteredCargoList = computed(() => {
  // 前端不再做任何过滤，直接返回原始数据
  return stowageStore.pendingCargoList;
});

// 计算选中货物的总TEU
const selectedTotalTEU = computed(() => {
  if (!stowageStore.selectedCargo || stowageStore.selectedCargo.length === 0)
    return 0;

  return stowageStore.selectedCargo.reduce((total, item) => {
    return total + stowageStore.calculateTEU(item.containers ?? {});
  }, 0);
});

// 计算拆解数量合计
const getSplitSum = (containerType) => {
  return splitRecords.value.reduce(
    (sum, record) => sum + (record.containers[containerType] || 0),
    0
  );
};

// 验证拆解是否有效
const isValidSplit = computed(() => {
  if (!splitItem.value || splitRecords.value.length === 0) return false;

  // 只验证实际的集装箱类型，排除 OtherDetail 等非数值字段
  return getContainerTypes().every((containerType) => {
    const originalQuantity = splitItem.value.containers[containerType];
    const splitSum = getSplitSum(containerType);
    return splitSum === originalQuantity;
  });
});

// 验证单个记录是否有效
const isRecordValid = (record) => {
  if (!splitItem.value) return false;

  // 只验证实际的集装箱类型，排除 OtherDetail 等非数值字段
  return getContainerTypes().every((containerType) => {
    const originalQuantity = splitItem.value.containers[containerType];
    const recordQuantity = record.containers[containerType] || 0;
    return recordQuantity <= originalQuantity;
  });
};

// 验证记录
const validateRecord = (record) => {
  // 这里可以添加额外的验证逻辑
  // 验证每个集装箱类型的总和不超过原始值
  Object.keys(splitItem.value.containers).forEach((containerType) => {
    const total = getSplitSum(containerType);
    const original = splitItem.value.containers[containerType];

    if (total > original) {
      // 如果总和超过原始值，需要调整
      const diff = total - original;
      // 从当前记录中减去超出的部分
      record.containers[containerType] = Math.max(
        0,
        (record.containers[containerType] || 0) - diff
      );
    }
  });
};

// 检查选中的货物是否可以合并
const canMergeSelected = computed(() => {
  if (stowageStore.selectedCargo.length < 2) return false;

  // 检查是否都是待处理或取消状态
  const allPending = stowageStore.selectedCargo.every(
    (item) => item.status === "pending" || item.status === "cancel"
  );
  if (!allPending) return false;

  // 检查是否可以合并（使用store中的方法）
  return stowageStore.canMergeCargo(stowageStore.selectedCargo);
});

// 处理选择变化
const handleSelectionChange = (selection) => {
  // 更新store中的选择
  stowageStore.selectedCargo = selection;
};

// 清空筛选
const clearFilters = () => {
  queryParams.searchValue = "";
  queryParams.status = "";
};

// 全选当前页
const selectAll = () => {
  tableRef.value.toggleAllSelection();
};

// 添加到缓冲区
const addToBuffer = (bufferZoneId) => {
  if (stowageStore.selectedCargo.length === 0) {
    ElMessage.warning("请先选择货物");
    return;
  }

  if (stowageStore.addToBuffer(stowageStore.selectedCargo, bufferZoneId)) {
    ElMessage.success(
      `已添加 ${stowageStore.selectedCargo.length} 项到缓冲区${bufferZoneId}`
    );
  } else {
    ElMessage.error("添加失败");
  }
};

// 批量拆解
const batchSplit = () => {
  if (stowageStore.selectedCargo.length !== 1) {
    ElMessage.warning("请选择一项进行拆解");
    return;
  }
  handleSplitItem(stowageStore.selectedCargo[0]);
};

// 批量合并
const batchMerge = () => {
  if (stowageStore.selectedCargo.length < 2) {
    ElMessage.warning("请至少选择两项进行合并");
    return;
  }

  if (!canMergeSelected.value) {
    ElMessage.warning(
      "选中的货物无法合并，请检查环节序号是否相同或来自同一拆解"
    );
    return;
  }
  const ids = stowageStore.selectedCargo.map((item) => item.id);
  // 调用后端接口
  mergeCargo(ids)
    .then((response) => {
      if (response.success) {
        // 假设接口返回字段 `success` 表示操作成功
        ElMessage.success(`成功合并 ${ids.length} 项货物`);
        stowageStore.clearSelection();
        fetchData(
          queryParams.pageNum,
          queryParams.pageSize,
          queryParams.searchValue,
          queryParams.status
        );
      } else {
        ElMessage.error("合并失败");
      }
    })
    .catch((error) => {
      console.error("合并请求失败:", error);
      ElMessage.error("合并请求失败，请重试");
    });
};

// 处理拆解单项
const handleSplitItem = (item) => {
  if (item.status === "allocated") {
    ElMessage.warning("已分配的货物不能拆解");
    return;
  }

  splitItem.value = item;

  // 初始化拆解记录
  splitRecords.value = [
    {
      containers: { ...item.containers },
    },
  ];

  splitDialogVisible.value = true;
};
// 联动更新其他记录
const updateLinkedRecords = (
  changedIndex,
  containerType,
  oldValue,
  newValue
) => {
  const diff = newValue - oldValue;

  if (diff === 0) return;

  // 找到除了当前修改记录之外的所有记录
  const otherRecords = splitRecords.value.filter(
    (_, index) => index !== changedIndex
  );

  // 如果没有其他记录，直接返回
  if (otherRecords.length === 0) return;

  // 找到第一个非零记录进行联动
  let targetIndex = -1;
  for (let i = 0; i < splitRecords.value.length; i++) {
    if (i !== changedIndex) {
      const currentValue = splitRecords.value[i].containers[containerType] || 0;
      if (currentValue > 0) {
        targetIndex = i;
        break;
      }
    }
  }

  // 如果没有找到非零记录，则找第一个非当前记录
  if (targetIndex === -1) {
    targetIndex = splitRecords.value.findIndex(
      (_, index) => index !== changedIndex
    );
  }

  // 如果仍然没有找到目标记录，说明只有一条记录
  if (targetIndex === -1) return;

  // 更新目标记录的对应集装箱数量
  const currentVal =
    splitRecords.value[targetIndex].containers[containerType] || 0;
  const newVal = Math.max(0, currentVal - diff); // 确保不为负数

  splitRecords.value[targetIndex].containers[containerType] = newVal;

  // 如果调整后的值小于0，说明超出了可调整范围，需要提示
  if (currentVal - diff < 0) {
    ElMessage.warning(`该集装箱类型已达到最大值`);
  }
};

// 处理输入框变化的联动逻辑
const handleContainerChange = (recordIndex, containerType, newValue) => {
  const record = splitRecords.value[recordIndex];
  const oldValue = record.containers[containerType] || 0;
  record.containers[containerType] = newValue;

  // 执行联动更新
  updateLinkedRecords(recordIndex, containerType, oldValue, newValue);

  // 验证记录
  validateRecord(record);
};
// 添加拆解记录
const addSplitRecord = () => {
  splitRecords.value.push({
    containers: {
      "20Empty": 0,
      "20Full": 0,
      "40Empty": 0,
      "40Full": 0,
      Other: 0,
    },
  });
};

// 移除拆解记录
const removeSplitRecord = (index) => {
  if (splitRecords.value.length > 1) {
    splitRecords.value.splice(index, 1);
  }
};

// 确认拆解
const confirmSplit = () => {
  if (!isValidSplit.value) {
    ElMessage.warning("拆解数量不匹配，请检查各项数量");
    return;
  }
  if (splitRecords.value.length <= 1) {
    ElMessage.warning("拆解记录需要大于1");
    return;
  }

  //检查是否有所有集装箱数量都为 0 的拆解记录
  const allZeroRecord = splitRecords.value.some((record) => {
    return Object.values(record.containers).every((value) => value === 0);
  });
  if (allZeroRecord) {
    ElMessage.warning("存在全部为 0 的拆解记录，请调整拆解数量");
    return;
  }

  // 修改参数格式：将 containers 提升到外层
  const formattedSplitRecords = splitRecords.value.map((record) => ({
    ...record.containers,
  }));

  // 调用后端接口进行拆解
  splitCargo(splitItem.value.id, formattedSplitRecords)
    .then((response) => {
      if (response.success) {
        // 假设接口返回字段 `success` 表示操作成功
        ElMessage.success(`运输环节 ${splitItem.value.logisticsMainId} 拆解成功`);
        splitDialogVisible.value = false;
        splitItem.value = null;
        splitRecords.value = [];

        // 刷新数据表格
        fetchData(
          queryParams.pageNum,
          queryParams.pageSize,
          queryParams.searchValue,
          queryParams.status
        );
      } else {
        ElMessage.error("拆解失败");
      }
    })
    .catch((error) => {
      console.error("拆解请求失败:", error);
      ElMessage.error("拆解请求失败，请重试");
    });
};

// 获取行样式
const getRowStyle = ({ row }) => {
  if (row.bufferZone) {
    const bufferColors = {
      1: "#ffebee",
      2: "#e8f5e8",
      3: "#e3f2fd",
    };
    return {
      backgroundColor: bufferColors[row.bufferZone] || "",
    };
  }
  return {};
};

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: "info",
    split: "warning",
    allocated: "success",
  };
  return types[status] || "";
};

// 获取状态标签
const getStatusLabel = (status) => {
  const labels = {
    pending: "待处理",
    split: "已拆解",
    allocated: "已分配",
  };
  return labels[status] || status;
};

// 获取缓冲区颜色
const getBufferColor = (bufferZone) => {
  const colors = {
    1: "#f44336",
    2: "#4caf50",
    3: "#2196f3",
  };
  return colors[bufferZone] || "";
};
// 获取集装箱标签
const getContainerLabel = (containerType) => {
  return containerTypes[containerType]?.label || containerType;
};
// 获取集装箱类型（排除 OtherDetail）
const getContainerTypes = () => {
  if (!splitItem.value) return [];

  return Object.keys(splitItem.value.containers).filter((type) =>
    containerTypes.hasOwnProperty(type)
  );
};
// 检查行是否可选
const isRowSelectable = (row) => {
  return row.status === "pending";
};

// 获取选中货物的航线
const getSelectedRoutes = () => {
  const routes = new Set();
  stowageStore.selectedCargo.forEach((item) => {
    routes.add(`${item.loadingTerminalId} → ${item.unloadingTerminalName}`);
  });
  return Array.from(routes);
};
// 处理批量操作
const handleBatchAction = (command) => {
  if (stowageStore.selectedCargo.length === 0) {
    ElMessage.warning("请先选择货物");
    return;
  }

  const firstItem = stowageStore.selectedCargo[0];
  let targetCargo = [];

  switch (command) {
    case "selectRoute":
      targetCargo = stowageStore.pendingCargoList.filter(
        (item) =>
          item.status === "pending" &&
          item.loadingTerminalId === firstItem.loadingTerminalId &&
          item.unloadingTerminalName === firstItem.unloadingTerminalName
      );
      break;
    case "selectshipperName":
      targetCargo = stowageStore.pendingCargoList.filter(
        (item) =>
          item.status === "pending" &&
          item.shipperName === firstItem.shipperName
      );
      break;
    case "selectProject":
      targetCargo = stowageStore.pendingCargoList.filter(
        (item) =>
          item.status === "pending" && item.project === firstItem.project
      );
      break;
    case "clear":
      stowageStore.clearSelection();
      ElMessage.success("已清空选择");
      return;
  }

  if (targetCargo.length > 0) {
    stowageStore.selectedCargo = targetCargo;
    ElMessage.success(`已选择 ${targetCargo.length} 项货物`);
    // 更新表格选中状态
    tableRef.value.clearSelection();
    targetCargo.forEach((cargo) => {
      tableRef.value.toggleRowSelection(cargo, true);
    });
  } else {
    ElMessage.warning("没有找到符合条件的货物");
  }
};

// 处理行拖拽开始
const handleRowDragStart = (row, event) => {
  if (row.status !== "pending") {
    event.preventDefault();
    return;
  }

  draggingRow.value = row;
  const dragData = {
    type: "cargo",
    item: row,
  };
  event.dataTransfer.setData("application/json", JSON.stringify(dragData));
  event.dataTransfer.effectAllowed = "copy";

  // 添加视觉反馈
  event.target.style.opacity = "0.5";
};

// 处理行拖拽结束
const handleRowDragEnd = (event) => {
  draggingRow.value = null;
  event.target.style.opacity = "1";
};
</script>

<style lang="scss" scoped>
.pending-cargo-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.search-filters {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
  flex-wrap: wrap;

  .filter-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
  }
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #b3d8ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .selection-info {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
  }
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.zero-quantity {
  color: #ccc;
}

.draggable-booking-no {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: grab;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  .drag-handle {
    opacity: 0.5;
    font-size: 12px;
  }

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);

    .drag-handle {
      opacity: 1;
    }
  }

  &:active {
    cursor: grabbing;
  }
}

.split-container {
  .original-info {
    margin-bottom: 20px;
  }

  .split-settings {
    .split-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        color: #333;
      }
    }

    .split-records {
      .split-record {
        margin-bottom: 16px;
        border: 1px solid #e6e6e6;
        border-radius: 6px;
        overflow: hidden;

        &.is-invalid {
          border-color: #f56565;
          background-color: #fef2f2;
        }

        .record-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          background: #f8f9fa;
          border-bottom: 1px solid #e6e6e6;

          .record-title {
            font-weight: 600;
            color: #333;
          }
        }

        .record-content {
          padding: 12px;

          .container-inputs {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 8px;

            .input-group {
              display: flex;
              align-items: center;
              gap: 4px;

              label {
                font-size: 12px;
                color: #666;
                min-width: 30px;
              }
            }
          }

          .record-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;

            .error-text {
              color: #f56565;
            }
          }
        }
      }
    }
  }

  .validation-info {
    margin-top: 16px;

    .validation-details {
      .validation-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        font-size: 13px;

        &:last-child {
          margin-bottom: 0;
        }

        .container-label {
          min-width: 60px;
          font-weight: 500;
          color: #333;
        }

        .quantity-info {
          display: flex;
          align-items: center;
          gap: 2px;
          min-width: 60px;

          .current {
            color: #409eff;
            font-weight: 600;
          }

          .separator {
            color: #999;
          }

          .total {
            color: #666;
            font-weight: 500;
          }
        }

        .status-missing {
          color: #e6a23c;
          font-size: 12px;
          font-weight: 500;
        }

        .status-excess {
          color: #f56565;
          font-size: 12px;
          font-weight: 500;
        }

        .status-match {
          color: #67c23a;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;

    .filter-actions {
      margin-left: 0;
      justify-content: center;
    }
  }

  .quick-actions {
    flex-direction: column;
    gap: 12px;

    .selection-info,
    .action-buttons {
      justify-content: center;
    }
  }
}
</style>