package com.ruoyi.customer.controller;

import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.customer.domain.CusHistory;
import com.ruoyi.customer.service.CusHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户变更历史Controller
 */
@Slf4j
@RestController
@RequestMapping("/cusHistory")
public class CusHistoryController {

    @Autowired
    private CusHistoryService cusHistoryService;

    /**
     * 查询客户时间变更列表
     *
     * @param cusId    客户id
     * @param pageNum  当前页
     * @param pageSize 分页显示数量
     * @return 客户时间变更列表
     */
    @GetMapping("/page")
    public Page<CusHistory> selectPage(String cusId, Integer pageNum, Integer pageSize) {
        return cusHistoryService.selectCusHistoryList(cusId, pageNum, pageSize);
    }

    /**
     * 查询客户时间变更详情
     *
     * @param id 客户变更历史id
     * @return CusMain
     */
    @GetMapping("/getInfo")
    public AjaxResult getCusHistoryInfo(String id) {
        return cusHistoryService.getCusHistoryById(id);
    }
}
