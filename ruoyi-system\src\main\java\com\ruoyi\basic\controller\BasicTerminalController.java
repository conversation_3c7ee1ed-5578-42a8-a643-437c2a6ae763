package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicTerminal;
import com.ruoyi.basic.service.IBasicAncBerthService;
import com.ruoyi.basic.service.IBasicTerminalService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mybatisflex.core.query.QueryMethods.distinct;
import static com.mybatisflex.core.query.QueryMethods.upper;

/**
 * 码头管理Controller
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RestController
@RequestMapping("/system/terminal")
public class BasicTerminalController extends BaseController {
    @Autowired
    private IBasicTerminalService basicTerminalService;

    /**
     * 查询码头管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:terminal:list')")
    @GetMapping("/list")
    public Page<BasicTerminal> list(BasicTerminal basicTerminal) {
        var pageDomain = TableSupport.buildPageRequest();

        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicTerminal.getTerminalCode())) {
            queryWrapper.like("terminal_code", basicTerminal.getTerminalCode());
        }
        if (StringUtils.isNotEmpty(basicTerminal.getTerminalName())) {
            queryWrapper.like("terminal_name", basicTerminal.getTerminalName());
        }
        if (StringUtils.isNotEmpty(basicTerminal.getType())) {
            queryWrapper.eq("type", basicTerminal.getType());
        }
        if (StringUtils.isNotEmpty(basicTerminal.getTerType())) {
            queryWrapper.eq("ter_type", basicTerminal.getTerType());
        }
        queryWrapper.orderBy("sort", true).orderBy("create_time", true);

        return basicTerminalService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    @PreAuthorize("@ss.hasPermi('system:terminal:list')")
    @GetMapping("/label")
    public AjaxResult label(String keyword) {

        QueryWrapper queryWrapper = QueryWrapper.create();

        if (StringUtils.isNotEmpty(keyword)) {

            String upperKey = keyword.toUpperCase();

            queryWrapper.and(upper(BasicTerminal::getTerminalName).like(upperKey))
                    .or(upper(BasicTerminal::getEnglishName).like(upperKey))
                    .or(upper(BasicTerminal::getTerminalCode).like(upperKey));

        }

        queryWrapper.orderBy(BasicTerminal::getSort, true).orderBy(BasicTerminal::getCreateTime, true);

        return AjaxResult.success(basicTerminalService.list(queryWrapper));
    }

    /**
     * 导出码头管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:terminal:export')")
    @Log(title = "码头管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicTerminal basicTerminal) {
        List<BasicTerminal> list = basicTerminalService.selectBasicTerminalList(basicTerminal);
        ExcelUtil<BasicTerminal> util = new ExcelUtil<BasicTerminal>(BasicTerminal.class);
        util.exportExcel(response, list, "码头管理数据");
    }

    /**
     * 获取码头管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:terminal:query')")
    @GetMapping(value = "/getTerminal/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(basicTerminalService.selectBasicTerminalById(id));
    }

    /**
     * 新增码头管理
     */
    @PreAuthorize("@ss.hasPermi('system:terminal:add')")
    @Log(title = "码头管理", businessType = BusinessType.INSERT)
    @PostMapping("/addTerminal")
    public AjaxResult add(@RequestBody BasicTerminal basicTerminal) {
        try {
            return toAjax(basicTerminalService.insertBasicTerminal(basicTerminal));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改码头管理
     */
    @PreAuthorize("@ss.hasPermi('system:terminal:edit')")
    @Log(title = "码头管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateTerminal")
    public AjaxResult edit(@RequestBody BasicTerminal basicTerminal) {
        try {
            return toAjax(basicTerminalService.updateBasicTerminal(basicTerminal));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除码头管理
     */
    @PreAuthorize("@ss.hasPermi('system:terminal:remove')")
    @Log(title = "码头管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteTerminal")
    public AjaxResult remove(@RequestBody List<String> ids) {
        return toAjax(basicTerminalService.deleteBasicTerminalByIds(ids));
    }

    /**
     * 远程搜索码头（支持名称或代码模糊查询，分页，专供下拉远程搜索）
     */
    @PreAuthorize("@ss.hasPermi('system:terminal:list')")
    @GetMapping("/remote-search")
    public AjaxResult remoteSearchTerminal(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(keyword)) {
            String upperKeyword = keyword.toUpperCase();
            queryWrapper.and("UPPER(terminal_name) LIKE ?", "%" + upperKeyword + "%")
                    .or("UPPER(terminal_code) LIKE ?", "%" + upperKeyword + "%");
        }
        queryWrapper.orderBy("sort", true).orderBy("create_time", true);
        Page<BasicTerminal> page = basicTerminalService.page(new Page<>(pageNum, pageSize), queryWrapper);
        return AjaxResult.success(page);
    }
}
