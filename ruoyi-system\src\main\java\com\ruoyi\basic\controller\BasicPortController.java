package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicPort;
import com.ruoyi.basic.service.IBasicPortService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicPortTableDef.BASIC_PORT;

/**
 * 港口管理Controller
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@RestController
@RequestMapping("/system/port")
public class BasicPortController extends BaseController {
    @Autowired
    private IBasicPortService basicPortService;

    /**
     * 查询港口管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:port:list')")
    @GetMapping("/list")
    public Page<BasicPort> list(BasicPort basicPort)
    {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_PORT.AREA_ID.eq(basicPort.getAreaId(), StringUtils.isNotEmpty(basicPort.getAreaId())))
                .and(BASIC_PORT.PORT_CODE.like(basicPort.getPortCode(), StringUtils.isNotEmpty(basicPort.getPortCode())))
                .and(BASIC_PORT.PORT_NAME.like(basicPort.getPortName(), StringUtils.isNotEmpty(basicPort.getPortName())))
                .orderBy(BASIC_PORT.CREATE_TIME.asc());
        
        var pageDomain = TableSupport.buildPageRequest();
        
        return basicPortService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
//        return getDataTable(page.getRecords(), page.getTotalRow());
    }

    @GetMapping("/label")
    public AjaxResult label(BasicPort basicPort){
        QueryWrapper queryWrapper = QueryWrapper.create().eq(BasicPort::getAreaId, basicPort.getAreaId());
        return AjaxResult.success(basicPortService.list(queryWrapper));
    }

    /**
     * 导出港口管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:port:export')")
    @Log(title = "港口管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicPort basicPort)
    {
        List<BasicPort> list = basicPortService.selectBasicPortList(basicPort);
        ExcelUtil<BasicPort> util = new ExcelUtil<BasicPort>(BasicPort.class);
        util.exportExcel(response, list, "港口管理数据");
    }

    /**
     * 获取港口管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:port:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicPortService.selectBasicPortById(id));
    }

    /**
     * 新增港口管理
     */
    @PreAuthorize("@ss.hasPermi('system:port:add')")
    @Log(title = "港口管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicPort basicPort)
    {
        try {
            return toAjax(basicPortService.insertBasicPort(basicPort));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改港口管理
     */
    @PreAuthorize("@ss.hasPermi('system:port:edit')")
    @Log(title = "港口管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicPort basicPort)
    {
        try {
            return toAjax(basicPortService.updateBasicPort(basicPort));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除港口管理
     */
    @PreAuthorize("@ss.hasPermi('system:port:remove')")
    @Log(title = "港口管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        try {
            return toAjax(basicPortService.deleteBasicPortByIds(ids));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
