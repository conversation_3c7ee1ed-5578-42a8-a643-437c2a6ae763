package com.ruoyi.system.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.SysFileManage;
import io.minio.errors.*;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

public interface ISysFileManageService extends IService<SysFileManage> {
    AjaxResult delete(SysFileManage fileManage) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException;

    List<SysFileManage> selectMyList(Long fileTypeId, String fileBusinessId);
}
