# 航线支线需求文档

## 一、展示航线信息

表单展示字段：主线名称，支线名称，起点码头名称，终点码头名称，距离（公里），时间（小时）。

## 二、新增航线信息

### 1. 基本信息
必填项：主线名称，支线名称，起点码头名称，终点码头名称。

### 2. 数据校验规则
1. 唯一性校验：
   - 如果码头A---->B已存在，再次提交时应提示"该航线关系已存在"
   - 同一码头不能同时作为起点和终点

2. 名称校验：
   - 主线名称不存在：提示"是否创建新主线？"
   - 支线名称不存在：提示"是否创建新支线？"，需保证支线归属主线不能为空
   - 名称长度限制：主线名称和支线名称长度不超过50个字符
   - 名称格式：不能包含特殊字符，只能包含中文、英文、数字和下划线

3. 数值校验：
   - 距离范围：0-9999.99公里，保留2位小数
   - 时间范围：0-999.99小时，保留2位小数
   - 距离和时间不能同时为空

4. 码头选择：
   - 码头名称从已有的 BASIC_PORT_AREA 表中选取，使用下拉选择框
   - 起点和终点码头不能相同

### 3. 操作流程
1. 选择/输入主线名称
   - 如果选择已有主线，自动加载该主线下的所有支线
   - 如果输入新主线，弹出确认框

2. 选择/输入支线名称
   - 如果选择已有支线，自动加载该支线的所有信息
   - 如果输入新支线，弹出确认框

3. 选择起点和终点码头
   - 从下拉列表中选择
   - 支持搜索过滤

4. 输入距离和时间
   - 支持手动输入
   - 支持小数点后两位

5. 提交保存
   - 进行所有数据校验
   - 显示保存成功/失败提示
   - 成功后自动刷新列表

### 4. 异常处理
1. 网络异常：提示"网络连接异常，请稍后重试"
2. 数据冲突：提示"数据已被其他用户修改，请刷新后重试"
3. 保存失败：显示具体错误原因，并提供重试选项

## 三、删除航线信息

1.删除前判断是否被其他模块引用，若存在依赖关系，提示无法删除。

2.提供删除确认弹窗。

3.在表单中选中删除，可批量删除。

## 四、修改航线信息

1.表单中选中修改，可修改字段：距离（公里），时间（小时）。

## 五、筛选查找航线信息

1.可筛选主线，支线，起始码头，终点码头，使用输入框进行筛选查找。

2.支持组合查询，如"某主线+某起点码头"。

3.提供重置按钮，清空筛选条件。

## 六、备注：

1.列表中添加操作列（编辑、删除按钮）

2.分页支持（每页20条）

3.需要导出按钮。

4.权限控制：
   - 管理员权限：
     * 可以查看所有航线信息
     * 可以新增航线信息
     * 可以修改航线信息
     * 可以删除航线信息
     * 可以导出数据
   - 非管理员权限：
     * 只能查看航线信息
     * 可以导出数据
     * 不能进行新增、修改、删除操作
   - 权限判断：
     * 根据用户角色自动判断权限
     * 非管理员用户的操作弹窗警示
     * 操作时进行权限二次校验