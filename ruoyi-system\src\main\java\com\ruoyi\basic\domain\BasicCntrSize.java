package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_cntr_size")
public class BasicCntrSize extends BaseEntity {

    /** 箱尺寸ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 箱尺寸代码 */
    @Excel(name = "箱尺寸代码")
    private String sizeCode;

    /** 箱尺寸名称 */
    @Excel(name = "箱尺寸名称")
    private String sizeName;

    /** ISO名称 */
    @Excel(name = "ISO名称")
    private String sizeIso;

    /** 净重 */
    @Excel(name = "净重")
    private BigDecimal sizeNetWeight;

    /** 超重值 */
    @Excel(name = "超重值")
    private BigDecimal sizeOverWeight;

    /** 归类尺寸 */
    @Excel(name = "归类尺寸")
    private String sizeCategorized;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

}
