package com.ruoyi.plan.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.plan.domain.RelationBookingVoyageCntrCoarse;
import com.ruoyi.plan.mapper.RelationBookingVoyageCntrCoarseMapper;
import com.ruoyi.plan.service.IRelationBookingVoyageCntrCoarseService;
// TODO: 等待重构 - booking模块已删除，相关功能需要重新实现
// import com.ruoyi.booking.domain.BookingCntrCoarse;
// import com.ruoyi.booking.service.IBookingCntrCoarseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class RelationBookingVoyageCntrCoarseServiceImpl extends ServiceImpl<RelationBookingVoyageCntrCoarseMapper, RelationBookingVoyageCntrCoarse>
        implements IRelationBookingVoyageCntrCoarseService {

    // TODO: 等待重构 - booking模块已删除，相关服务需要重新实现
    // @Autowired
    // private IBookingCntrCoarseService bookingCntrCoarseService;

    @Override
    public RelationBookingVoyageCntrCoarse selectById(String id) {
        return this.getById(id);
    }

    @Override
    public int insert(RelationBookingVoyageCntrCoarse entity) {
        return this.save(entity) ? 1 : 0;
    }

    @Override
    public int update(RelationBookingVoyageCntrCoarse entity) {
        return this.updateById(entity) ? 1 : 0;
    }

    @Override
    public int deleteByIds(String[] ids) {
        return this.removeByIds(Arrays.asList(ids)) ? ids.length : 0;
    }

    @Override
    public int deleteById(String id) {
        return this.removeById(id) ? 1 : 0;
    }

    @Override
    public Integer getSumQuantityByBookingCntrCoarseId(String bookingCntrCoarseId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select("quantity")
                .from("RELATION_BOOKING_VOYAGE_CNTR_COARSE")
                .where("booking_cntr_coarse_id = ?", bookingCntrCoarseId);
        
        List<RelationBookingVoyageCntrCoarse> list = this.list(queryWrapper);
        return list.stream()
                .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
                .sum();
    }

    @Override
    public Boolean isBookingFullyAllocated(String bookingId) {
        // TODO: 等待重构 - booking模块已删除，此方法需要重新实现
        // 1. 查询该订舱委托下的所有粗略箱信息
        // List<BookingCntrCoarse> cntrCoarseList = bookingCntrCoarseService.list(
        //         QueryWrapper.create()
        //                 .where("booking_id = ?", bookingId)
        // );
        // 
        // if (cntrCoarseList.isEmpty()) {
        //     return false; // 没有箱型信息，认为未配载
        // }
        // 
        // // 2. 检查每个箱型是否都已完全配载
        // for (BookingCntrCoarse coarse : cntrCoarseList) {
        //     // 获取该箱型的原始数量
        //     Integer originalQuantity = coarse.getQuantity() != null ? coarse.getQuantity() : 0;
        //     
        //     // 获取该箱型的已配数量
        //     Integer allocatedQuantity = getSumQuantityByBookingCntrCoarseId(coarse.getId());
        //     
        //     // 如果已配数量小于原始数量，说明未完全配载
        //     if (allocatedQuantity < originalQuantity) {
        //         return false;
        //     }
        // }
        // 
        // return true; // 所有箱型都已完全配载
        
        // 临时实现 - 返回false，等待重构
        return false;
    }
} 