package com.ruoyi.basic.mapper;

import com.mybatisflex.core.BaseMapper;
import com.ruoyi.basic.domain.BasicDanger;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface BasicDangerMapper extends BaseMapper<BasicDanger> {
    BasicDanger selectById(@Param("id") String id);
    int updateById(BasicDanger basicDanger);
    int deleteById(@Param("id") String id);
    int deleteBatchByIds(@Param("ids") List<String> ids);
}
