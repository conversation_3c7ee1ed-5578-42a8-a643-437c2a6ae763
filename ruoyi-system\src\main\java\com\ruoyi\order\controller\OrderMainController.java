package com.ruoyi.order.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.order.domain.OrderMain;
import com.ruoyi.order.service.IOrderMainService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.mybatisflex.core.paginate.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/order/main")
public class OrderMainController extends BaseController {
    @Autowired
    private IOrderMainService orderMainService;

    /**
     * 分页查询订单主表
     */
    @PreAuthorize("@ss.hasPermi('order:main:list')")
    @GetMapping("/page")
    public AjaxResult page(@RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            OrderMain orderMain) {
        log.debug("分页查询订单主表 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, orderMain);
        Page<OrderMain> page = orderMainService.selectOrderMainPage(pageNumber, pageSize, orderMain);
        log.debug("分页查询订单主表完成 - 总记录数: {}", page.getTotalRow());
        return AjaxResult.success(page);
    }

    /**
     * 导出订单主表列表
     */
    @PreAuthorize("@ss.hasPermi('order:main:export')")
    @Log(title = "订单主表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderMain orderMain) {
        log.debug("导出订单主表数据 - 查询条件: {}", orderMain);
        List<OrderMain> list = orderMainService.selectOrderMainPage(1, Integer.MAX_VALUE, orderMain).getRecords();
        log.debug("导出订单主表数据 - 导出记录数: {}", list.size());
        ExcelUtil<OrderMain> util = new ExcelUtil<>(OrderMain.class);
        util.exportExcel(response, list, "订单主表数据");
        log.debug("导出订单主表数据完成");
    }

    /**
     * 获取订单主表详细信息
     */
    @PreAuthorize("@ss.hasPermi('order:main:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        log.debug("获取订单主表详细信息 - ID: {}", id);
        OrderMain orderMain = orderMainService.getById(id);
        log.debug("获取订单主表详细信息完成 - 结果: {}", orderMain);
        return success(orderMain);
    }

    /**
     * 新增订单主表
     */
    @PreAuthorize("@ss.hasPermi('order:main:add')")
    @Log(title = "订单主表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrderMain orderMain) {
        log.debug("新增订单主表 - 数据: {}", orderMain);
        boolean success = orderMainService.save(orderMain);
        log.debug("新增订单主表完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 修改订单主表
     */
    @PreAuthorize("@ss.hasPermi('order:main:edit')")
    @Log(title = "订单主表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderMain orderMain) {
        log.debug("修改订单主表 - 数据: {}", orderMain);
        boolean success = orderMainService.updateById(orderMain);
        log.debug("修改订单主表完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 删除订单主表
     */
    @PreAuthorize("@ss.hasPermi('order:main:remove')")
    @Log(title = "订单主表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        log.debug("删除订单主表 - ID数组: {}", Arrays.toString(ids));
        boolean success = orderMainService.removeByIds(Arrays.asList(ids));
        log.debug("删除订单主表完成 - 结果: {}", success);
        return toAjax(success);
    }
}