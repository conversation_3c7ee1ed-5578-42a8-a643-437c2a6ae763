package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicDanger;
import com.ruoyi.basic.mapper.BasicDangerMapper;

import java.util.List;

public interface IBasicDangerService extends IService<BasicDanger> {
    /**
     * 获取Mapper
     *
     * @return 返回Mapper
     */
    BasicDangerMapper getMapper();

    /**
     * 查询危险品等级
     *
     * @param id 危险品等级主键
     * @return 危险品等级
     */
    public BasicDanger selectBasicDangerById(String id);

    /**
     * 查询危险品等级列表
     *
     * @param basicDanger 危险品等级
     * @return 危险品等级集合
     */
    public List<BasicDanger> selectBasicDangerList(BasicDanger basicDanger);

    /**
     * 新增危险品等级
     *
     * @param basicDanger 危险品等级
     * @return 结果
     */
    public boolean insertBasicDanger(BasicDanger basicDanger);

    /**
     * 修改危险品等级
     *
     * @param basicDanger 危险品等级
     * @return 结果
     */
    public boolean updateBasicDanger(BasicDanger basicDanger);

    /**
     * 批量删除危险品等级
     *
     * @param ids 需要删除的危险品等级主键集合
     * @return 结果
     */
    public boolean deleteBasicDangerByIds(List<String> ids);

    /**
     * 删除危险品等级信息
     *
     * @param id 危险品等级主键
     * @return 结果
     */
    public boolean deleteBasicDangerById(String id);
}
