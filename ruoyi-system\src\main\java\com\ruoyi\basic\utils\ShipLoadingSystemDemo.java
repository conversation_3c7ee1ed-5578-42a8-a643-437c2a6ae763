package com.ruoyi.basic.utils;
// import com.alibaba.fastjson2.JSONObject;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.stream.Collectors;

public class ShipLoadingSystemDemo {
    static class Ship {
        String name;
        double length; // 船长
        double width; // 船宽
        double height; // 船高
        double draft; // 吃水
        double maxWeight; // 最大载重
        int maxTEU; // 最大TEU
        String currentDestination; // 当前目的港口
        LocalDateTime estimatedArrival; // 预计到港时间（小时）
        double currentCargoWeight; // 当前货物载重量
        Map<String, Double> maxCargoWeightPerType; // 每种货类最大载重
        boolean isOnePortShip; // 是否一港通
        // 当前装载的货物
        List<Cargo> loadedCargo = new ArrayList<>();
        LocalDateTime operationFinishTime; // 当前装卸作业预计完成时间

        public Ship(String name, double length, double width, double height, double draft,
                    double maxWeight, int maxTEU, String currentDestination,
                    LocalDateTime estimatedArrival, Map<String, Double> maxCargoWeightPerType,
                    boolean isOnePortShip) {
            this.name = name;
            this.length = length;
            this.width = width;
            this.height = height;
            this.draft = draft;
            this.maxWeight = maxWeight;
            this.maxTEU = maxTEU;
            this.currentDestination = currentDestination;
            this.estimatedArrival = estimatedArrival;
            this.maxCargoWeightPerType = maxCargoWeightPerType;
            this.isOnePortShip = isOnePortShip;
            this.currentCargoWeight = 0.0;
            this.operationFinishTime = null;
        }

        // 获取剩余载重容量
        public double getRemainingWeightCapacity() {
            return maxWeight - currentCargoWeight;
        }

        // 获取剩余TEU容量
        public int getRemainingTEUCapacity() {
            int loadedTEU = loadedCargo.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
            return maxTEU - loadedTEU;
        }

        // 获取特定货类的剩余容量
        public double getRemainingCapacityForCargoType(String cargoType) {
            double currentTypeWeight = loadedCargo.stream()
                    .filter(c -> c.cargoType.equals(cargoType))
                    .mapToDouble(c -> c.weight)
                    .sum();

            double maxForType = maxCargoWeightPerType.getOrDefault(cargoType, Double.MAX_VALUE);
            return Math.min(maxForType - currentTypeWeight, getRemainingWeightCapacity());
        }

        // 添加货物
        public boolean addCargo(Cargo cargo) {
            if (cargo.isOnePort && !this.isOnePortShip) {
//                System.out.println("【装载失败】货物是一港通，船不是一港通");
                return false;
            }
            if (this.isOnePortShip && cargo.isOnePort) {
                if (!loadedCargo.isEmpty()) {
                    String onePortDest = loadedCargo.get(0).destinationPort;
                    if (!cargo.destinationPort.equals(onePortDest)) {
//                        System.out.println("【装载失败】一港通船舶装载一港通货物时，目的港不一致");
                        return false;
                    }
                    for (Cargo c : loadedCargo) {
                        if (!c.destinationPort.equals(cargo.destinationPort)) {
//                            System.out.println("【装载失败】一港通船舶已有货物目的港与新货物不一致");
                            return false;
                        }
                    }
                }
            }
            boolean hasOnePortCargo = loadedCargo.stream().anyMatch(c -> c.isOnePort);
            if (hasOnePortCargo) {
                String onePortDest = loadedCargo.stream().filter(c -> c.isOnePort).findFirst().get().destinationPort;
                if (!cargo.destinationPort.equals(onePortDest)) {
//                    System.out.println("【装载失败】船上已有一港通货物，目的港不一致");
                    return false;
                }
            }
            if (cargo.weight > getRemainingWeightCapacity()) {
//                System.out.println("【装载失败】重量超限");
                return false;
            }
            int requiredTEU = cargo.containerType.equals("20ft") ? 1 : 2;
            if (requiredTEU > getRemainingTEUCapacity()) {
//                System.out.println("【装载失败】TEU超限");
                return false;
            }
            if (cargo.weight > getRemainingCapacityForCargoType(cargo.cargoType)) {
//                System.out.println("【装载失败】货类容量超限");
                return false;
            }
            // 添加货物
            loadedCargo.add(cargo);
            currentCargoWeight += cargo.weight;
            return true;
        }

        // 卸载货物
        public List<Cargo> unloadCargo(String port, boolean unloadAll) {
            List<Cargo> unloaded = new ArrayList<>();
            Iterator<Cargo> iterator = loadedCargo.iterator();

            while (iterator.hasNext()) {
                Cargo cargo = iterator.next();
                if (cargo.destinationPort.equals(port)) {
                    if (unloadAll || cargo.destinationPort.equals(currentDestination)) {
                        unloaded.add(cargo);
                        currentCargoWeight -= cargo.weight;
                        iterator.remove();
                    }
                }
            }

            return unloaded;
        }

        @Override
        public String toString() {
            return String.format("Ship{name='%s', currentDestination='%s', capacity=%.1f/%.1f tons, TEU=%d/%d}",
                    name, currentDestination, currentCargoWeight, maxWeight,
                    maxTEU - getRemainingTEUCapacity(), maxTEU);
        }
    }

    // 坐标点类
    static class Coordinate {
        double latitude; // 纬度
        double longitude; // 经度
        String portName; // 港口名称（可选）

        public Coordinate(double latitude, double longitude) {
            this.latitude = latitude;
            this.longitude = longitude;
        }

        public Coordinate(double latitude, double longitude, String portName) {
            this.latitude = latitude;
            this.longitude = longitude;
            this.portName = portName;
        }

        @Override
        public String toString() {
            return String.format("Coordinate{lat=%.4f, lng=%.4f, port='%s'}", 
                    latitude, longitude, portName != null ? portName : "未知");
        }
    }

    // 水道类 - 表示单个水道
    static class Waterway {
        String name; // 水道名称
        double maxLength; // 最大船长
        double maxWidth; // 最大船宽
        double maxHeight; // 最大船高
        double maxDraft; // 最大吃水
        int distance; // 水道距离
        int time; // 航行时间（小时）
        List<Coordinate> coordinates; // 水道坐标点列表

        public Waterway(String name, double maxLength, double maxWidth, double maxHeight, double maxDraft, int distance, int time) {
            this.name = name;
            this.maxLength = maxLength;
            this.maxWidth = maxWidth;
            this.maxHeight = maxHeight;
            this.maxDraft = maxDraft;
            this.distance = distance;
            this.time = time;
            this.coordinates = new ArrayList<>();
        }

        public Waterway(String name, double maxLength, double maxWidth, double maxHeight, double maxDraft, int distance, int time, List<Coordinate> coordinates) {
            this.name = name;
            this.maxLength = maxLength;
            this.maxWidth = maxWidth;
            this.maxHeight = maxHeight;
            this.maxDraft = maxDraft;
            this.distance = distance;
            this.time = time;
            this.coordinates = coordinates != null ? coordinates : new ArrayList<>();
        }

        // 添加坐标点
        public void addCoordinate(Coordinate coordinate) {
            this.coordinates.add(coordinate);
        }

        // 添加坐标点（简化版）
        public void addCoordinate(double latitude, double longitude) {
            this.coordinates.add(new Coordinate(latitude, longitude));
        }

        // 添加坐标点（带港口名）
        public void addCoordinate(double latitude, double longitude, String portName) {
            this.coordinates.add(new Coordinate(latitude, longitude, portName));
        }

        // 检查船舶是否可以通过此水道
        public boolean canShipPass(Ship ship) {
            return ship.length <= maxLength && 
                   ship.width <= maxWidth && 
                   ship.draft <= maxDraft;
        }

        // 获取起运港坐标（第一个坐标点）
        public Coordinate getOriginCoordinate() {
            return coordinates.isEmpty() ? null : coordinates.get(0);
        }

        // 获取目的港坐标（最后一个坐标点）
        public Coordinate getDestinationCoordinate() {
            return coordinates.isEmpty() ? null : coordinates.get(coordinates.size() - 1);
        }

        @Override
        public String toString() {
            return String.format("Waterway{name='%s', maxLength=%.1f, maxWidth=%.1f, maxHeight=%.1f, maxDraft=%.1f, distance=%d, time=%d, coordinates=%d}",
                    name, maxLength, maxWidth, maxHeight, maxDraft, distance, time, coordinates.size());
        }
    }

    // 路线类 - 表示两点间的直达路线
    static class Route {
        String originPort; // 起运港
        String destinationPort; // 目的港
        List<Waterway> waterways; // 中间的水道列表
        int totalDistance; // 总距离
        int totalTime; // 总时间

        public Route(String originPort, String destinationPort, List<Waterway> waterways) {
            this.originPort = originPort;
            this.destinationPort = destinationPort;
            this.waterways = waterways != null ? waterways : new ArrayList<>();
            this.totalDistance = this.waterways.stream().mapToInt(w -> w.distance).sum();
            this.totalTime = this.waterways.stream().mapToInt(w -> w.time).sum();
        }

        // 检查船舶是否可以通过整个路线
        public boolean canShipPass(Ship ship) {
            return waterways.stream().allMatch(w -> w.canShipPass(ship));
        }

        // 获取路线的总距离
        public int getTotalDistance() {
            return totalDistance;
        }

        // 获取路线的总时间
        public int getTotalTime() {
            return totalTime;
        }

        // 检查路线是否连通（水道坐标点能串通）
        public boolean isConnected() {
            if (waterways.isEmpty()) return false;
            
            // 检查第一个水道的起运港
            Waterway firstWaterway = waterways.get(0);
            Coordinate firstOrigin = firstWaterway.getOriginCoordinate();
            if (firstOrigin == null || firstOrigin.portName == null) return false;
            
            // 检查最后一个水道的目的港
            Waterway lastWaterway = waterways.get(waterways.size() - 1);
            Coordinate lastDest = lastWaterway.getDestinationCoordinate();
            if (lastDest == null || lastDest.portName == null) return false;
            
            // 检查中间水道是否连通
            for (int i = 0; i < waterways.size() - 1; i++) {
                Waterway current = waterways.get(i);
                Waterway next = waterways.get(i + 1);
                
                Coordinate currentDest = current.getDestinationCoordinate();
                Coordinate nextOrigin = next.getOriginCoordinate();
                
                if (currentDest == null || nextOrigin == null) return false;
                
                // 检查坐标是否匹配（允许一定的误差）
                double latDiff = Math.abs(currentDest.latitude - nextOrigin.latitude);
                double lngDiff = Math.abs(currentDest.longitude - nextOrigin.longitude);
                
                // 如果坐标差异太大，认为不连通
                if (latDiff > 0.01 || lngDiff > 0.01) {
                    return false;
                }
            }
            
            return true;
        }

        @Override
        public String toString() {
            return String.format("Route{origin='%s', destination='%s', waterways=%d, totalDistance=%d, totalTime=%d, connected=%s}",
                    originPort, destinationPort, waterways.size(), totalDistance, totalTime, isConnected());
        }
    }

    // 水道组类 - 表示从起运港到目的港的完整水道组合
    static class WaterwayGroup {
        String name; // 水道组名称
        List<Waterway> waterways; // 包含的水道列表
        int totalDistance; // 总距离
        int totalTime; // 总时间
        boolean isPhysicallyConnected; // 是否物理连通

        public WaterwayGroup(String name, List<Waterway> waterways) {
            this.name = name;
            this.waterways = waterways != null ? waterways : new ArrayList<>();
            this.totalDistance = this.waterways.stream().mapToInt(w -> w.distance).sum();
            this.totalTime = this.waterways.stream().mapToInt(w -> w.time).sum();
            this.isPhysicallyConnected = checkPhysicalConnectivity();
        }

        // 检查水道组是否物理连通（水道坐标点能串通）
        private boolean checkPhysicalConnectivity() {
            if (waterways.isEmpty()) return false;
            
            // 检查每个水道是否都有两个坐标点
            for (Waterway waterway : waterways) {
                if (waterway.coordinates.size() != 2) {
                    System.out.println("警告: 水道 " + waterway.name + " 的坐标点数量不是2个，而是 " + waterway.coordinates.size() + " 个");
                    return false;
                }
            }
            
            // 检查水道之间的连通性
            for (int i = 0; i < waterways.size() - 1; i++) {
                Waterway current = waterways.get(i);
                Waterway next = waterways.get(i + 1);
                
                Coordinate currentDest = current.getDestinationCoordinate();
                Coordinate nextOrigin = next.getOriginCoordinate();
                
                if (currentDest == null || nextOrigin == null) {
                    System.out.println("警告: 水道 " + current.name + " 或 " + next.name + " 缺少起点或终点坐标");
                    return false;
                }
                
                // 检查坐标是否匹配（允许一定的误差）
                double latDiff = Math.abs(currentDest.latitude - nextOrigin.latitude);
                double lngDiff = Math.abs(currentDest.longitude - nextOrigin.longitude);
                
                // 如果坐标差异太大，认为不连通
                if (latDiff > 0.01 || lngDiff > 0.01) {
                    System.out.println("警告: 水道 " + current.name + " 的终点坐标与水道 " + next.name + " 的起点坐标不匹配");
                    System.out.println("  当前水道终点: " + currentDest);
                    System.out.println("  下一水道起点: " + nextOrigin);
                    return false;
                }
            }
            
            return true;
        }

        // 检查船舶是否可以通过整个水道组
        public boolean canShipPass(Ship ship) {
            return waterways.stream().allMatch(w -> w.canShipPass(ship));
        }

        // 获取水道组的总距离
        public int getTotalDistance() {
            return totalDistance;
        }

        // 获取水道组的总时间
        public int getTotalTime() {
            return totalTime;
        }

        // 检查水道组是否物理连通
        public boolean isPhysicallyConnected() {
            return isPhysicallyConnected;
        }

        // 获取水道组的起点港口名称
        public String getOriginPort() {
            if (waterways.isEmpty()) return null;
            Coordinate origin = waterways.get(0).getOriginCoordinate();
            return origin != null ? origin.portName : null;
        }

        // 获取水道组的终点港口名称
        public String getDestinationPort() {
            if (waterways.isEmpty()) return null;
            Coordinate dest = waterways.get(waterways.size() - 1).getDestinationCoordinate();
            return dest != null ? dest.portName : null;
        }

        @Override
        public String toString() {
            return String.format("WaterwayGroup{name='%s', waterways=%d, totalDistance=%d, totalTime=%d, connected=%s, origin=%s, destination=%s}",
                    name, waterways.size(), totalDistance, totalTime, isPhysicallyConnected, getOriginPort(), getDestinationPort());
        }
    }

    static class RouteInfo {
        int distance; // 距离
        int time;    // 时间（单位：小时）

        public RouteInfo(int distance, int time) {
            this.distance = distance;
            this.time = time;
        }
    }

    static class RouteGraph {
        Map<String, Map<String, List<WaterwayGroup>>> graph = new HashMap<>();

        // 添加水道组航线
        public void addWaterwayGroup(String from, String to, WaterwayGroup waterwayGroup) {
            graph.computeIfAbsent(from, k -> new HashMap<>()).put(to, Arrays.asList(waterwayGroup));
            graph.computeIfAbsent(to, k -> new HashMap<>()).put(from, Arrays.asList(waterwayGroup));
        }

        // 添加多条水道组航线（从起运港到目的港可能有多个水道组选择）
        public void addWaterwayGroups(String from, String to, List<WaterwayGroup> waterwayGroups) {
            graph.computeIfAbsent(from, k -> new HashMap<>()).put(to, waterwayGroups);
            graph.computeIfAbsent(to, k -> new HashMap<>()).put(from, waterwayGroups);
        }

        // 获取船舶可用的最短路径（按时间或距离）
        public Map<String, Integer> shortestPath(String start, String mode, Ship ship) {
            Map<String, Integer> distances = new HashMap<>();
            PriorityQueue<Map.Entry<String, Integer>> pq = new PriorityQueue<>(
                    (a, b) -> a.getValue() - b.getValue()
            );
            
            for (String port : graph.keySet()) {
                distances.put(port, Integer.MAX_VALUE);
            }
            distances.put(start, 0);
            pq.add(new AbstractMap.SimpleEntry<>(start, 0));
            
            while (!pq.isEmpty()) {
                Map.Entry<String, Integer> entry = pq.poll();
                String currentPort = entry.getKey();
                int currentDist = entry.getValue();
                
                if (currentDist > distances.get(currentPort)) continue;
                
                if (graph.containsKey(currentPort)) {
                    for (Map.Entry<String, List<WaterwayGroup>> neighbor : graph.get(currentPort).entrySet()) {
                        String nextPort = neighbor.getKey();
                        List<WaterwayGroup> waterwayGroups = neighbor.getValue();
                        
                        // 找到船舶可以通过的最短水道组（只考虑物理连通的）
                        int bestValue = Integer.MAX_VALUE;
                        for (WaterwayGroup wg : waterwayGroups) {
                            if (wg.isPhysicallyConnected() && wg.canShipPass(ship)) {
                                int value = mode.equals("time") ? wg.getTotalTime() : wg.getTotalDistance();
                                bestValue = Math.min(bestValue, value);
                            }
                        }
                        
                        if (bestValue != Integer.MAX_VALUE) {
                            int newDist = currentDist + bestValue;
                            if (newDist < distances.get(nextPort)) {
                                distances.put(nextPort, newDist);
                                pq.add(new AbstractMap.SimpleEntry<>(nextPort, newDist));
                            }
                        }
                    }
                }
            }
            return distances;
        }

        // 获取船舶可用的最短时间路径的完整港口序列
        public List<String> getShortestPathSequence(String start, String end, Ship ship) {
            Map<String, Integer> dist = new HashMap<>();
            Map<String, String> prev = new HashMap<>();
            PriorityQueue<Map.Entry<String, Integer>> pq = new PriorityQueue<>(
                    (a, b) -> a.getValue() - b.getValue()
            );
            
            for (String port : graph.keySet()) {
                dist.put(port, Integer.MAX_VALUE);
            }
            dist.put(start, 0);
            pq.add(new AbstractMap.SimpleEntry<>(start, 0));
            
            while (!pq.isEmpty()) {
                Map.Entry<String, Integer> entry = pq.poll();
                String currentPort = entry.getKey();
                int currentDist = entry.getValue();
                
                if (currentDist > dist.get(currentPort)) continue;
                
                if (graph.containsKey(currentPort)) {
                    for (Map.Entry<String, List<WaterwayGroup>> neighbor : graph.get(currentPort).entrySet()) {
                        String nextPort = neighbor.getKey();
                        List<WaterwayGroup> waterwayGroups = neighbor.getValue();
                        
                        // 找到船舶可以通过的最短时间水道组（只考虑物理连通的）
                        int bestTime = Integer.MAX_VALUE;
                        for (WaterwayGroup wg : waterwayGroups) {
                            if (wg.isPhysicallyConnected() && wg.canShipPass(ship)) {
                                bestTime = Math.min(bestTime, wg.getTotalTime());
                            }
                        }
                        
                        if (bestTime != Integer.MAX_VALUE) {
                            int newDist = currentDist + bestTime;
                            if (newDist < dist.get(nextPort)) {
                                dist.put(nextPort, newDist);
                                prev.put(nextPort, currentPort);
                                pq.add(new AbstractMap.SimpleEntry<>(nextPort, newDist));
                            }
                        }
                    }
                }
            }
            
            // 回溯路径
            List<String> path = new ArrayList<>();
            String curr = end;
            while (curr != null && prev.containsKey(curr)) {
                path.add(curr);
                curr = prev.get(curr);
            }
            if (curr != null && curr.equals(start)) {
                path.add(start);
            }
            Collections.reverse(path);
            return path;
        }

        // 获取船舶从起运港到目的港的最优水道组
        public WaterwayGroup getBestWaterwayGroup(String from, String to, Ship ship, String mode) {
            if (!graph.containsKey(from) || !graph.get(from).containsKey(to)) {
                return null;
            }
            
            List<WaterwayGroup> waterwayGroups = graph.get(from).get(to);
            WaterwayGroup bestGroup = null;
            int bestValue = Integer.MAX_VALUE;
            
            for (WaterwayGroup wg : waterwayGroups) {
                // 只考虑物理连通且船舶可以通过的水道组
                if (wg.isPhysicallyConnected() && wg.canShipPass(ship)) {
                    int value = mode.equals("time") ? wg.getTotalTime() : wg.getTotalDistance();
                    if (value < bestValue) {
                        bestValue = value;
                        bestGroup = wg;
                    }
                }
            }
            
            return bestGroup;
        }

        // 兼容旧版本的简单航线添加方法（用于测试）
        public void addRoute(String from, String to, int distance, int time) {
            Waterway waterway = new Waterway("Default", 1000, 1000, 1000, 1000, distance, time);
            WaterwayGroup waterwayGroup = new WaterwayGroup("Default", Arrays.asList(waterway));
            addWaterwayGroup(from, to, waterwayGroup);
        }
    }

    static class Port {
        String name;
        Coordinate coordinate; // 港口坐标点
        List<Cargo> cargoList = new ArrayList<>();

        public Port(String name) {
            this.name = name;
            this.coordinate = null;
        }

        public Port(String name, double latitude, double longitude) {
            this.name = name;
            this.coordinate = new Coordinate(latitude, longitude, name);
        }

        public Port(String name, Coordinate coordinate) {
            this.name = name;
            this.coordinate = coordinate;
        }

        public void setCoordinate(double latitude, double longitude) {
            this.coordinate = new Coordinate(latitude, longitude, name);
        }

        public void setCoordinate(Coordinate coordinate) {
            this.coordinate = coordinate;
        }

        public Coordinate getCoordinate() {
            return coordinate;
        }

        public void addCargo(Cargo cargo) {
            cargoList.add(cargo);
        }

        public void removeCargo(Cargo cargo) {
            cargoList.remove(cargo);
        }

        // 获取指定目的港的货物
        public List<Cargo> getCargoForDestination(String destination) {
            List<Cargo> result = new ArrayList<>();
            for (Cargo cargo : cargoList) {
                if (cargo.destinationPort.equals(destination)) {
                    result.add(cargo);
                }
            }
            return result;
        }

        @Override
        public String toString() {
            if (coordinate != null) {
                return String.format("Port{name='%s', coordinate=%s}", name, coordinate);
            } else {
                return String.format("Port{name='%s', coordinate=null}", name);
            }
        }
    }

    static class Cargo {
        String cargoType; // 货类
        double weight; // 重量
        String containerType; // 箱型 (20ft/40ft)
        String originPort; // 起运港
        String destinationPort; // 目的港
        boolean isOnePort; // 是否一港通
        int dangerLevel; // 危险品等级 (0表示普通货物)
        String consignmentId; // 委托号
        boolean canSplit; // 委托能否拆分
        LocalDate deadline; // 委托截止日期
        String containerNumber; // 箱号
        LocalDate latestDeliveryDate; // 最晚发货日期

        public Cargo(String cargoType, double weight, String containerType,
                     String originPort, String destinationPort, boolean isOnePort,
                     int dangerLevel, String consignmentId, boolean canSplit,
                     LocalDate deadline, String containerNumber, LocalDate latestDeliveryDate) {
            this.cargoType = cargoType;
            this.weight = weight;
            this.containerType = containerType;
            this.originPort = originPort;
            this.destinationPort = destinationPort;
            this.isOnePort = isOnePort;
            this.dangerLevel = dangerLevel;
            this.consignmentId = consignmentId;
            this.canSplit = canSplit;
            this.deadline = deadline;
            this.containerNumber = containerNumber;
            this.latestDeliveryDate = latestDeliveryDate;
        }

        @Override
        public String toString() {
            return String.format("Cargo{type='%s', %.1f tons, %s, %s->%s, consignment=%s, containerNumber=%s, isOnePort=%b, deadline=%s, latestDeliveryDate=%s}",
                    cargoType, weight, containerType, originPort,
                    destinationPort, consignmentId, containerNumber, isOnePort, deadline, latestDeliveryDate);
        }

        public String getConsignmentId() {
            return consignmentId;
        }

        public String getContainerNumber() {
            return containerNumber;
        }

        public String getDestinationPort() {
            return destinationPort;
        }
    }

    static Random random = new Random();

    // 随机生成箱号
    private static String generateRandomContainerNumber() {
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            sb.append(letters.charAt(random.nextInt(letters.length())));
        }
        for (int i = 0; i < 7; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    // 随机生成货物
    public static void generateRandomCargo(Port port, LocalDate currentDate) {
        String[] cargoTypes = {"General", "Refrigerated", "Dangerous"};
        String[] containerTypes = {"20ft", "40ft"};
        String[] destPorts = {"虎门", "容奇", "北滘", "黄埔", "清远"};
        String cargoType = cargoTypes[random.nextInt(cargoTypes.length)];
        String origin = port.name;
        String dest;
        do {
            dest = destPorts[random.nextInt(destPorts.length)];
        } while (dest.equals(origin));
        boolean isOnePort = random.nextDouble() > 0.9 ? true : false;
        int dangerLevel = cargoType.equals("Dangerous") ? 1 + random.nextInt(3) : 0;
        String consignmentId = origin.substring(0,2).toUpperCase() + String.format("%03d", random.nextInt(1000));
        boolean canSplit = random.nextBoolean();
        LocalDate deadline = currentDate.plusDays(10 + random.nextInt(3));
        LocalDate latestDeliveryDate = currentDate.plusDays(3 + random.nextInt(3));
        if (canSplit) {
            String containerType = containerTypes[random.nextInt(containerTypes.length)];
            double weight = 5 + random.nextInt(20);
            String containerNumber = generateRandomContainerNumber();
            port.addCargo(new Cargo(cargoType, weight, containerType, origin, dest, isOnePort, dangerLevel, consignmentId, canSplit, deadline, containerNumber, latestDeliveryDate));
        } else {
            int boxCount = 2 + random.nextInt(3); // 2~4个箱子
            for (int i = 0; i < boxCount; i++) {
                String containerType = containerTypes[random.nextInt(containerTypes.length)];
                double weight = 5 + random.nextInt(20);
                String containerNumber = generateRandomContainerNumber();
                port.addCargo(new Cargo(cargoType, weight, containerType, origin, dest, isOnePort, dangerLevel, consignmentId, canSplit, deadline, containerNumber, latestDeliveryDate));
            }
        }
    }

    // 规划下一目的港和到达时间（全排列最优可行方案）
    public static void planNextDestination(Ship ship, ShipLoadingSystem system, LocalDate currentDate) {
        // 优先判断船上是否有货物
        if (ship.loadedCargo != null && !ship.loadedCargo.isEmpty()) {
            // 船上有货物，枚举所有目的港顺序
            Set<String> destSet = ship.loadedCargo.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
            List<String> destList = new ArrayList<>(destSet);
            List<List<String>> allOrders = new ArrayList<>();
            permute(destList, 0, allOrders);
            LocalDateTime bestArrival = null;
            List<String> bestOrder = null;
            int minTotalHours = Integer.MAX_VALUE;
            for (List<String> order : allOrders) {
                LocalDateTime t = LocalDateTime.of(currentDate, java.time.LocalTime.of(0,0));
                String curPort = ship.currentDestination;
                boolean allOnTime = true;
                Map<String, LocalDateTime> arrivalMap = new HashMap<>();
                int totalHours = 0;
                for (String dest : order) {
                    int travelHours = system.routeGraph.shortestPath(curPort, "time", ship).get(dest);
                    t = t.plusHours(travelHours);
                    totalHours += travelHours;
                    // 检查所有去dest的货物
                    for (Cargo c : ship.loadedCargo) {
                        if (c.destinationPort.equals(dest)) {
                            if (t.toLocalDate().isAfter(c.deadline)) {
                                allOnTime = false;
                                break;
                            }
                            arrivalMap.put(dest, t);
                        }
                    }
                    if (!allOnTime) break;
                    curPort = dest;
                }
                if (allOnTime && totalHours < minTotalHours) {
                    minTotalHours = totalHours;
                    bestOrder = new ArrayList<>(order);
                    bestArrival = arrivalMap.get(order.get(0)); // 只用于日志
                }
            }
            if (bestOrder != null) {
                // 选择第一个目的港为下一目的港
                String nextDest = bestOrder.get(0);
                int hours = system.routeGraph.shortestPath(ship.currentDestination, "time", ship).get(nextDest);
                ship.currentDestination = nextDest;
                ship.estimatedArrival = LocalDateTime.of(currentDate, java.time.LocalTime.of(0,0)).plusHours(hours);
                System.out.println("[调度] " + ship.name + " 规划下一目的港: " + nextDest + ", 预计到达: " + ship.estimatedArrival + ", 最优顺序: " + bestOrder);
            } else {
                // 没有可行方案，停留在港口
                ship.estimatedArrival = null;
                System.out.println("[调度] " + ship.name + " 暂无可行航线，停留在 " + ship.currentDestination);
            }
        } else {
            // 船上没有货物，先尝试装货
            Port currentPort = system.ports.get(ship.currentDestination);
            if (currentPort == null) return;
            boolean loaded = false;
            // 简单贪心装载
            for (Cargo cargo : new ArrayList<>(currentPort.cargoList)) {
                if (currentDate.atStartOfDay().isAfter(cargo.latestDeliveryDate.atStartOfDay())) continue;
                if (ship.addCargo(cargo)) {
                    currentPort.removeCargo(cargo);
                    loaded = true;
                }
                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
            }
            if (loaded) {
                // 装上货物后，重新走"有货物"分支
                planNextDestination(ship, system, currentDate);
                return;
            }
            // 没装上货物，才考虑空船调度
            Set<String> destSet = new HashSet<>();
            for (Cargo cargo : currentPort.cargoList) {
                destSet.add(cargo.destinationPort);
            }
            if (!destSet.isEmpty()) {
                String nextDest = destSet.stream().skip(new Random().nextInt(destSet.size())).findFirst().orElse(ship.currentDestination);
                Map<String, Integer> timeMap = system.routeGraph.shortestPath(ship.currentDestination, "time", ship);
                int hours = timeMap.getOrDefault(nextDest, 120);
                ship.currentDestination = nextDest;
                ship.estimatedArrival = LocalDateTime.of(currentDate, java.time.LocalTime.of(0,0)).plusHours(hours);
                System.out.println("[调度] " + ship.name + " 规划下一目的港: " + nextDest + ", 预计到达: " + ship.estimatedArrival);
            } else {
                // 没有货物，停留在港口
                ship.estimatedArrival = null;
                System.out.println("[调度] " + ship.name + " 暂无货物，停留在 " + ship.currentDestination);
            }
        }
    }

    // 全排列工具
    private static void permute(List<String> arr, int k, List<List<String>> result) {
        if (k == arr.size()) {
            result.add(new ArrayList<>(arr));
        } else {
            for (int i = k; i < arr.size(); i++) {
                Collections.swap(arr, i, k);
                permute(arr, k + 1, result);
                Collections.swap(arr, i, k);
            }
        }
    }

    static class ShipLoadingSystem {
        List<Ship> ships = new ArrayList<>();
        Map<String, Port> ports = new HashMap<>();
        RouteGraph routeGraph = new RouteGraph();

        // 添加船舶
        public void addShip(Ship ship) {
            ships.add(ship);
        }

        // 添加港口
        public void addPort(Port port) {
            ports.put(port.name, port);
        }

        // 添加航线（增加时间参数）
        public void addRoute(String port1, String port2, int distance, int time) {
            routeGraph.addRoute(port1, port2, distance, time);
        }

        // 处理船舶到达港口
        public void processShipArrival(Ship ship, String portName, boolean unloadAll) {
            Port port = ports.get(portName);
            if (port == null) return;

            // 卸载货物
            List<Cargo> unloaded = ship.unloadCargo(portName, unloadAll);
            System.out.println(ship.name + " 在 " + portName + " 卸载了 " + unloaded.size() + " 个货物");
//        if(unloaded.size() > 0){
//            System.out.println("卸载货物清单: " );
//            for (Cargo cargo : unloaded) {
//                System.out.println(cargo);
//            }
//        }

            // 如果部分卸载，获取剩余货物的目的港
            Set<String> remainingDestinations = new HashSet<>();
            for (Cargo cargo : ship.loadedCargo) {
                remainingDestinations.add(cargo.destinationPort);
            }

            if (!remainingDestinations.isEmpty()) {
                // 优先装载与剩余货物目的港相同的货物
                for (String dest : remainingDestinations) {
                    loadCargoToShip(ship, port, dest, LocalDate.now());
                }

                // 如果船还没满，装载在最短路径上的货物
                if (ship.getRemainingWeightCapacity() > 0 || ship.getRemainingTEUCapacity() > 0) {
                    loadCargoOnShortestPath(ship, port, new ArrayList<>(remainingDestinations), LocalDate.now());
                }
            } else {
                // 全部卸载，重新装载货物
                reloadShip(ship, port, LocalDate.now());
            }
            // 新增：无论部分还是全部卸货，最后都补货装满船，并统计装载数量
            reloadShip(ship, port, LocalDate.now());
        }

        // 装载指定目的港的货物
        private void loadCargoToShip(Ship ship, Port port, String destination, LocalDate currentDate) {
            List<Cargo> availableCargo = port.getCargoForDestination(destination);
            for (Cargo cargo : availableCargo) {
                if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                if (ship.addCargo(cargo)) {
                    port.removeCargo(cargo);
//                System.out.println("装载货物: " + cargo);
                }
                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) {
                    break;
                }
            }
        }

        // 装载在最短路径上的货物（以最短时间为主）
        private void loadCargoOnShortestPath(Ship ship, Port port, List<String> destinations, LocalDate currentDate) {
            // 计算到所有目的港的最短时间路径
            Map<String, Map<String, Integer>> timeToMainDest = new HashMap<>();
            for (String dest : destinations) {
                Map<String, Integer> time = routeGraph.shortestPath(dest, "time", ship);
                timeToMainDest.put(dest, time);
            }
            List<Cargo> allCargo = new ArrayList<>(port.cargoList);
            allCargo.sort(Comparator.comparing(c -> c.deadline));
            for (Cargo cargo : allCargo) {
                if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                boolean onPath = false;
                for (String dest : destinations) {
                    Map<String, Integer> time = routeGraph.shortestPath(ship.currentDestination, "time", ship);
                    if (time.containsKey(cargo.destinationPort)) {
                        onPath = true;
                        break;
                    }
                }
                if (onPath && ship.addCargo(cargo)) {
                    port.removeCargo(cargo);
//                System.out.println("装载在最短时间路径上的货物: " + cargo);
                }
                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) {
                    break;
                }
            }
        }

        // 辅助方法：将不可拆分委托分批装到多条船（贪心式装载）
        private void splitAndAssignConsignment(List<Cargo> consignment, List<Ship> ships, Port port, LocalDate currentDate) {
            List<Cargo> remaining = new ArrayList<>(consignment);
            boolean assigned = false;
            for (Ship s : ships) {
                Iterator<Cargo> it = remaining.iterator();
                while (it.hasNext()) {
                    Cargo cargo = it.next();
                    if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                    if (s.addCargo(cargo)) {
                        port.removeCargo(cargo);
//                    System.out.println("[拆分装载] " + s.name + " 装载不可拆分委托货物: " + cargo);
                        it.remove();
                    }
                }
                if (remaining.isEmpty()) {
                    assigned = true;
                    break;
                }
            }
            if (!assigned && !remaining.isEmpty()) {
                System.out.println("无法全部拆分装载不可拆分委托: " + consignment.get(0).consignmentId);
            }
        }

        // 重新装载船舶
        public void reloadShip(Ship ship, Port port, LocalDate currentDate) {
            // 添加调试信息
            System.out.println("【装载调试】" + ship.name + " 在 " + port.name + " 开始装载");
            System.out.println("【装载调试】港口货物总数: " + port.cargoList.size());
            System.out.println("【装载调试】船舶剩余载重: " + ship.getRemainingWeightCapacity() + "/" + ship.maxWeight);
            System.out.println("【装载调试】船舶剩余TEU: " + ship.getRemainingTEUCapacity() + "/" + ship.maxTEU);

            // 统计货物类型
            long onePortCount = port.cargoList.stream().filter(c -> c.isOnePort).count();
            long normalCount = port.cargoList.stream().filter(c -> !c.isOnePort).count();
            System.out.println("【装载调试】港口一港通货物: " + onePortCount + ", 普通货物: " + normalCount);

            // 统计过期货物
            long expiredCount = port.cargoList.stream().filter(c -> currentDate.isAfter(c.latestDeliveryDate)).count();
            System.out.println("【装载调试】港口过latestDeliveryDate期货物: " + expiredCount);

            // 一港通船舶特殊处理：只装同一目的港的货物
            if (ship.isOnePortShip) {
                System.out.println("【装载调试】一港通船舶装载逻辑");
                
                // 检查船上是否已有货物
                String targetDest = null;
                Map<String, List<Cargo>> onePortCargoByDest = new HashMap<>();
                
                if (!ship.loadedCargo.isEmpty()) {
                    // 如果船上已有货物，使用现有货物的目的港
                    targetDest = ship.loadedCargo.get(0).destinationPort;
                    System.out.println("【装载调试】船上已有货物，固定目的港: " + targetDest);
                } else {
                    // 如果船上没有货物，选择港口中一港通货物最多的目的港
                    for (Cargo cargo : port.cargoList) {
                        if (cargo.isOnePort && !currentDate.isAfter(cargo.latestDeliveryDate)) {
                            onePortCargoByDest.computeIfAbsent(cargo.destinationPort, k -> new ArrayList<>()).add(cargo);
                        }
                    }
                    // 选数量最多的目的港
                    int maxCount = 0;
                    for (Map.Entry<String, List<Cargo>> entry : onePortCargoByDest.entrySet()) {
                        if (entry.getValue().size() > maxCount) {
                            maxCount = entry.getValue().size();
                            targetDest = entry.getKey();
                        }
                    }
                    System.out.println("【装载调试】船上无货物，选择目的港: " + targetDest + ", 货物数量: " + maxCount);
                }
                
                // 只装去targetDest的货物
                if (targetDest != null) {
                    // 如果船上已有货物，需要重新构建onePortCargoByDest以获取当前港口中对应目的港的货物
                    if (!ship.loadedCargo.isEmpty()) {
                        onePortCargoByDest.clear();
                        for (Cargo cargo : port.cargoList) {
                            if (cargo.isOnePort && cargo.destinationPort.equals(targetDest) && !currentDate.isAfter(cargo.latestDeliveryDate)) {
                                onePortCargoByDest.computeIfAbsent(cargo.destinationPort, k -> new ArrayList<>()).add(cargo);
                            }
                        }
                    }
                    List<Cargo> cargos = onePortCargoByDest.get(targetDest);
                    // FIX: Add null check for cargos list
                    if (cargos == null || cargos.isEmpty()) {
                        System.out.println("【装载调试】港口无匹配一港通货物可供装载到固定目的港: " + targetDest);
                        return; // No cargo to load for this target destination, so skip loading
                    }
                    // 按委托分组
                    Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                    for (Cargo cargo : cargos) {
                        consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                    }
                    // 先装不可拆分委托
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (!consignment.get(0).canSplit) {
                            double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                            int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                            boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                            // 可达性校验
                            Set<String> destSet = consignment.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
                            if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                    totalTEU <= ship.getRemainingTEUCapacity() && allValid &&
                                    allDestPortsReachable(ship, port.name, destSet, this)) {
                                for (Cargo cargo : consignment) {
                                    if (ship.addCargo(cargo)) {
                                        port.removeCargo(cargo);
                                    }
                                }
                            }
                        }
                    }
                    // 再装可拆分委托
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (consignment.get(0).canSplit) {
                            for (Cargo cargo : consignment) {
                                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                                // 可达性校验
                                List<Cargo> temp = new ArrayList<>(ship.loadedCargo);
                                temp.add(cargo);
                                Set<String> destSet = temp.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
                                if (allDestPortsReachable(ship, port.name, destSet, this)) {
                                    if (ship.addCargo(cargo)) {
                                        port.removeCargo(cargo);
                                    }
                                }
                            }
                        }
                    }
                }
                // 一港通船舶装完后直接返回
                return;
            }

            // 非一港通船舶优化：全新装载逻辑
            if (!ship.isOnePortShip) {
                System.out.println("【装载调试】普通船舶装载逻辑");
                Set<String> shipDestPorts = ship.loadedCargo.stream()
                        .map(c -> c.destinationPort)
                        .collect(Collectors.toSet());

                if (!shipDestPorts.isEmpty()) {
                    System.out.println("【装载调试】船上已有货物目的港: " + shipDestPorts);
                    // 2.1 优先装载与船上目的港相同的货物
                    for (String dest : shipDestPorts) {
                        List<Cargo> sameDestCargo = port.cargoList.stream()
                                .filter(c -> c.destinationPort.equals(dest) && !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort)
                                .collect(Collectors.toList());
//                    System.out.println("【装载调试】目的港 " + dest + " 可装载货物: " + sameDestCargo.size());
                        // 按委托分组
                        Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                        for (Cargo cargo : sameDestCargo) {
                            consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                        }
                        // 先装不可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (!consignment.get(0).canSplit) {
                                double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                                int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                                boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort);
                                // 可达性校验
                                List<Cargo> temp = new ArrayList<>(ship.loadedCargo);
                                temp.addAll(consignment);
                                Set<String> destSet = temp.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
                                if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                        totalTEU <= ship.getRemainingTEUCapacity() && allValid &&
                                        allDestPortsReachable(ship, port.name, destSet, this)) {
                                    for (Cargo cargo : consignment) {
                                        if (!cargo.isOnePort && ship.addCargo(cargo)) {
                                            port.removeCargo(cargo);
                                        }
                                    }
                                }
                            }
                        }
                        // 再装可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (consignment.get(0).canSplit) {
                                for (Cargo cargo : consignment) {
                                    if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                                    // 可达性校验
                                    List<Cargo> temp = new ArrayList<>(ship.loadedCargo);
                                    temp.add(cargo);
                                    Set<String> destSet = temp.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
                                    if (!cargo.isOnePort && allDestPortsReachable(ship, port.name, destSet, this)) {
                                        if (ship.addCargo(cargo)) {
                                            port.removeCargo(cargo);
                                        }
                                    }
                                }
                            }
                        }
                        if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) return;
                    }
                    // 2.2 装载最短路径中转港上的货物
                    List<Cargo> portCargoList = new ArrayList<>(port.cargoList);
                    for (Cargo cargo : portCargoList) {
                        if (currentDate.isAfter(cargo.latestDeliveryDate) || cargo.isOnePort) continue;
                        List<String> path = routeGraph.getShortestPathSequence(ship.currentDestination, cargo.destinationPort, ship);
                        if (path == null || path.isEmpty()) continue;
                        // 检查路径中是否包含船上已有货物的目的港（中转港）
                        boolean canLoad = false;
                        for (String midPort : path) {
                            if (shipDestPorts.contains(midPort)) {
                                canLoad = true;
                                break;
                            }
                        }
                        if (canLoad) {
                            // 按委托分组
                            Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                            consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                            for (List<Cargo> consignment : consignmentMap.values()) {
                                if (!consignment.get(0).canSplit) {
                                    double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                                    int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                                    boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort);
                                    List<Cargo> temp = new ArrayList<>(ship.loadedCargo);
                                    temp.addAll(consignment);
                                    Set<String> destSet = temp.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
                                    if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                            totalTEU <= ship.getRemainingTEUCapacity() && allValid &&
                                            allDestPortsReachable(ship, port.name, destSet, this)) {
                                        for (Cargo c : consignment) {
                                            if (!c.isOnePort && ship.addCargo(c)) {
                                                port.removeCargo(c);
                                            }
                                        }
                                    }
                                } else {
                                    for (Cargo c : consignment) {
                                        if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0)
                                            break;
                                        List<Cargo> temp = new ArrayList<>(ship.loadedCargo);
                                        temp.add(c);
                                        Set<String> destSet = temp.stream().map(cc -> cc.destinationPort).collect(Collectors.toSet());
                                        if (!c.isOnePort && allDestPortsReachable(ship, port.name, destSet, this)) {
                                            if (ship.addCargo(c)) {
                                                port.removeCargo(c);
                                            }
                                        }
                                    }
                                }
                            }
                            if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) return;
                        }
                    }
                } else {
                    System.out.println("【装载调试】船上无货物，开始初始装载");
                    // 船上没有货物时，优先装载货物最多的目的港
                    Map<String, List<Cargo>> cargoByDest = new HashMap<>();
                    for (Cargo cargo : port.cargoList) {
                        if (!currentDate.isAfter(cargo.latestDeliveryDate) && !cargo.isOnePort) {
                            cargoByDest.computeIfAbsent(cargo.destinationPort, k -> new ArrayList<>()).add(cargo);
                        }
                    }
                    // 按目的港货物数量排序，优先装载
                    List<Map.Entry<String, List<Cargo>>> sortedDestinations = cargoByDest.entrySet().stream()
                            .sorted((a, b) -> Integer.compare(b.getValue().size(), a.getValue().size()))
                            .collect(Collectors.toList());

                    for (Map.Entry<String, List<Cargo>> entry : sortedDestinations) {
                        String dest = entry.getKey();
                        List<Cargo> cargos = entry.getValue();
                        // 按委托分组
                        Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                        for (Cargo cargo : cargos) {
                            consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                        }

                        // 先装不可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (!consignment.get(0).canSplit) {
                                double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                                int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                                boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort);
                                List<Cargo> temp = new ArrayList<>(ship.loadedCargo);
                                temp.addAll(consignment);
                                Set<String> destSet = temp.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
                                if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                        totalTEU <= ship.getRemainingTEUCapacity() && allValid &&
                                        allDestPortsReachable(ship, port.name, destSet, this)) {
                                    for (Cargo cargo : consignment) {
                                        if (!cargo.isOnePort && ship.addCargo(cargo)) {
                                            port.removeCargo(cargo);
                                        }
                                    }
                                }
                            }
                        }

                        // 再装可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (consignment.get(0).canSplit) {
                                for (Cargo cargo : consignment) {
                                    if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                                    List<Cargo> temp = new ArrayList<>(ship.loadedCargo);
                                    temp.add(cargo);
                                    Set<String> destSet = temp.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
                                    if (!cargo.isOnePort && allDestPortsReachable(ship, port.name, destSet, this)) {
                                        if (ship.addCargo(cargo)) {
                                            port.removeCargo(cargo);
                                        }
                                    }
                                }
                            }
                        }

                        if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                    }
                }

                System.out.println("【装载调试】装载完成，船上货物: " + ship.loadedCargo.size() + " 个");
                return;
            }
        }

        /**
         * 多船多路径智能分配装载货物
         * @param shipsAtPort 当前港口所有待装载的船舶
         * @param port 当前港口
         * @param currentDate 当前日期
         */
        public void reloadShipsMultiPath(List<Ship> shipsAtPort, Port port, LocalDate currentDate) {
            // 1. 统计每条船的所有可达目的港的最短路径节点集合
            Map<Ship, Set<String>> shipPathMap = new HashMap<>();
            Map<String, Integer> nodeCount = new HashMap<>();
            for (Ship ship : shipsAtPort) {
                Set<String> pathNodes = new HashSet<>();
                for (Cargo cargo : port.cargoList) {
                    if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                    List<String> path = routeGraph.getShortestPathSequence(ship.currentDestination, cargo.destinationPort, ship);
                    if (path != null) pathNodes.addAll(path);
                }
                shipPathMap.put(ship, pathNodes);
                for (String node : pathNodes) {
                    nodeCount.put(node, nodeCount.getOrDefault(node, 0) + 1);
                }
            }
            // 2. 每条船优先装载独占路径节点上的货物
            for (Ship ship : shipsAtPort) {
                Set<String> uniqueNodes = shipPathMap.get(ship).stream()
                        .filter(node -> nodeCount.get(node) == 1)
                        .collect(Collectors.toSet());
                // 装载 uniqueNodes 上的货物
                List<Cargo> cargos = port.cargoList.stream()
                        .filter(c -> uniqueNodes.contains(c.destinationPort) && !currentDate.isAfter(c.latestDeliveryDate))
                        .collect(Collectors.toList());
                // 按委托分组，先装不可拆分，再装可拆分
                Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                for (Cargo cargo : cargos) {
                    consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                }
                // 不可拆分
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (!consignment.get(0).canSplit) {
                        double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                        int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                        boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                        if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                            for (Cargo cargo : consignment) {
                                if (ship.addCargo(cargo)) {
                                    port.removeCargo(cargo);
                                }
                            }
                        }
                    }
                }
                // 可拆分
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (consignment.get(0).canSplit) {
                        for (Cargo cargo : consignment) {
                            if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                            if (ship.addCargo(cargo)) {
                                port.removeCargo(cargo);
                            }
                        }
                    }
                }
            }
            // 3. 再装载多船都经过的路径节点上的货物
            for (Ship ship : shipsAtPort) {
                Set<String> sharedNodes = shipPathMap.get(ship).stream()
                        .filter(node -> nodeCount.get(node) > 1)
                        .collect(Collectors.toSet());
                List<Cargo> cargos = port.cargoList.stream()
                        .filter(c -> sharedNodes.contains(c.destinationPort) && !currentDate.isAfter(c.latestDeliveryDate))
                        .collect(Collectors.toList());
                Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                for (Cargo cargo : cargos) {
                    consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                }
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (!consignment.get(0).canSplit) {
                        double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                        int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                        boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                        if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                            for (Cargo cargo : consignment) {
                                if (ship.addCargo(cargo)) {
                                    port.removeCargo(cargo);
                                }
                            }
                        }
                    }
                }
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (consignment.get(0).canSplit) {
                        for (Cargo cargo : consignment) {
                            if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                            if (ship.addCargo(cargo)) {
                                port.removeCargo(cargo);
                            }
                        }
                    }
                }
            }
            // 4. 最后补货装满
            for (Ship ship : shipsAtPort) {
                if (ship.getRemainingWeightCapacity() > 0 && ship.getRemainingTEUCapacity() > 0) {
                    // 只要还有剩余运力，随便装
                    List<Cargo> cargos = port.cargoList.stream()
                            .filter(c -> !currentDate.isAfter(c.latestDeliveryDate))
                            .collect(Collectors.toList());
                    Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                    for (Cargo cargo : cargos) {
                        consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                    }
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (!consignment.get(0).canSplit) {
                            double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                            int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                            boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                            if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                    totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                                for (Cargo cargo : consignment) {
                                    if (ship.addCargo(cargo)) {
                                        port.removeCargo(cargo);
                                    }
                                }
                            }
                        }
                    }
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (consignment.get(0).canSplit) {
                            for (Cargo cargo : consignment) {
                                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                                if (ship.addCargo(cargo)) {
                                    port.removeCargo(cargo);
                                }
                            }
                        }
                    }
                }
            }
        }

        /**
         * 计算船上所有货物的最优卸货顺序（经过所有目的港，总时间最短）
         * 并输出每段的时间和总时间
         */
        public void printBestUnloadOrder(Ship ship, String startPort, LocalDateTime startTime) {
            // 1. 收集所有目的港
            Set<String> destPorts = ship.loadedCargo.stream()
                    .map(c -> c.destinationPort)
                    .collect(Collectors.toSet());
            if (destPorts.isEmpty()) {
                System.out.println("【航线规划】船上无货物，无需规划航线");
                return;
            }
            List<String> destList = destPorts != null ? new ArrayList<>(destPorts) : new ArrayList<>();

            List<String> bestOrder = null;
            int minTotalHours = Integer.MAX_VALUE;
            List<Integer> bestSegmentHours = null;
            List<WaterwayGroup> bestWaterwayGroups = new ArrayList<>();

            // 2. 全排列
            List<List<String>> allOrders = new ArrayList<>();
            permute(destList, 0, allOrders);

            for (List<String> order : allOrders) {
                String curPort = startPort;
                int orderTotalHours = 0;
                boolean allOnTime = true;
                List<Integer> segmentHours = new ArrayList<>();
                List<WaterwayGroup> waterwayGroups = new ArrayList<>();
                for (String dest : order) {
                    Integer travelHours = routeGraph.shortestPath(curPort, "time", ship).get(dest);
                    if (travelHours == null || travelHours == Integer.MAX_VALUE) { allOnTime = false; break; }
                    // 获取最优水道组
                    WaterwayGroup bestWaterwayGroup = routeGraph.getBestWaterwayGroup(curPort, dest, ship, "time");
                    if (bestWaterwayGroup != null) {
                        waterwayGroups.add(bestWaterwayGroup);
                    }
                    segmentHours.add(travelHours);
                    orderTotalHours += travelHours;
                    // 可加 deadline 检查
                    curPort = dest;
                }
                if (allOnTime && orderTotalHours < minTotalHours) {
                    minTotalHours = orderTotalHours;
                    bestOrder = order != null ? new ArrayList<>(order) : new ArrayList<>();
                    bestSegmentHours = segmentHours != null ? new ArrayList<>(segmentHours) : new ArrayList<>();
                    bestWaterwayGroups = new ArrayList<>(waterwayGroups);
                }
            }
            if (bestOrder == null) {
                System.out.println("【航线规划】未找到可行的卸货顺序");
                return;
            }
            // 生成完整建议航线（含中转港口）
            List<String> fullRoute = new ArrayList<>();
            String curPort = startPort;
            for (String dest : bestOrder) {
                List<String> segment = routeGraph.getShortestPathSequence(curPort, dest, ship);
                if (segment != null && !segment.isEmpty()) {
                    if (fullRoute.isEmpty()) {
                        fullRoute.addAll(segment);
                    } else {
                        // 避免重复添加起点
                        fullRoute.addAll(segment.subList(1, segment.size()));
                    }
                    curPort = dest;
                }
            }
            System.out.println("【航线规划】建议航线: " + fullRoute);

            // 计算每个目的港的预计到达（卸货完成）时间
            List<String> portArriveInfo = new ArrayList<>();
            String lastPort = startPort;
            LocalDateTime t = startTime;
            // 复制一份货物清单用于模拟
            List<Cargo> cargoSim = ship.loadedCargo != null ? new ArrayList<>(ship.loadedCargo) : new ArrayList<>();
            for (String dest : bestOrder) {
                Integer travelHours = routeGraph.shortestPath(lastPort, "time", ship).get(dest);
                if (travelHours == null || travelHours == Integer.MAX_VALUE) {
                    portArriveInfo.add(dest + ": 不可达");
                    continue;
                }
                t = t.plusHours(travelHours);
                // 统计要卸货的货物
                List<Cargo> toUnload = new ArrayList<>();
                for (Cargo c : cargoSim) {
                    if (c.destinationPort.equals(dest)) {
                        toUnload.add(c);
                    }
                }
                int unloadCount = toUnload.size();
                double unloadWeight = toUnload.stream().mapToDouble(c -> c.weight).sum();
                // 卸货作业时间
                double hourByBox = unloadCount * 1/20.0;
                double hourByWeight = unloadWeight * 1/20.0;
                int operationHours = (int)Math.ceil(Math.min(hourByBox, hourByWeight));
                if (operationHours < 1) operationHours = 1;
                t = t.plusHours(operationHours);
                portArriveInfo.add(dest + ": " + t);
                // 卸货
                cargoSim.removeIf(c -> c.destinationPort.equals(dest));
                lastPort = dest;
            }
            System.out.println("【航线规划】每段到达及卸货完成时间: " + portArriveInfo);
            System.out.println("【航线规划】建议卸货顺序: " + bestOrder);
            System.out.println("【航线规划】每段航行时间: " + bestSegmentHours);
            System.out.println("【航线规划】使用的水道组: " + bestWaterwayGroups.stream().map(wg -> wg.name).collect(Collectors.toList()));
            System.out.println("【航线规划】总航行时间: " + minTotalHours + " 小时");
        }

        // 工具：全排列
        private void permute(List<String> arr, int k, List<List<String>> result) {
            if (k == arr.size()) {
                result.add(new ArrayList<>(arr));
            } else {
                for (int i = k; i < arr.size(); i++) {
                    Collections.swap(arr, i, k);
                    permute(arr, k + 1, result);
                    Collections.swap(arr, i, k);
                }
            }
        }

        // 打印系统状态
        public void printSystemStatus() {
            System.out.println("\n===== 船舶状态 =====");

            Map<String, List<Cargo>> shipCargoMap = new HashMap<>();

            for (Ship ship : ships) {
                System.out.println(ship);
                System.out.println("  装载货物: " + ship.loadedCargo.size() + " 个");

                List<Cargo> cargos = ship.loadedCargo;
//            System.out.println("装载货物清单:" + JSONObject.toJSONString(cargos));
                List<String> target = cargos.stream().map(Cargo::getDestinationPort).distinct().collect(Collectors.toList());
                System.out.println("装载货物目的港:" + target);
                shipCargoMap.put(ship.name, ship.loadedCargo);
            }

            Map<String, List<Cargo>> portCargoMap = new HashMap<>();

            System.out.println("\n===== 港口货物 =====");
            for (Port port : ports.values()) {
                System.out.println("港口 " + port.name + ": " + port.cargoList.size() + " 个货物");
//            System.out.println("  货物清单:");
//            for (Cargo cargo : port.cargoList) {
//                System.out.println(cargo);
//            }
                List<Cargo> cargos = port.cargoList;

                List<Cargo> isOnePort = cargos.stream().filter(cargo -> cargo.isOnePort).collect(Collectors.toList());

                System.out.println("一港通货物数量:" + isOnePort.size());

//            System.out.println("一港通货物清单:" + JSONObject.toJSONString(isOnePort));

                List<String> start = cargos.stream().map(cargo -> cargo.originPort).distinct().collect(Collectors.toList());

//            System.out.println("起始港:" + JSONObject.toJSONString(start));

                List<String> target = cargos.stream().map(cargo -> cargo.destinationPort).distinct().collect(Collectors.toList());

//            System.out.println("目的港:" + JSONObject.toJSONString(target));

                //统计每个目的港的货物数量
                Map<String, Long> countByTarget = cargos.stream()
                        .collect(Collectors.groupingBy(Cargo::getDestinationPort, Collectors.counting()));
                System.out.println("目的港货物数量:" + countByTarget);

//            for (String portName:countByTarget.keySet()) {
//
//                //统计每个目的港的货物清单
//                List<Cargo> targetCargos = cargos.stream().filter(cargo -> cargo.destinationPort.equals(portName)).collect(Collectors.toList());
//                System.out.println("目的港口:" + portName);
//                for (Cargo cargo:targetCargos
//                     ) {
//                    System.out.println(cargo);
//                }
//
//            }

                portCargoMap.put(port.name, port.cargoList);
            }

        }

        // 工具：判断当前船舶从当前港口能否依次到达所有目的港（顺序无关，只要都能到达即可）
        private boolean allDestPortsReachable(Ship ship, String currentPort, Collection<String> destPorts, ShipLoadingSystem system) {
            for (String dest : destPorts) {
                Integer travelHours = system.routeGraph.shortestPath(currentPort, "time", ship).get(dest);
                if (travelHours == null || travelHours == Integer.MAX_VALUE) {
                    return false;
                }
            }
            return true;
        }
        
        /**
         * 演示坐标和水道自动拼路线功能
         */
        public void demonstrateRouteBuilding() {
            System.out.println("\n===== 坐标和水道自动拼路线演示 =====");
            
            // 创建一些带坐标的水道
            List<Waterway> waterways = new ArrayList<>();
            
            // 水道1：虎门 -> 容奇
            Waterway waterway1 = new Waterway("虎门-容奇水道", 300.0, 40.0, 40.0, 12.0, 1000, 10);
            waterway1.addCoordinate(22.8234, 113.6789, "虎门");
            waterway1.addCoordinate(22.8234, 113.6889); // 中间点
            waterway1.addCoordinate(22.8234, 113.6989, "容奇");
            waterways.add(waterway1);
            
            // 水道2：容奇 -> 清远
            Waterway waterway2 = new Waterway("容奇-清远水道", 300.0, 40.0, 40.0, 12.0, 2000, 20);
            waterway2.addCoordinate(22.8234, 113.6989, "容奇");
            waterway2.addCoordinate(23.1234, 113.6989); // 中间点
            waterway2.addCoordinate(23.4234, 113.6989, "清远");
            waterways.add(waterway2);
            
            // 水道3：虎门 -> 北滘（中船水道）
            Waterway waterway3 = new Waterway("虎门-北滘水道", 400.0, 60.0, 50.0, 16.0, 1500, 15);
            waterway3.addCoordinate(22.8234, 113.6789, "虎门");
            waterway3.addCoordinate(22.8234, 113.6889); // 中间点
            waterway3.addCoordinate(22.8234, 113.7089, "北滘");
            waterways.add(waterway3);
            
            // 水道4：北滘 -> 清远（中船水道）
            Waterway waterway4 = new Waterway("北滘-清远水道", 400.0, 60.0, 50.0, 16.0, 2500, 25);
            waterway4.addCoordinate(22.8234, 113.7089, "北滘");
            waterway4.addCoordinate(23.1234, 113.7089); // 中间点
            waterway4.addCoordinate(23.4234, 113.7089, "清远");
            waterways.add(waterway4);
            
            // 水道5：虎门 -> 黄埔（大船水道）
            Waterway waterway5 = new Waterway("虎门-黄埔水道", 500.0, 80.0, 60.0, 20.0, 3000, 30);
            waterway5.addCoordinate(22.8234, 113.6789, "虎门");
            waterway5.addCoordinate(23.0234, 113.6789); // 中间点
            waterway5.addCoordinate(23.2234, 113.6789, "黄埔");
            waterways.add(waterway5);

            Waterway waterway6 = new Waterway("黄埔-清远水道", 500.0, 80.0, 60.0, 20.0, 3500, 35);
            waterway6.addCoordinate(23.2234, 113.6789, "黄埔");

            System.out.println("创建的水道列表:");
            for (Waterway waterway : waterways) {
                System.out.println("  " + waterway);
                System.out.println("    起运港: " + waterway.getOriginCoordinate());
                System.out.println("    目的港: " + waterway.getDestinationCoordinate());
            }
            
            // 创建路线构建器
            RouteBuilder routeBuilder = new RouteBuilder(waterways);
            
            // 测试自动拼路线
            System.out.println("\n自动拼路线测试:");
            
            // 测试1：虎门 -> 清远（需要经过容奇）
            Route route1 = routeBuilder.buildRoute("虎门", "清远");
            if (route1 != null) {
                System.out.println("虎门 -> 清远: " + route1);
                System.out.println("  连通性: " + route1.isConnected());
                System.out.println("  总距离: " + route1.getTotalDistance());
                System.out.println("  总时间: " + route1.getTotalTime());
            } else {
                System.out.println("虎门 -> 清远: 无法拼出路线");
            }
            
            // 测试2：虎门 -> 北滘（直达）
            Route route2 = routeBuilder.buildRoute("虎门", "北滘");
            if (route2 != null) {
                System.out.println("虎门 -> 北滘: " + route2);
                System.out.println("  连通性: " + route2.isConnected());
            } else {
                System.out.println("虎门 -> 北滘: 无法拼出路线");
            }
            
            // 测试3：查找所有可能的路线
            System.out.println("\n查找所有可能的路线:");
            List<Route> allRoutes = routeBuilder.buildAllPossibleRoutes("虎门", "清远");
            for (int i = 0; i < allRoutes.size(); i++) {
                Route route = allRoutes.get(i);
                System.out.println("  路线" + (i+1) + ": " + route);
            }
            
            // 测试船舶通过性
            System.out.println("\n船舶通过性测试:");
            Map<String, Double> cargoLimits = new HashMap<>();
            cargoLimits.put("General", 40000.0);
            
            Ship smallShip = new Ship("小型船舶", 300.0, 40.0, 40.0, 12.0, 30000.0, 8000, 
                "虎门", LocalDateTime.now(), cargoLimits, false);
            Ship mediumShip = new Ship("中型船舶", 400.0, 60.0, 50.0, 16.0, 60000.0, 15000, 
                "虎门", LocalDateTime.now(), cargoLimits, false);
            Ship largeShip = new Ship("大型船舶", 500.0, 80.0, 60.0, 20.0, 80000.0, 20000, 
                "虎门", LocalDateTime.now(), cargoLimits, false);
            
            for (Route route : allRoutes) {
                System.out.println("路线: " + route.originPort + " -> " + route.destinationPort);
                System.out.println("  小型船舶可通过: " + route.canShipPass(smallShip));
                System.out.println("  中型船舶可通过: " + route.canShipPass(mediumShip));
                System.out.println("  大型船舶可通过: " + route.canShipPass(largeShip));
            }
        }

        // 演示物理连通的水道组功能
        public void demonstratePhysicallyConnectedWaterwayGroups() {
            System.out.println("\n===== 物理连通水道组演示 =====");
            
            // 创建一些示例水道，每个水道只有两个坐标点
            List<Waterway> connectedWaterways = new ArrayList<>();
            
            // 连通的水道组：虎门 -> 黄埔 -> 清远 -> 北滘
            Waterway w1 = new Waterway("虎门-黄埔水道", 400, 60, 50, 16, 100, 20);
            w1.addCoordinate(22.8, 113.6, "虎门");
            w1.addCoordinate(23.1, 113.4, "黄埔");
            connectedWaterways.add(w1);
            
            Waterway w2 = new Waterway("黄埔-清远水道", 400, 60, 50, 16, 150, 30);
            w2.addCoordinate(23.1, 113.4, "黄埔");
            w2.addCoordinate(23.7, 113.0, "清远");
            connectedWaterways.add(w2);
            
            Waterway w3 = new Waterway("清远-北滘水道", 400, 60, 50, 16, 150, 30);
            w3.addCoordinate(23.7, 113.0, "清远");
            w3.addCoordinate(22.9, 113.2, "北滘");
            connectedWaterways.add(w3);
            
            // 创建物理连通的水道组
            WaterwayGroup connectedGroup = new WaterwayGroup("连通水道组", connectedWaterways);
            System.out.println("连通水道组: " + connectedGroup);
            
            // 创建不连通的水道组（坐标不匹配）
            List<Waterway> disconnectedWaterways = new ArrayList<>();
            Waterway w4 = new Waterway("虎门-黄埔水道", 400, 60, 50, 16, 100, 20);
            w4.addCoordinate(22.8, 113.6, "虎门");
            w4.addCoordinate(23.1, 113.4, "黄埔");
            disconnectedWaterways.add(w4);
            
            Waterway w5 = new Waterway("黄埔-容奇水道", 400, 60, 50, 16, 150, 30);
            w5.addCoordinate(23.1, 113.4, "黄埔");
            w5.addCoordinate(22.8, 113.1, "容奇"); // 这个坐标与上一个水道的终点不匹配
            disconnectedWaterways.add(w5);
            
            WaterwayGroup disconnectedGroup = new WaterwayGroup("不连通水道组", disconnectedWaterways);
            System.out.println("不连通水道组: " + disconnectedGroup);
            
            // 创建坐标点数量不正确的水道组
            List<Waterway> invalidWaterways = new ArrayList<>();
            Waterway w6 = new Waterway("无效水道", 400, 60, 50, 16, 100, 20);
            w6.addCoordinate(22.8, 113.6, "虎门");
            w6.addCoordinate(23.1, 113.4, "黄埔");
            w6.addCoordinate(23.7, 113.0, "清远"); // 三个坐标点，不符合要求
            invalidWaterways.add(w6);
            
            WaterwayGroup invalidGroup = new WaterwayGroup("无效水道组", invalidWaterways);
            System.out.println("无效水道组: " + invalidGroup);
            
            // 测试船舶通过性
            System.out.println("\n船舶通过性测试:");
            Ship ship = new Ship("测试船", 300, 45, 40, 12, 100000, 20000, "虎门", 
                    LocalDateTime.now(), new HashMap<>(), false);
            
            System.out.println("船舶通过连通水道组: " + connectedGroup.canShipPass(ship));
            System.out.println("船舶通过不连通水道组: " + disconnectedGroup.canShipPass(ship));
            System.out.println("船舶通过无效水道组: " + invalidGroup.canShipPass(ship));
            
            // 测试在路线图中使用物理连通检查
            System.out.println("\n路线图物理连通检查测试:");
            RouteGraph testGraph = new RouteGraph();
            testGraph.addWaterwayGroups("虎门", "北滘", Arrays.asList(connectedGroup, disconnectedGroup));
            
            WaterwayGroup bestGroup = testGraph.getBestWaterwayGroup("虎门", "北滘", ship, "time");
            System.out.println("最佳水道组: " + (bestGroup != null ? bestGroup.name : "无"));
        }

        public void demonstratePortCoordinatesAndRouteBuilding() {
            System.out.println("\n===== 港口坐标和自动路线构建演示 =====");
            
            // 显示所有港口的坐标信息
            System.out.println("港口坐标信息:");
            for (Port port : ports.values()) {
                System.out.println("  " + port);
            }
            
            // 显示RouteGraph中构建的路线
            System.out.println("\n自动构建的路线:");
            String[] portNames = {"虎门", "容奇", "北滘", "黄埔", "清远"};
            for (String originPort : portNames) {
                for (String destPort : portNames) {
                    if (!originPort.equals(destPort)) {
                        List<WaterwayGroup> waterwayGroups = routeGraph.graph.get(originPort) != null ? 
                            routeGraph.graph.get(originPort).get(destPort) : null;
                        if (waterwayGroups != null && !waterwayGroups.isEmpty()) {
                            System.out.println("  " + originPort + " -> " + destPort + ":");
                            for (WaterwayGroup wg : waterwayGroups) {
                                System.out.println("    " + wg);
                            }
                        }
                    }
                }
            }
            
            // 测试船舶航线规划
            System.out.println("\n船舶航线规划测试:");
            Ship testShip = ships.get(0); // 使用第一个船舶进行测试
            for (String destPort : portNames) {
                if (!testShip.currentDestination.equals(destPort)) {
                    WaterwayGroup bestGroup = routeGraph.getBestWaterwayGroup(
                        testShip.currentDestination, destPort, testShip, "time");
                    if (bestGroup != null) {
                        System.out.println("  " + testShip.name + " 从 " + testShip.currentDestination + 
                            " 到 " + destPort + " 的最佳水道组: " + bestGroup.name);
                    } else {
                        System.out.println("  " + testShip.name + " 无法从 " + testShip.currentDestination + 
                            " 到达 " + destPort);
                    }
                }
            }
        }
        
        /**
         * 检查船舶是否可以装载更多货物（考虑截止日期和容量限制）
         * @param ship 船舶
         * @param port 港口
         * @param currentDate 当前日期
         * @return 是否可以继续装货
         */
        private boolean canShipLoadMoreCargo(Ship ship, Port port, LocalDate currentDate) {
            // 检查船舶容量
            if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) {
                return false;
            }
            
            // 检查港口是否有可用货物
            boolean hasAvailableCargo = port.cargoList.stream()
                .anyMatch(cargo -> !currentDate.isAfter(cargo.latestDeliveryDate));
            
            if (!hasAvailableCargo) {
                return false;
            }
            
            // 检查船舶当前货物是否满足截止日期要求
            for (Cargo cargo : ship.loadedCargo) {
                if (currentDate.isAfter(cargo.deadline)) {
                    return false; // 已有货物超期，不能再装货
                }
            }
            
            return true;
        }
        
        /**
         * 为新生成的货物尝试装载到正在作业的船舶
         * @param ship 船舶
         * @param port 港口
         * @param currentDateTime 当前时间
         */
        private void tryLoadNewCargoToOperatingShip(Ship ship, Port port, LocalDateTime currentDateTime) {
            if (!canShipLoadMoreCargo(ship, port, currentDateTime.toLocalDate())) {
                return;
            }
            
            System.out.println("【新货物装货优化】" + ship.name + " 在 " + ship.currentDestination + " 正在作业，尝试继续装货");
            
            // 记录装货前的状态
            int beforeCargoCount = ship.loadedCargo.size();
            double beforeWeight = ship.currentCargoWeight;
            
            // 尝试继续装货
            reloadShip(ship, port, currentDateTime.toLocalDate());
            
            // 检查是否有新货物被装载
            int afterCargoCount = ship.loadedCargo.size();
            double afterWeight = ship.currentCargoWeight;
            int newCargoCount = afterCargoCount - beforeCargoCount;
            double newWeight = afterWeight - beforeWeight;
            
            if (newCargoCount > 0) {
                System.out.println("【新货物装货优化】" + ship.name + " 成功装载 " + newCargoCount + " 个新货物，新增重量: " + newWeight + " 吨");
                
                // 重新计算作业时间（因为增加了货物）
                int unloadCount = ship.loadedCargo.stream()
                    .filter(c -> c.destinationPort.equals(ship.currentDestination))
                    .mapToInt(c -> 1).sum();
                double unloadWeight = ship.loadedCargo.stream()
                    .filter(c -> c.destinationPort.equals(ship.currentDestination))
                    .mapToDouble(c -> c.weight).sum();
                
                // 计算新的作业时间
                double hourByBox = (unloadCount + newCargoCount) * 1.0/20;
                double hourByWeight = (unloadWeight + newWeight) * 1.0/20;
                int newOperationHours = (int)Math.ceil(Math.min(hourByBox, hourByWeight));
                if (newOperationHours < 1) newOperationHours = 1;
                
                // 计算原有作业的剩余时间
                long remainingHours = java.time.Duration.between(currentDateTime, ship.operationFinishTime).toHours();
                if (remainingHours < 0) remainingHours = 0;
                
                // 更新作业完成时间：原有剩余时间 + 新增作业时间
                ship.operationFinishTime = currentDateTime.plusHours(remainingHours + newOperationHours);
                System.out.println("【新货物装货优化】" + ship.name + " 新增作业时间 " + newOperationHours + " 小时，总剩余作业时间 " + (remainingHours + newOperationHours) + " 小时，完成时间: " + ship.operationFinishTime);
                
                // 重新规划航线（因为增加了新货物）
                printBestUnloadOrder(ship, ship.currentDestination, ship.operationFinishTime);
            } else {
                System.out.println("【新货物装货优化】" + ship.name + " 无法装载更多货物（容量已满或无可达航线）");
            }
        }
        
        /**
         * 演示新货物装货优化功能
         */
        public void demonstrateNewCargoLoadingOptimization() {
            System.out.println("\n===== 新货物装货优化功能演示 =====");
            
            // 模拟一个船舶正在港口作业的场景
            Ship testShip = ships.get(0); // 使用第一个船舶
            Port testPort = ports.get("虎门");
            
            if (testShip != null && testPort != null) {
                System.out.println("演示船舶: " + testShip.name);
                System.out.println("演示港口: " + testPort.name);
                System.out.println("船舶当前容量: " + testShip.getRemainingWeightCapacity() + "/" + testShip.maxWeight + " 吨");
                System.out.println("船舶当前TEU: " + testShip.getRemainingTEUCapacity() + "/" + testShip.maxTEU);
                
                // 模拟船舶正在作业
                LocalDateTime currentTime = LocalDateTime.now();
                testShip.operationFinishTime = currentTime.plusHours(5);
                System.out.println("船舶作业完成时间: " + testShip.operationFinishTime);
                
                // 检查是否可以装载更多货物
                boolean canLoad = canShipLoadMoreCargo(testShip, testPort, currentTime.toLocalDate());
                System.out.println("船舶是否可以装载更多货物: " + canLoad);
                
                if (canLoad) {
                    System.out.println("开始尝试装载新货物...");
                    tryLoadNewCargoToOperatingShip(testShip, testPort, currentTime);
                } else {
                    System.out.println("船舶无法装载更多货物（容量已满或无可达航线）");
                }
            }
            
            System.out.println("===== 新货物装货优化功能演示完成 =====\n");
        }
    }


    public static void main(String[] args) {
        ShipLoadingSystem system = new ShipLoadingSystem();
        
        // 创建带坐标的港口
        Port HuMen = new Port("虎门", 22.8, 113.6);
        Port RongQi = new Port("容奇", 22.8, 113.1);
        Port BeiJiao = new Port("北滘", 22.9, 113.2);
        Port HuangPu = new Port("黄埔", 24.1, 113.9);
        Port QingYuan = new Port("清远", 23.7, 113.0);

        system.addPort(HuMen);
        system.addPort(RongQi);
        system.addPort(BeiJiao);
        system.addPort(HuangPu);
        system.addPort(QingYuan);

        // 创建所有水道（包含船舶尺寸限制和坐标点）
        List<Waterway> allWaterways = new ArrayList<>();
        
        // 大船水道组（适合大型船舶）
        Waterway largeWaterway1 = new Waterway("虎门-东江桥", 500.0, 80.0, 60.0, 20.0, 3000, 20);
        largeWaterway1.addCoordinate(22.8, 113.6, "虎门");
        largeWaterway1.addCoordinate(23.1, 113.4, "东江桥");
        allWaterways.add(largeWaterway1);

        Waterway largeWaterway3 = new Waterway("东江桥-黄埔",500,80,60,20,3000,20);
        largeWaterway3.addCoordinate(23.1, 113.4, "东江桥");
        largeWaterway3.addCoordinate(24.1, 113.9, "黄埔");
        allWaterways.add(largeWaterway3);

        //小船
        Waterway smallWaterway3 = new Waterway("虎门-乌冲",300.0, 40.0, 40.0, 12.0, 3000, 20);
        smallWaterway3.addCoordinate(22.8, 113.6, "虎门");
        smallWaterway3.addCoordinate(22.8, 113.1, "乌冲");
        allWaterways.add(smallWaterway3);

        Waterway smallWaterway4 = new Waterway("乌冲-黄埔",300.0, 40.0, 40.0, 12.0, 6100, 30);
        smallWaterway4.addCoordinate(22.8, 113.1, "乌冲");
        smallWaterway4.addCoordinate(24.1, 113.9, "黄埔");

        //大船
        Waterway largeWaterway2 = new Waterway("黄埔-清远", 500.0, 80.0, 60.0, 20.0, 6100, 30);
        largeWaterway2.addCoordinate(24.1, 113.9, "黄埔");
        largeWaterway2.addCoordinate(23.7, 113.0, "清远");
        allWaterways.add(largeWaterway2);
        
        // 中船水道组（适合中型船舶）
        Waterway mediumWaterway1 = new Waterway("中船水道", 400.0, 60.0, 50.0, 16.0, 3000, 20);
        mediumWaterway1.addCoordinate(22.8, 113.6, "虎门");
        mediumWaterway1.addCoordinate(22.9, 113.2, "北滘");
        allWaterways.add(mediumWaterway1);
        
        Waterway mediumWaterway2 = new Waterway("中船水道", 400.0, 60.0, 50.0, 16.0, 6100, 30);
        mediumWaterway2.addCoordinate(22.9, 113.2, "北滘");
        mediumWaterway2.addCoordinate(23.7, 113.0, "清远");
        allWaterways.add(mediumWaterway2);


        Waterway mediumWaterway3 = new Waterway("中船水道", 400.0, 60.0, 50.0, 16.0, 6100, 30);
        mediumWaterway3.addCoordinate(22.8, 113.6, "虎门");
        mediumWaterway3.addCoordinate(22.8, 113.1, "容奇");
        allWaterways.add(mediumWaterway3);

        Waterway mediumWaterway4 =  new Waterway("中船水道", 400.0, 60.0, 50.0, 16.0, 6100, 30);
        mediumWaterway4.addCoordinate(22.8, 113.1, "容奇");
        mediumWaterway4.addCoordinate(23.7, 113.0, "清远");
        allWaterways.add(mediumWaterway4);

        // 小船水道组（适合小型船舶）
        Waterway smallWaterway1 = new Waterway("小船水道", 300.0, 40.0, 40.0, 12.0, 3000, 20);
        smallWaterway1.addCoordinate(22.8, 113.6, "虎门");
        smallWaterway1.addCoordinate(22.8, 113.1, "容奇");
        allWaterways.add(smallWaterway1);
        
        Waterway smallWaterway2 = new Waterway("小船水道", 300.0, 40.0, 40.0, 12.0, 6100, 30);
        smallWaterway2.addCoordinate(22.8, 113.1, "容奇");
        smallWaterway2.addCoordinate(23.7, 113.0, "清远");
        allWaterways.add(smallWaterway2);

        // 使用RouteBuilder自动构建路线
        RouteBuilder routeBuilder = new RouteBuilder(allWaterways);
        
        // 定义所有港口对
        String[] portNames = {"虎门", "容奇", "北滘", "黄埔", "清远"};
        
        // 为每个港口对构建路线并添加到RouteGraph
        for (String originPort : portNames) {
            for (String destPort : portNames) {
                if (!originPort.equals(destPort)) {
                    List<Route> routes = routeBuilder.buildAllPossibleRoutes(originPort, destPort);
                    if (!routes.isEmpty()) {
                        List<WaterwayGroup> waterwayGroups = new ArrayList<>();
                        for (Route route : routes) {
                            // 将Route转换为WaterwayGroup
                            WaterwayGroup waterwayGroup = new WaterwayGroup(
                                "自动构建路线_" + originPort + "_" + destPort + "_" + waterwayGroups.size(),
                                route.waterways
                            );
                            waterwayGroups.add(waterwayGroup);
                        }
                        system.routeGraph.addWaterwayGroups(originPort, destPort, waterwayGroups);
                    }
                }
            }
        }

        Map<String, Double> cargoLimits1 = new HashMap<>();
        cargoLimits1.put("General", 40000.00);
        cargoLimits1.put("Refrigerated", 30000.0);
        cargoLimits1.put("Dangerous", 10000.0);
        Ship ship1 = new Ship("一港通中船", 400.0, 60, 50.0, 16.0, 80000.0, 20000, "虎门", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, true);
        Ship ship2 = new Ship("容奇中船", 400.0, 60, 50.0, 16.0, 80000.0, 20000, "容奇", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship3 = new Ship("清远大船", 500.0, 70, 50.0, 16.0, 180000.0, 42000, "清远", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship4 = new Ship("虎门小船", 300.0, 30, 40, 12, 40000.0, 10000, "虎门", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship5 = new Ship("北滘小船", 300.0, 30, 40, 12, 40000.0, 10000, "北滘", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);


        system.addShip(ship1);
        system.addShip(ship2);
        system.addShip(ship3);
        system.addShip(ship4);
        system.addShip(ship5);

        // 小时级主循环
        LocalDateTime currentDateTime = LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0));

        for (Port port : system.ports.values()) {
            for (int i = 0; i < 300; i++) {
                generateRandomCargo(port, currentDateTime.toLocalDate().minusDays(1));
            }
        }

        int totalHours = 24 * 30; // 30天
        for (int hour = 0; hour < totalHours; hour++) {
            System.out.println("\n==== 当前时间: " + currentDateTime + " ====\n");
            // 1. 到港且未作业的船，开始装卸
            for (Ship ship : system.ships) {
                if (ship.estimatedArrival != null && !ship.estimatedArrival.isAfter(currentDateTime) && ship.operationFinishTime == null) {
                    // 卸货
                    List<Cargo> unloaded = ship.unloadCargo(ship.currentDestination, true);
                    System.out.println(ship.name + "卸货数量：" + unloaded.size());
                    // 调用智能配载
                    system.reloadShip(ship, system.ports.get(ship.currentDestination), currentDateTime.toLocalDate());
                    System.out.println(ship.name + "装载货物数量：" + ship.loadedCargo.size());
                    if (!ship.loadedCargo.isEmpty()) {
//                        System.out.println(ship.name + "装货清单:");
//                        for (Cargo cargo : ship.loadedCargo) {
//                            System.out.println("  " + cargo);
//                        }
                        List<Cargo> loaded = ship.loadedCargo;



                    }
                    // 计算本次实际装卸的箱数/重量
                    int unloadCount = unloaded.size();
                    double unloadWeight = unloaded.stream().mapToDouble(c -> c.weight).sum();
                    int loadCount = ship.loadedCargo.size();
                    double loadWeight = ship.loadedCargo.stream().mapToDouble(c -> c.weight).sum();
                    int totalCount = unloadCount + loadCount;
                    double totalWeight = unloadWeight + loadWeight;
                    double hourByBox = totalCount * 1/20;
                    double hourByWeight = totalWeight * 1/20;
                    int operationHours = (int)Math.ceil(Math.min(hourByBox, hourByWeight));
                    if (operationHours < 1) operationHours = 1;
                    ship.operationFinishTime = currentDateTime.plusHours(operationHours);
                    System.out.println("[作业] " + ship.name + " 在 " + ship.currentDestination + " 开始装卸作业，预计耗时 " + operationHours + " 小时，完成时间: " + ship.operationFinishTime);
                    system.printBestUnloadOrder(ship,ship.currentDestination, ship.operationFinishTime);
                }
            }
            // 2. 作业完成的船，开始运输
            for (Ship ship : system.ships) {
                if (ship.operationFinishTime != null && !ship.operationFinishTime.isAfter(currentDateTime)) {
                    // 调度下一目的港
                    if (ship.loadedCargo != null && !ship.loadedCargo.isEmpty()) {
                        // 船上有货，优先以货物目的港为下一目的港
                        Set<String> destSet = new HashSet<>();
                        for (Cargo cargo : ship.loadedCargo) {
                            destSet.add(cargo.destinationPort);
                        }
                        // 获取最优卸货顺序
                        List<String> destList = new ArrayList<>(destSet);
                        List<List<String>> allOrders = new ArrayList<>();
                        // 全排列
                        ShipLoadingSystemDemo.permute(destList, 0, allOrders);
                        List<String> bestOrder = null;
                        int minTotalHours = Integer.MAX_VALUE;
                        for (List<String> order : allOrders) {
                            String curPort = ship.currentDestination;
                            int orderTotalHours = 0;
                            boolean allOnTime = true;
                            LocalDateTime t = currentDateTime;
                            for (String dest : order) {
                                Integer travelHours = system.routeGraph.shortestPath(curPort, "time", ship).get(dest);
                                if (travelHours == null) { allOnTime = false; break; }
                                t = t.plusHours(travelHours);
                                orderTotalHours += travelHours;
                                // 检查所有去dest的货物
                                for (Cargo c : ship.loadedCargo) {
                                    if (c.destinationPort.equals(dest)) {
                                        if (t.toLocalDate().isAfter(c.deadline)) {
                                            allOnTime = false;
                                            break;
                                        }
                                    }
                                }
                                if (!allOnTime) break;
                                curPort = dest;
                            }
                            if (allOnTime && orderTotalHours < minTotalHours) {
                                minTotalHours = orderTotalHours;
                                bestOrder = new ArrayList<>(order);
                            }
                        }
                        // 选择第一个目的港为下一目的港
                        String nextDest;
                        if (bestOrder != null) {
                            nextDest = bestOrder.get(0);
                        } else {
                            nextDest = destSet.stream().skip(random.nextInt(destSet.size())).findFirst().orElse(ship.currentDestination);
                        }
                        // 获取完整路径（含中转港）
                        List<String> path = system.routeGraph.getShortestPathSequence(ship.currentDestination, nextDest, ship);
                        if (path == null) path = new ArrayList<>();
                        if (path == null || path.size() < 2) {
                            // 直接到目的港
                            Map<String, Integer> timeMap = system.routeGraph.shortestPath(ship.currentDestination, "time", ship);
                            int travelHours = timeMap.getOrDefault(nextDest, 120);
                            System.out.println("[运输] " + ship.name + " 从 " + ship.currentDestination + " 出发前往 " + nextDest + "，航行时间: " + travelHours + " 小时，预计到达: " + currentDateTime.plusHours(travelHours));
                            ship.currentDestination = nextDest;
                            ship.estimatedArrival = currentDateTime.plusHours(travelHours);
                        } else {
                            // 依次经过中转港口
                            String curPort = ship.currentDestination;
                            LocalDateTime t = currentDateTime;
                            for (int i = 1; i < path.size(); i++) {
                                String midPort = path.get(i);
                                Integer travelHours = system.routeGraph.shortestPath(curPort, "time", ship).get(midPort);
                                if (travelHours == null) travelHours = 120;
                                t = t.plusHours(travelHours);
                                System.out.println("[运输] " + ship.name + " 从 " + curPort + " 出发前往 " + midPort + "，航行时间: " + travelHours + " 小时，预计到达: " + t);
                                curPort = midPort;
                                // 只在中转港口（非终点）装货
                                if (i < path.size() - 1) {
                                    // 记录装货前后数量和重量
                                    int beforeCount = ship.loadedCargo.size();
                                    double beforeWeight = ship.currentCargoWeight;
                                    // 备份船货物状态
                                    List<Cargo> backupCargo = new ArrayList<>(ship.loadedCargo);
                                    double backupWeight = ship.currentCargoWeight;
                                    system.reloadShip(ship, system.ports.get(midPort), t.toLocalDate());
                                    int afterCount = ship.loadedCargo.size();
                                    double afterWeight = ship.currentCargoWeight;
                                    int loadCount = afterCount - beforeCount;
                                    double loadWeight = afterWeight - beforeWeight;
                                    // 计算作业时间
                                    double hourByBox = loadCount * 1/20.0;
                                    double hourByWeight = loadWeight * 1/20.0;
                                    int operationHours = (int)Math.ceil(Math.min(hourByBox, hourByWeight));
                                    if (operationHours < 1) operationHours = 1;
                                    t = t.plusHours(operationHours); // 等待作业完成
                                    System.out.println("[中转港作业] " + ship.name + " 在 " + midPort + " 装货 " + loadCount + " 个，作业耗时 " + operationHours + " 小时，完成时间: " + t);
                                    // 校验：装货后所有货物的deadline是否满足（模拟后续所有目的港的到达与卸货）
                                    boolean allValid = true;
                                    if (bestOrder != null) {
                                        // 复制一份货物清单用于模拟
                                        List<Cargo> cargoSim = new ArrayList<>(ship.loadedCargo);
                                        String simLastPort = midPort;
                                        LocalDateTime simTime = t;
                                        for (String dest : bestOrder) {
                                            // 航行到目的港
                                            Integer simTravel = system.routeGraph.shortestPath(simLastPort, "time", ship).get(dest);
                                            if (simTravel == null) simTravel = 120;
                                            simTime = simTime.plusHours(simTravel);
                                            // 统计要卸货的货物
                                            List<Cargo> toUnload = new ArrayList<>();
                                            for (Cargo c : cargoSim) {
                                                if (c.destinationPort.equals(dest)) {
                                                    toUnload.add(c);
                                                }
                                            }
                                            int unloadCountSim = toUnload.size();
                                            double unloadWeightSim = toUnload.stream().mapToDouble(c -> c.weight).sum();
                                            // 卸货作业时间
                                            double hourByBoxSim = unloadCountSim * 1/20.0;
                                            double hourByWeightSim = unloadWeightSim * 1/20.0;
                                            int opHourSim = (int)Math.ceil(Math.min(hourByBoxSim, hourByWeightSim));
                                            if (opHourSim < 1) opHourSim = 1;
                                            simTime = simTime.plusHours(opHourSim);
                                            // 检查deadline
                                            for (Cargo c : toUnload) {
                                                if (simTime.toLocalDate().isAfter(c.deadline)) {
                                                    allValid = false;
                                                    break;
                                                }
                                            }
                                            if (!allValid) break;
                                            // 卸货
                                            cargoSim.removeIf(c -> c.destinationPort.equals(dest));
                                            simLastPort = dest;
                                        }
                                    }
                                    if (!allValid) {
                                        // 回滚装货
                                        ship.loadedCargo.clear();
                                        ship.loadedCargo.addAll(backupCargo);
                                        ship.currentCargoWeight = backupWeight;
                                        System.out.println("[中转港装货回滚] " + ship.name + " 在 " + midPort + " 装货后会导致超时，已跳过装货");
                                    }
                                } else {
                                    // 最后到达目的港
                                    ship.currentDestination = midPort;
                                    ship.estimatedArrival = t;
                                }
                            }
                        }
                    } else {
                        // 船上没货，停留在港口
                        System.out.println("[调度] " + ship.name + " 暂无货物，停留在 " + ship.currentDestination);
                        ship.estimatedArrival = null;
                    }
                    ship.operationFinishTime = null;

                }
            }
            // 3. 为每个港口生成新货物（可按需调整频率）
            if (hour % 24 == 0) { //
                for (Port port : system.ports.values()) {
                    for (int i = 0; i < 10; i++) {
                        generateRandomCargo(port, currentDateTime.toLocalDate());
                    }
                }
                
                // === 新增：生成新货物后，检查是否有船在港口作业，尝试继续装货 ===
                System.out.println("\n===== 新货物生成后船舶装货优化 =====");
                for (Ship ship : system.ships) {
                    // 检查船舶是否在港口且正在作业（operationFinishTime不为null且未完成）
                    if (ship.operationFinishTime != null && ship.operationFinishTime.isAfter(currentDateTime)) {
                        Port currentPort = system.ports.get(ship.currentDestination);
                        if (currentPort != null) {
                            system.tryLoadNewCargoToOperatingShip(ship, currentPort, currentDateTime);
                        }
                    }
                }
                System.out.println("===== 新货物装货优化完成 =====\n");
            }

            // === 新增：让所有停留在港口的空船尝试装货 ===
            for (Ship ship : system.ships) {
                if (ship.estimatedArrival == null && ship.operationFinishTime == null && (ship.loadedCargo == null || ship.loadedCargo.isEmpty())) {
                    system.reloadShip(ship, system.ports.get(ship.currentDestination), currentDateTime.toLocalDate());
                    if (ship.loadedCargo != null && !ship.loadedCargo.isEmpty()) {
                        ShipLoadingSystemDemo.planNextDestination(ship, system, currentDateTime.toLocalDate());
                    }
                }
            }
            // 4. 检查所有港口货物是否超时
            for (Port port : system.ports.values()) {
                List<Cargo> overTimeCargo = new ArrayList<>();
                for (Cargo cargo : new ArrayList<>(port.cargoList)) {
                    if (cargo.deadline.isBefore(currentDateTime.toLocalDate())) {
//                        System.out.println("[超时警告] 港口 " + port.name + " 有超时货物: " + cargo);
                        overTimeCargo.add(cargo);
                    }
                }

                System.out.println("[超时货物] 港口 " + port.name + " 有 " + overTimeCargo.size() + " 超deadline货物");

            }
            // 5. 输出当天系统状态
            if (currentDateTime.getHour() == 0) {
                system.printSystemStatus();
            }
            currentDateTime = currentDateTime.plusHours(1);
        }
        // 让所有停留在港口的空船尝试装货
        for (Ship ship : system.ships) {
            if (ship.estimatedArrival == null && ship.operationFinishTime == null && ship.loadedCargo.isEmpty()) {
                system.reloadShip(ship, system.ports.get(ship.currentDestination), currentDateTime.toLocalDate());
                // 如果装上货物，分配目的港和到达时间
                if (!ship.loadedCargo.isEmpty()) {
                    ShipLoadingSystemDemo.planNextDestination(ship, system, currentDateTime.toLocalDate());
                }
            }
        }
        
        // 演示新货物装货优化功能
        system.demonstrateNewCargoLoadingOptimization();
    }



    // 路线构建器 - 用于根据水道列表自动拼出路线
    static class RouteBuilder {
        private List<Waterway> allWaterways; // 所有可用的水道

        public RouteBuilder(List<Waterway> allWaterways) {
            this.allWaterways = allWaterways != null ? allWaterways : new ArrayList<>();
        }

        // 根据起运港和目的港自动拼出路线
        public Route buildRoute(String originPort, String destinationPort) {
            List<Waterway> routeWaterways = new ArrayList<>();
            
            // 查找从起运港开始的水道
            Waterway startWaterway = findWaterwayStartingFrom(originPort);
            if (startWaterway == null) {
                return null; // 找不到从起运港开始的水道
            }
            
            routeWaterways.add(startWaterway);
            
            // 如果第一个水道的目的港就是目标港口，直接返回
            Coordinate firstDest = startWaterway.getDestinationCoordinate();
            if (firstDest != null && destinationPort.equals(firstDest.portName)) {
                return new Route(originPort, destinationPort, routeWaterways);
            }
            
            // 否则需要寻找连通的水道链
            String currentPort = firstDest != null ? firstDest.portName : null;
            int maxSteps = 10; // 防止无限循环
            int steps = 0;
            
            while (currentPort != null && !currentPort.equals(destinationPort) && steps < maxSteps) {
                Waterway nextWaterway = findWaterwayStartingFrom(currentPort);
                if (nextWaterway == null) {
                    break; // 找不到下一个连通的水道
                }
                
                routeWaterways.add(nextWaterway);
                Coordinate nextDest = nextWaterway.getDestinationCoordinate();
                currentPort = nextDest != null ? nextDest.portName : null;
                steps++;
            }
            
            // 检查最后是否到达目标港口
            if (currentPort != null && currentPort.equals(destinationPort)) {
                return new Route(originPort, destinationPort, routeWaterways);
            }
            
            return null; // 无法拼出完整路线
        }

        // 查找从指定港口开始的水道
        private Waterway findWaterwayStartingFrom(String portName) {
            for (Waterway waterway : allWaterways) {
                Coordinate origin = waterway.getOriginCoordinate();
                if (origin != null && portName.equals(origin.portName)) {
                    return waterway;
                }
            }
            return null;
        }

        // 查找所有可能的路线（多条路径）
        public List<Route> buildAllPossibleRoutes(String originPort, String destinationPort) {
            List<Route> routes = new ArrayList<>();
            
            // 查找所有从起运港开始的水道
            List<Waterway> startWaterways = findAllWaterwaysStartingFrom(originPort);
            
            for (Waterway startWaterway : startWaterways) {
                Route route = buildRouteFromWaterway(startWaterway, destinationPort);
                if (route != null) {
                    routes.add(route);
                }
            }
            
            return routes;
        }

        // 从指定水道开始构建路线
        private Route buildRouteFromWaterway(Waterway startWaterway, String destinationPort) {
            List<Waterway> routeWaterways = new ArrayList<>();
            routeWaterways.add(startWaterway);
            
            String currentPort = startWaterway.getDestinationCoordinate() != null ? 
                    startWaterway.getDestinationCoordinate().portName : null;
            
            int maxSteps = 10;
            int steps = 0;
            
            while (currentPort != null && !currentPort.equals(destinationPort) && steps < maxSteps) {
                Waterway nextWaterway = findWaterwayStartingFrom(currentPort);
                if (nextWaterway == null) {
                    break;
                }
                
                routeWaterways.add(nextWaterway);
                Coordinate nextDest = nextWaterway.getDestinationCoordinate();
                currentPort = nextDest != null ? nextDest.portName : null;
                steps++;
            }
            
            if (currentPort != null && currentPort.equals(destinationPort)) {
                String originPort = startWaterway.getOriginCoordinate() != null ? 
                        startWaterway.getOriginCoordinate().portName : "未知";
                return new Route(originPort, destinationPort, routeWaterways);
            }
            
            return null;
        }

        // 查找所有从指定港口开始的水道
        private List<Waterway> findAllWaterwaysStartingFrom(String portName) {
            List<Waterway> result = new ArrayList<>();
            for (Waterway waterway : allWaterways) {
                Coordinate origin = waterway.getOriginCoordinate();
                if (origin != null && portName.equals(origin.portName)) {
                    result.add(waterway);
                }
            }
            return result;
        }

        // 添加水道到构建器
        public void addWaterway(Waterway waterway) {
            if (waterway != null) {
                this.allWaterways.add(waterway);
            }
        }

        // 清空所有水道
        public void clearWaterways() {
            this.allWaterways.clear();
        }

        // 获取所有水道
        public List<Waterway> getAllWaterways() {
            return new ArrayList<>(allWaterways);
        }
    }
}