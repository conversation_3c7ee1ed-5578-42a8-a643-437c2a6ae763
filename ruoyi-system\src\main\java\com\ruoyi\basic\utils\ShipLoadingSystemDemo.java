package com.ruoyi.basic.utils;
import com.alibaba.fastjson2.JSONObject;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.stream.Collectors;

public class ShipLoadingSystemDemo {
    static class Ship {
        String name;
        double length; // 船长
        double width; // 船宽
        double draft; // 吃水
        double maxWeight; // 最大载重
        int maxTEU; // 最大TEU
        String currentDestination; // 当前目的港口
        LocalDateTime estimatedArrival; // 预计到港时间（小时）
        double currentCargoWeight; // 当前货物载重量
        Map<String, Double> maxCargoWeightPerType; // 每种货类最大载重
        boolean isOnePortShip; // 是否一港通
        // 当前装载的货物
        List<Cargo> loadedCargo = new ArrayList<>();
        LocalDateTime operationFinishTime; // 当前装卸作业预计完成时间

        public Ship(String name, double length, double width, double draft,
                    double maxWeight, int maxTEU, String currentDestination,
                    LocalDateTime estimatedArrival, Map<String, Double> maxCargoWeightPerType,
                    boolean isOnePortShip) {
            this.name = name;
            this.length = length;
            this.width = width;
            this.draft = draft;
            this.maxWeight = maxWeight;
            this.maxTEU = maxTEU;
            this.currentDestination = currentDestination;
            this.estimatedArrival = estimatedArrival;
            this.maxCargoWeightPerType = maxCargoWeightPerType;
            this.isOnePortShip = isOnePortShip;
            this.currentCargoWeight = 0.0;
            this.operationFinishTime = null;
        }

        // 获取剩余载重容量
        public double getRemainingWeightCapacity() {
            return maxWeight - currentCargoWeight;
        }

        // 获取剩余TEU容量
        public int getRemainingTEUCapacity() {
            int loadedTEU = loadedCargo.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
            return maxTEU - loadedTEU;
        }

        // 获取特定货类的剩余容量
        public double getRemainingCapacityForCargoType(String cargoType) {
            double currentTypeWeight = loadedCargo.stream()
                    .filter(c -> c.cargoType.equals(cargoType))
                    .mapToDouble(c -> c.weight)
                    .sum();

            double maxForType = maxCargoWeightPerType.getOrDefault(cargoType, Double.MAX_VALUE);
            return Math.min(maxForType - currentTypeWeight, getRemainingWeightCapacity());
        }

        // 添加货物
        public boolean addCargo(Cargo cargo) {
            if (cargo.isOnePort && !this.isOnePortShip) {
//                System.out.println("【装载失败】货物是一港通，船不是一港通");
                return false;
            }
            if (this.isOnePortShip && cargo.isOnePort) {
                if (!loadedCargo.isEmpty()) {
                    String onePortDest = loadedCargo.get(0).destinationPort;
                    if (!cargo.destinationPort.equals(onePortDest)) {
//                        System.out.println("【装载失败】一港通船舶装载一港通货物时，目的港不一致");
                        return false;
                    }
                    for (Cargo c : loadedCargo) {
                        if (!c.destinationPort.equals(cargo.destinationPort)) {
//                            System.out.println("【装载失败】一港通船舶已有货物目的港与新货物不一致");
                            return false;
                        }
                    }
                }
            }
            boolean hasOnePortCargo = loadedCargo.stream().anyMatch(c -> c.isOnePort);
            if (hasOnePortCargo) {
                String onePortDest = loadedCargo.stream().filter(c -> c.isOnePort).findFirst().get().destinationPort;
                if (!cargo.destinationPort.equals(onePortDest)) {
//                    System.out.println("【装载失败】船上已有一港通货物，目的港不一致");
                    return false;
                }
            }
            if (cargo.weight > getRemainingWeightCapacity()) {
//                System.out.println("【装载失败】重量超限");
                return false;
            }
            int requiredTEU = cargo.containerType.equals("20ft") ? 1 : 2;
            if (requiredTEU > getRemainingTEUCapacity()) {
//                System.out.println("【装载失败】TEU超限");
                return false;
            }
            if (cargo.weight > getRemainingCapacityForCargoType(cargo.cargoType)) {
//                System.out.println("【装载失败】货类容量超限");
                return false;
            }
            // 添加货物
            loadedCargo.add(cargo);
            currentCargoWeight += cargo.weight;
            return true;
        }

        // 卸载货物
        public List<Cargo> unloadCargo(String port, boolean unloadAll) {
            List<Cargo> unloaded = new ArrayList<>();
            Iterator<Cargo> iterator = loadedCargo.iterator();

            while (iterator.hasNext()) {
                Cargo cargo = iterator.next();
                if (cargo.destinationPort.equals(port)) {
                    if (unloadAll || cargo.destinationPort.equals(currentDestination)) {
                        unloaded.add(cargo);
                        currentCargoWeight -= cargo.weight;
                        iterator.remove();
                    }
                }
            }

            return unloaded;
        }

        @Override
        public String toString() {
            return String.format("Ship{name='%s', currentDestination='%s', capacity=%.1f/%.1f tons, TEU=%d/%d}",
                    name, currentDestination, currentCargoWeight, maxWeight,
                    maxTEU - getRemainingTEUCapacity(), maxTEU);
        }
    }

    static class RouteInfo {
        int distance; // 距离
        int time;    // 时间（单位：小时）

        public RouteInfo(int distance, int time) {
            this.distance = distance;
            this.time = time;
        }
    }

    static class RouteGraph {
        Map<String, Map<String, RouteInfo>> graph = new HashMap<>();

        // 添加航线，包含距离和时间
        public void addRoute(String from, String to, int distance, int time) {
            graph.computeIfAbsent(from, k -> new HashMap<>()).put(to, new RouteInfo(distance, time));
            graph.computeIfAbsent(to, k -> new HashMap<>()).put(from, new RouteInfo(distance, time));
        }

        // mode: "distance" 或 "time"
        public Map<String, Integer> shortestPath(String start, String mode) {
            Map<String, Integer> distances = new HashMap<>();
            PriorityQueue<Map.Entry<String, Integer>> pq = new PriorityQueue<>(
                    (a, b) -> a.getValue() - b.getValue()
            );
            for (String port : graph.keySet()) {
                distances.put(port, Integer.MAX_VALUE);
            }
            distances.put(start, 0);
            pq.add(new AbstractMap.SimpleEntry<>(start, 0));
            while (!pq.isEmpty()) {
                Map.Entry<String, Integer> entry = pq.poll();
                String currentPort = entry.getKey();
                int currentDist = entry.getValue();
                if (currentDist > distances.get(currentPort)) continue;
                if (graph.containsKey(currentPort)) {
                    for (Map.Entry<String, RouteInfo> neighbor : graph.get(currentPort).entrySet()) {
                        String nextPort = neighbor.getKey();
                        int value = mode.equals("time") ? neighbor.getValue().time : neighbor.getValue().distance;
                        int newDist = currentDist + value;
                        if (newDist < distances.get(nextPort)) {
                            distances.put(nextPort, newDist);
                            pq.add(new AbstractMap.SimpleEntry<>(nextPort, newDist));
                        }
                    }
                }
            }
            return distances;
        }

        // 获取最短时间路径的完整港口序列
        public List<String> getShortestPathSequence(String start, String end) {
            Map<String, Integer> dist = new HashMap<>();
            Map<String, String> prev = new HashMap<>();
            PriorityQueue<Map.Entry<String, Integer>> pq = new PriorityQueue<>(
                    (a, b) -> a.getValue() - b.getValue()
            );
            for (String port : graph.keySet()) {
                dist.put(port, Integer.MAX_VALUE);
            }
            dist.put(start, 0);
            pq.add(new AbstractMap.SimpleEntry<>(start, 0));
            while (!pq.isEmpty()) {
                Map.Entry<String, Integer> entry = pq.poll();
                String currentPort = entry.getKey();
                int currentDist = entry.getValue();
                if (currentDist > dist.get(currentPort)) continue;
                if (graph.containsKey(currentPort)) {
                    for (Map.Entry<String, RouteInfo> neighbor : graph.get(currentPort).entrySet()) {
                        String nextPort = neighbor.getKey();
                        int value = neighbor.getValue().time;
                        int newDist = currentDist + value;
                        if (newDist < dist.get(nextPort)) {
                            dist.put(nextPort, newDist);
                            prev.put(nextPort, currentPort);
                            pq.add(new AbstractMap.SimpleEntry<>(nextPort, newDist));
                        }
                    }
                }
            }
            // 回溯路径
            List<String> path = new ArrayList<>();
            String curr = end;
            while (curr != null && prev.containsKey(curr)) {
                path.add(curr);
                curr = prev.get(curr);
            }
            if (curr != null && curr.equals(start)) {
                path.add(start);
            }
            Collections.reverse(path);
            return path;
        }
    }
    static class Port {
        String name;
        List<Cargo> cargoList = new ArrayList<>();

        public Port(String name) {
            this.name = name;
        }

        public void addCargo(Cargo cargo) {
            cargoList.add(cargo);
        }

        public void removeCargo(Cargo cargo) {
            cargoList.remove(cargo);
        }

        // 获取指定目的港的货物
        public List<Cargo> getCargoForDestination(String destination) {
            List<Cargo> result = new ArrayList<>();
            for (Cargo cargo : cargoList) {
                if (cargo.destinationPort.equals(destination)) {
                    result.add(cargo);
                }
            }
            return result;
        }
    }

    static class Cargo {
        String cargoType; // 货类
        double weight; // 重量
        String containerType; // 箱型 (20ft/40ft)
        String originPort; // 起运港
        String destinationPort; // 目的港
        boolean isOnePort; // 是否一港通
        int dangerLevel; // 危险品等级 (0表示普通货物)
        String consignmentId; // 委托号
        boolean canSplit; // 委托能否拆分
        LocalDate deadline; // 委托截止日期
        String containerNumber; // 箱号
        LocalDate latestDeliveryDate; // 最晚发货日期

        public Cargo(String cargoType, double weight, String containerType,
                     String originPort, String destinationPort, boolean isOnePort,
                     int dangerLevel, String consignmentId, boolean canSplit,
                     LocalDate deadline, String containerNumber, LocalDate latestDeliveryDate) {
            this.cargoType = cargoType;
            this.weight = weight;
            this.containerType = containerType;
            this.originPort = originPort;
            this.destinationPort = destinationPort;
            this.isOnePort = isOnePort;
            this.dangerLevel = dangerLevel;
            this.consignmentId = consignmentId;
            this.canSplit = canSplit;
            this.deadline = deadline;
            this.containerNumber = containerNumber;
            this.latestDeliveryDate = latestDeliveryDate;
        }

        @Override
        public String toString() {
            return String.format("Cargo{type='%s', %.1f tons, %s, %s->%s, consignment=%s, containerNumber=%s, isOnePort=%b, deadline=%s, latestDeliveryDate=%s}",
                    cargoType, weight, containerType, originPort,
                    destinationPort, consignmentId, containerNumber, isOnePort, deadline, latestDeliveryDate);
        }

        public String getConsignmentId() {
            return consignmentId;
        }

        public String getContainerNumber() {
            return containerNumber;
        }

        public String getDestinationPort() {
            return destinationPort;
        }
    }

    static Random random = new Random();

    // 随机生成箱号
    private static String generateRandomContainerNumber() {
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            sb.append(letters.charAt(random.nextInt(letters.length())));
        }
        for (int i = 0; i < 7; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    // 随机生成货物
    public static void generateRandomCargo(Port port, LocalDate currentDate) {
        String[] cargoTypes = {"General", "Refrigerated", "Dangerous"};
        String[] containerTypes = {"20ft", "40ft"};
        String[] destPorts = {"Shanghai", "Singapore", "Rotterdam", "New York", "Tokyo","Guangzhou"};
        String cargoType = cargoTypes[random.nextInt(cargoTypes.length)];
        String origin = port.name;
        String dest;
        do {
            dest = destPorts[random.nextInt(destPorts.length)];
        } while (dest.equals(origin));
        boolean isOnePort = random.nextDouble() > 0.9 ? true : false;
        int dangerLevel = cargoType.equals("Dangerous") ? 1 + random.nextInt(3) : 0;
        String consignmentId = origin.substring(0,2).toUpperCase() + String.format("%03d", random.nextInt(1000));
        boolean canSplit = random.nextBoolean();
        LocalDate deadline = currentDate.plusDays(10 + random.nextInt(3));
        LocalDate latestDeliveryDate = currentDate.plusDays(3 + random.nextInt(3));
        if (canSplit) {
            String containerType = containerTypes[random.nextInt(containerTypes.length)];
            double weight = 5 + random.nextInt(20);
            String containerNumber = generateRandomContainerNumber();
            port.addCargo(new Cargo(cargoType, weight, containerType, origin, dest, isOnePort, dangerLevel, consignmentId, canSplit, deadline, containerNumber, latestDeliveryDate));
        } else {
            int boxCount = 2 + random.nextInt(3); // 2~4个箱子
            for (int i = 0; i < boxCount; i++) {
                String containerType = containerTypes[random.nextInt(containerTypes.length)];
                double weight = 5 + random.nextInt(20);
                String containerNumber = generateRandomContainerNumber();
                port.addCargo(new Cargo(cargoType, weight, containerType, origin, dest, isOnePort, dangerLevel, consignmentId, canSplit, deadline, containerNumber, latestDeliveryDate));
            }
        }
    }

    // 规划下一目的港和到达时间（全排列最优可行方案）
    public static void planNextDestination(Ship ship, ShipLoadingSystem system, LocalDate currentDate) {
        // 优先判断船上是否有货物
        if (ship.loadedCargo != null && !ship.loadedCargo.isEmpty()) {
            // 船上有货物，枚举所有目的港顺序
            Set<String> destSet = ship.loadedCargo.stream().map(c -> c.destinationPort).collect(Collectors.toSet());
            List<String> destList = new ArrayList<>(destSet);
            List<List<String>> allOrders = new ArrayList<>();
            permute(destList, 0, allOrders);
            LocalDateTime bestArrival = null;
            List<String> bestOrder = null;
            int minTotalHours = Integer.MAX_VALUE;
            for (List<String> order : allOrders) {
                LocalDateTime t = LocalDateTime.of(currentDate, java.time.LocalTime.of(0,0));
                String curPort = ship.currentDestination;
                boolean allOnTime = true;
                Map<String, LocalDateTime> arrivalMap = new HashMap<>();
                int totalHours = 0;
                for (String dest : order) {
                    int travelHours = system.routeGraph.shortestPath(curPort, "time").get(dest);
                    t = t.plusHours(travelHours);
                    totalHours += travelHours;
                    // 检查所有去dest的货物
                    for (Cargo c : ship.loadedCargo) {
                        if (c.destinationPort.equals(dest)) {
                            if (t.toLocalDate().isAfter(c.deadline)) {
                                allOnTime = false;
                                break;
                            }
                            arrivalMap.put(dest, t);
                        }
                    }
                    if (!allOnTime) break;
                    curPort = dest;
                }
                if (allOnTime && totalHours < minTotalHours) {
                    minTotalHours = totalHours;
                    bestOrder = new ArrayList<>(order);
                    bestArrival = arrivalMap.get(order.get(0)); // 只用于日志
                }
            }
            if (bestOrder != null) {
                // 选择第一个目的港为下一目的港
                String nextDest = bestOrder.get(0);
                int hours = system.routeGraph.shortestPath(ship.currentDestination, "time").get(nextDest);
                ship.currentDestination = nextDest;
                ship.estimatedArrival = LocalDateTime.of(currentDate, java.time.LocalTime.of(0,0)).plusHours(hours);
                System.out.println("[调度] " + ship.name + " 规划下一目的港: " + nextDest + ", 预计到达: " + ship.estimatedArrival + ", 最优顺序: " + bestOrder);
            } else {
                // 没有可行方案，停留在港口
                ship.estimatedArrival = null;
                System.out.println("[调度] " + ship.name + " 暂无可行航线，停留在 " + ship.currentDestination);
            }
        } else {
            // 船上没有货物，先尝试装货
            Port currentPort = system.ports.get(ship.currentDestination);
            if (currentPort == null) return;
            boolean loaded = false;
            // 简单贪心装载
            for (Cargo cargo : new ArrayList<>(currentPort.cargoList)) {
                if (currentDate.atStartOfDay().isAfter(cargo.latestDeliveryDate.atStartOfDay())) continue;
                if (ship.addCargo(cargo)) {
                    currentPort.removeCargo(cargo);
                    loaded = true;
                }
                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
            }
            if (loaded) {
                // 装上货物后，重新走"有货物"分支
                planNextDestination(ship, system, currentDate);
                return;
            }
            // 没装上货物，才考虑空船调度
            Set<String> destSet = new HashSet<>();
            for (Cargo cargo : currentPort.cargoList) {
                destSet.add(cargo.destinationPort);
            }
            if (!destSet.isEmpty()) {
                String nextDest = destSet.stream().skip(new Random().nextInt(destSet.size())).findFirst().orElse(ship.currentDestination);
                Map<String, Integer> timeMap = system.routeGraph.shortestPath(ship.currentDestination, "time");
                int hours = timeMap.getOrDefault(nextDest, 120);
                ship.currentDestination = nextDest;
                ship.estimatedArrival = LocalDateTime.of(currentDate, java.time.LocalTime.of(0,0)).plusHours(hours);
                System.out.println("[调度] " + ship.name + " 规划下一目的港: " + nextDest + ", 预计到达: " + ship.estimatedArrival);
            } else {
                // 没有货物，停留在港口
                ship.estimatedArrival = null;
                System.out.println("[调度] " + ship.name + " 暂无货物，停留在 " + ship.currentDestination);
            }
        }
    }

    // 全排列工具
    private static void permute(List<String> arr, int k, List<List<String>> result) {
        if (k == arr.size()) {
            result.add(new ArrayList<>(arr));
        } else {
            for (int i = k; i < arr.size(); i++) {
                Collections.swap(arr, i, k);
                permute(arr, k + 1, result);
                Collections.swap(arr, i, k);
            }
        }
    }

    static class ShipLoadingSystem {
        List<Ship> ships = new ArrayList<>();
        Map<String, Port> ports = new HashMap<>();
        RouteGraph routeGraph = new RouteGraph();

        // 添加船舶
        public void addShip(Ship ship) {
            ships.add(ship);
        }

        // 添加港口
        public void addPort(Port port) {
            ports.put(port.name, port);
        }

        // 添加航线（增加时间参数）
        public void addRoute(String port1, String port2, int distance, int time) {
            routeGraph.addRoute(port1, port2, distance, time);
        }

        // 处理船舶到达港口
        public void processShipArrival(Ship ship, String portName, boolean unloadAll) {
            Port port = ports.get(portName);
            if (port == null) return;

            // 卸载货物
            List<Cargo> unloaded = ship.unloadCargo(portName, unloadAll);
            System.out.println(ship.name + " 在 " + portName + " 卸载了 " + unloaded.size() + " 个货物");
//        if(unloaded.size() > 0){
//            System.out.println("卸载货物清单: " );
//            for (Cargo cargo : unloaded) {
//                System.out.println(cargo);
//            }
//        }

            // 如果部分卸载，获取剩余货物的目的港
            Set<String> remainingDestinations = new HashSet<>();
            for (Cargo cargo : ship.loadedCargo) {
                remainingDestinations.add(cargo.destinationPort);
            }

            if (!remainingDestinations.isEmpty()) {
                // 优先装载与剩余货物目的港相同的货物
                for (String dest : remainingDestinations) {
                    loadCargoToShip(ship, port, dest, LocalDate.now());
                }

                // 如果船还没满，装载在最短路径上的货物
                if (ship.getRemainingWeightCapacity() > 0 || ship.getRemainingTEUCapacity() > 0) {
                    loadCargoOnShortestPath(ship, port, new ArrayList<>(remainingDestinations), LocalDate.now());
                }
            } else {
                // 全部卸载，重新装载货物
                reloadShip(ship, port, LocalDate.now());
            }
            // 新增：无论部分还是全部卸货，最后都补货装满船，并统计装载数量
            reloadShip(ship, port, LocalDate.now());
        }

        // 装载指定目的港的货物
        private void loadCargoToShip(Ship ship, Port port, String destination, LocalDate currentDate) {
            List<Cargo> availableCargo = port.getCargoForDestination(destination);
            for (Cargo cargo : availableCargo) {
                if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                if (ship.addCargo(cargo)) {
                    port.removeCargo(cargo);
//                System.out.println("装载货物: " + cargo);
                }
                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) {
                    break;
                }
            }
        }

        // 装载在最短路径上的货物（以最短时间为主）
        private void loadCargoOnShortestPath(Ship ship, Port port, List<String> destinations, LocalDate currentDate) {
            // 计算到所有目的港的最短时间路径
            Map<String, Map<String, Integer>> timeToMainDest = new HashMap<>();
            for (String dest : destinations) {
                Map<String, Integer> time = routeGraph.shortestPath(dest, "time");
                timeToMainDest.put(dest, time);
            }
            List<Cargo> allCargo = new ArrayList<>(port.cargoList);
            allCargo.sort(Comparator.comparing(c -> c.deadline));
            for (Cargo cargo : allCargo) {
                if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                boolean onPath = false;
                for (String dest : destinations) {
                    Map<String, Integer> time = routeGraph.shortestPath(ship.currentDestination, "time");
                    if (time.containsKey(cargo.destinationPort)) {
                        onPath = true;
                        break;
                    }
                }
                if (onPath && ship.addCargo(cargo)) {
                    port.removeCargo(cargo);
//                System.out.println("装载在最短时间路径上的货物: " + cargo);
                }
                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) {
                    break;
                }
            }
        }

        // 辅助方法：将不可拆分委托分批装到多条船（贪心式装载）
        private void splitAndAssignConsignment(List<Cargo> consignment, List<Ship> ships, Port port, LocalDate currentDate) {
            List<Cargo> remaining = new ArrayList<>(consignment);
            boolean assigned = false;
            for (Ship s : ships) {
                Iterator<Cargo> it = remaining.iterator();
                while (it.hasNext()) {
                    Cargo cargo = it.next();
                    if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                    if (s.addCargo(cargo)) {
                        port.removeCargo(cargo);
//                    System.out.println("[拆分装载] " + s.name + " 装载不可拆分委托货物: " + cargo);
                        it.remove();
                    }
                }
                if (remaining.isEmpty()) {
                    assigned = true;
                    break;
                }
            }
            if (!assigned && !remaining.isEmpty()) {
                System.out.println("无法全部拆分装载不可拆分委托: " + consignment.get(0).consignmentId);
            }
        }

        // 重新装载船舶
        public void reloadShip(Ship ship, Port port, LocalDate currentDate) {
            // 添加调试信息
            System.out.println("【装载调试】" + ship.name + " 在 " + port.name + " 开始装载");
            System.out.println("【装载调试】港口货物总数: " + port.cargoList.size());
            System.out.println("【装载调试】船舶剩余载重: " + ship.getRemainingWeightCapacity() + "/" + ship.maxWeight);
            System.out.println("【装载调试】船舶剩余TEU: " + ship.getRemainingTEUCapacity() + "/" + ship.maxTEU);

            // 统计货物类型
            long onePortCount = port.cargoList.stream().filter(c -> c.isOnePort).count();
            long normalCount = port.cargoList.stream().filter(c -> !c.isOnePort).count();
            System.out.println("【装载调试】港口一港通货物: " + onePortCount + ", 普通货物: " + normalCount);

            // 统计过期货物
            long expiredCount = port.cargoList.stream().filter(c -> currentDate.isAfter(c.latestDeliveryDate)).count();
            System.out.println("【装载调试】港口过latestDeliveryDate期货物: " + expiredCount);

            // 一港通船舶特殊处理：只装同一目的港的货物
            if (ship.isOnePortShip) {
                System.out.println("【装载调试】一港通船舶装载逻辑");
                // 统计港口所有一港通货物的目的港
                Map<String, List<Cargo>> onePortCargoByDest = new HashMap<>();
                for (Cargo cargo : port.cargoList) {
                    if (cargo.isOnePort && !currentDate.isAfter(cargo.latestDeliveryDate)) {
                        onePortCargoByDest.computeIfAbsent(cargo.destinationPort, k -> new ArrayList<>()).add(cargo);
                    }
                }
                // 选数量最多的目的港
                String targetDest = null;
                int maxCount = 0;
                for (Map.Entry<String, List<Cargo>> entry : onePortCargoByDest.entrySet()) {
                    if (entry.getValue().size() > maxCount) {
                        maxCount = entry.getValue().size();
                        targetDest = entry.getKey();
                    }
                }
                System.out.println("【装载调试】选择目的港: " + targetDest + ", 货物数量: " + maxCount);
                // 只装去targetDest的货物
                if (targetDest != null) {
                    List<Cargo> cargos = onePortCargoByDest.get(targetDest);
                    // 按委托分组
                    Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                    for (Cargo cargo : cargos) {
                        consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                    }
                    // 先装不可拆分委托
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (!consignment.get(0).canSplit) {
                            double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                            int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                            boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                            if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                    totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                                for (Cargo cargo : consignment) {
                                    if (ship.addCargo(cargo)) {
                                        port.removeCargo(cargo);
//                                    System.out.println("【装载成功】一港通不可拆分: " + cargo);
                                    }
                                }
                            }
                        }
                    }
                    // 再装可拆分委托
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (consignment.get(0).canSplit) {
                            for (Cargo cargo : consignment) {
                                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                                if (ship.addCargo(cargo)) {
                                    port.removeCargo(cargo);
//                                System.out.println("【装载成功】一港通可拆分: " + cargo);
                                }
                            }
                        }
                    }
                }
                // 一港通船舶装完后直接返回
                return;
            }

            // 非一港通船舶优化：全新装载逻辑
            if (!ship.isOnePortShip) {
                System.out.println("【装载调试】普通船舶装载逻辑");
                Set<String> shipDestPorts = ship.loadedCargo.stream()
                        .map(c -> c.destinationPort)
                        .collect(Collectors.toSet());

                if (!shipDestPorts.isEmpty()) {
                    System.out.println("【装载调试】船上已有货物目的港: " + shipDestPorts);
                    // 2.1 优先装载与船上目的港相同的货物
                    for (String dest : shipDestPorts) {
                        List<Cargo> sameDestCargo = port.cargoList.stream()
                                .filter(c -> c.destinationPort.equals(dest) && !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort)
                                .collect(Collectors.toList());
//                    System.out.println("【装载调试】目的港 " + dest + " 可装载货物: " + sameDestCargo.size());
                        // 按委托分组
                        Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                        for (Cargo cargo : sameDestCargo) {
                            consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                        }
                        // 先装不可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (!consignment.get(0).canSplit) {
                                double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                                int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                                boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort);
                                if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                        totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                                    for (Cargo cargo : consignment) {
                                        if (!cargo.isOnePort && ship.addCargo(cargo)) {
                                            port.removeCargo(cargo);
//                                        System.out.println("【装载成功】同目的港不可拆分: " + cargo);
                                        }
                                    }
                                }
                            }
                        }
                        // 再装可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (consignment.get(0).canSplit) {
                                for (Cargo cargo : consignment) {
                                    if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0)
                                        break;
                                    if (!cargo.isOnePort && ship.addCargo(cargo)) {
                                        port.removeCargo(cargo);
//                                    System.out.println("【装载成功】同目的港可拆分: " + cargo);
                                    }
                                }
                            }
                        }
                        if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) return;
                    }
                    // 2.2 装载最短路径中转港上的货物
                    List<Cargo> portCargoList = new ArrayList<>(port.cargoList);
                    for (Cargo cargo : portCargoList) {
                        if (currentDate.isAfter(cargo.latestDeliveryDate) || cargo.isOnePort) continue;
                        List<String> path = routeGraph.getShortestPathSequence(ship.currentDestination, cargo.destinationPort);
                        if (path == null || path.isEmpty()) continue;
                        // 检查路径中是否包含船上已有货物的目的港（中转港）
                        boolean canLoad = false;
                        for (String midPort : path) {
                            if (shipDestPorts.contains(midPort)) {
                                canLoad = true;
                                break;
                            }
                        }
                        if (canLoad) {
                            // 按委托分组
                            Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                            consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                            for (List<Cargo> consignment : consignmentMap.values()) {
                                if (!consignment.get(0).canSplit) {
                                    double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                                    int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                                    boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort);
                                    if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                            totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                                        for (Cargo c : consignment) {
                                            if (!c.isOnePort && ship.addCargo(c)) {
                                                port.removeCargo(c);
//                                            System.out.println("【装载成功】中转港不可拆分: " + c);
                                            }
                                        }
                                    }
                                } else {
                                    for (Cargo c : consignment) {
                                        if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0)
                                            break;
                                        if (!c.isOnePort && ship.addCargo(c)) {
                                            port.removeCargo(c);
//                                        System.out.println("【装载成功】中转港可拆分: " + c);
                                        }
                                    }
                                }
                            }
                            if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) return;
                        }
                    }
                } else {
                    System.out.println("【装载调试】船上无货物，开始初始装载");
                    // 船上没有货物时，优先装载货物最多的目的港
                    Map<String, List<Cargo>> cargoByDest = new HashMap<>();
                    for (Cargo cargo : port.cargoList) {
                        if (!currentDate.isAfter(cargo.latestDeliveryDate) && !cargo.isOnePort) {
                            cargoByDest.computeIfAbsent(cargo.destinationPort, k -> new ArrayList<>()).add(cargo);
                        }
                    }
                    // 按目的港货物数量排序，优先装载
                    List<Map.Entry<String, List<Cargo>>> sortedDestinations = cargoByDest.entrySet().stream()
                            .sorted((a, b) -> Integer.compare(b.getValue().size(), a.getValue().size()))
                            .collect(Collectors.toList());

                    for (Map.Entry<String, List<Cargo>> entry : sortedDestinations) {
                        String dest = entry.getKey();
                        List<Cargo> cargos = entry.getValue();

//                    System.out.println("【装载调试】装载目的港 " + dest + " 的货物，数量: " + cargos.size());

                        // 按委托分组
                        Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                        for (Cargo cargo : cargos) {
                            consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                        }

                        // 先装不可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (!consignment.get(0).canSplit) {
                                double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                                int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                                boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate) && !c.isOnePort);
//                            System.out.println("【装载调试】不可拆分委托 - 重量: " + totalWeight + ", TEU: " + totalTEU + ", 有效: " + allValid);
                                if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                        totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                                    for (Cargo cargo : consignment) {
                                        if (!cargo.isOnePort && ship.addCargo(cargo)) {
                                            port.removeCargo(cargo);
//                                        System.out.println("【装载成功】初始不可拆分: " + cargo);
                                        }
                                    }
                                }
                            }
                        }

                        // 再装可拆分委托
                        for (List<Cargo> consignment : consignmentMap.values()) {
                            if (consignment.get(0).canSplit) {
                                for (Cargo cargo : consignment) {
                                    if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                                    if (!cargo.isOnePort && ship.addCargo(cargo)) {
                                        port.removeCargo(cargo);
//                                    System.out.println("【装载成功】初始可拆分: " + cargo);
                                    }
                                }
                            }
                        }

                        if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                    }
                }

                System.out.println("【装载调试】装载完成，船上货物: " + ship.loadedCargo.size() + " 个");
                return;
            }
        }

        /**
         * 多船多路径智能分配装载货物
         * @param shipsAtPort 当前港口所有待装载的船舶
         * @param port 当前港口
         * @param currentDate 当前日期
         */
        public void reloadShipsMultiPath(List<Ship> shipsAtPort, Port port, LocalDate currentDate) {
            // 1. 统计每条船的所有可达目的港的最短路径节点集合
            Map<Ship, Set<String>> shipPathMap = new HashMap<>();
            Map<String, Integer> nodeCount = new HashMap<>();
            for (Ship ship : shipsAtPort) {
                Set<String> pathNodes = new HashSet<>();
                for (Cargo cargo : port.cargoList) {
                    if (currentDate.isAfter(cargo.latestDeliveryDate)) continue;
                    List<String> path = routeGraph.getShortestPathSequence(ship.currentDestination, cargo.destinationPort);
                    if (path != null) pathNodes.addAll(path);
                }
                shipPathMap.put(ship, pathNodes);
                for (String node : pathNodes) {
                    nodeCount.put(node, nodeCount.getOrDefault(node, 0) + 1);
                }
            }
            // 2. 每条船优先装载独占路径节点上的货物
            for (Ship ship : shipsAtPort) {
                Set<String> uniqueNodes = shipPathMap.get(ship).stream()
                        .filter(node -> nodeCount.get(node) == 1)
                        .collect(Collectors.toSet());
                // 装载 uniqueNodes 上的货物
                List<Cargo> cargos = port.cargoList.stream()
                        .filter(c -> uniqueNodes.contains(c.destinationPort) && !currentDate.isAfter(c.latestDeliveryDate))
                        .collect(Collectors.toList());
                // 按委托分组，先装不可拆分，再装可拆分
                Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                for (Cargo cargo : cargos) {
                    consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                }
                // 不可拆分
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (!consignment.get(0).canSplit) {
                        double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                        int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                        boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                        if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                            for (Cargo cargo : consignment) {
                                if (ship.addCargo(cargo)) {
                                    port.removeCargo(cargo);
                                }
                            }
                        }
                    }
                }
                // 可拆分
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (consignment.get(0).canSplit) {
                        for (Cargo cargo : consignment) {
                            if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                            if (ship.addCargo(cargo)) {
                                port.removeCargo(cargo);
                            }
                        }
                    }
                }
            }
            // 3. 再装载多船都经过的路径节点上的货物
            for (Ship ship : shipsAtPort) {
                Set<String> sharedNodes = shipPathMap.get(ship).stream()
                        .filter(node -> nodeCount.get(node) > 1)
                        .collect(Collectors.toSet());
                List<Cargo> cargos = port.cargoList.stream()
                        .filter(c -> sharedNodes.contains(c.destinationPort) && !currentDate.isAfter(c.latestDeliveryDate))
                        .collect(Collectors.toList());
                Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                for (Cargo cargo : cargos) {
                    consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                }
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (!consignment.get(0).canSplit) {
                        double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                        int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                        boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                        if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                            for (Cargo cargo : consignment) {
                                if (ship.addCargo(cargo)) {
                                    port.removeCargo(cargo);
                                }
                            }
                        }
                    }
                }
                for (List<Cargo> consignment : consignmentMap.values()) {
                    if (consignment.get(0).canSplit) {
                        for (Cargo cargo : consignment) {
                            if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                            if (ship.addCargo(cargo)) {
                                port.removeCargo(cargo);
                            }
                        }
                    }
                }
            }
            // 4. 最后补货装满
            for (Ship ship : shipsAtPort) {
                if (ship.getRemainingWeightCapacity() > 0 && ship.getRemainingTEUCapacity() > 0) {
                    // 只要还有剩余运力，随便装
                    List<Cargo> cargos = port.cargoList.stream()
                            .filter(c -> !currentDate.isAfter(c.latestDeliveryDate))
                            .collect(Collectors.toList());
                    Map<String, List<Cargo>> consignmentMap = new HashMap<>();
                    for (Cargo cargo : cargos) {
                        consignmentMap.computeIfAbsent(cargo.consignmentId, k -> new ArrayList<>()).add(cargo);
                    }
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (!consignment.get(0).canSplit) {
                            double totalWeight = consignment.stream().mapToDouble(c -> c.weight).sum();
                            int totalTEU = consignment.stream().mapToInt(c -> c.containerType.equals("20ft") ? 1 : 2).sum();
                            boolean allValid = consignment.stream().allMatch(c -> !currentDate.isAfter(c.latestDeliveryDate));
                            if (totalWeight <= ship.getRemainingWeightCapacity() &&
                                    totalTEU <= ship.getRemainingTEUCapacity() && allValid) {
                                for (Cargo cargo : consignment) {
                                    if (ship.addCargo(cargo)) {
                                        port.removeCargo(cargo);
                                    }
                                }
                            }
                        }
                    }
                    for (List<Cargo> consignment : consignmentMap.values()) {
                        if (consignment.get(0).canSplit) {
                            for (Cargo cargo : consignment) {
                                if (ship.getRemainingWeightCapacity() <= 0 || ship.getRemainingTEUCapacity() <= 0) break;
                                if (ship.addCargo(cargo)) {
                                    port.removeCargo(cargo);
                                }
                            }
                        }
                    }
                }
            }
        }

        /**
         * 计算船上所有货物的最优卸货顺序（经过所有目的港，总时间最短）
         * 并输出每段的时间和总时间
         */
        public void printBestUnloadOrder(Ship ship, String startPort, LocalDateTime startTime) {
            // 1. 收集所有目的港
            Set<String> destPorts = ship.loadedCargo.stream()
                    .map(c -> c.destinationPort)
                    .collect(Collectors.toSet());
            if (destPorts.isEmpty()) {
                System.out.println("【航线规划】船上无货物，无需规划航线");
                return;
            }
            List<String> destList = destPorts != null ? new ArrayList<>(destPorts) : new ArrayList<>();

            List<String> bestOrder = null;
            int minTotalHours = Integer.MAX_VALUE;
            List<Integer> bestSegmentHours = null;

            // 2. 全排列
            List<List<String>> allOrders = new ArrayList<>();
            permute(destList, 0, allOrders);

            for (List<String> order : allOrders) {
                String curPort = startPort;
                int orderTotalHours = 0;
                boolean allOnTime = true;
                List<Integer> segmentHours = new ArrayList<>();
                for (String dest : order) {
                    Integer travelHours = routeGraph.shortestPath(curPort, "time").get(dest);
                    if (travelHours == null) { allOnTime = false; break; }
                    segmentHours.add(travelHours);
                    orderTotalHours += travelHours;
                    // 可加 deadline 检查
                    curPort = dest;
                }
                if (allOnTime && orderTotalHours < minTotalHours) {
                    minTotalHours = orderTotalHours;
                    bestOrder = order != null ? new ArrayList<>(order) : new ArrayList<>();
                    bestSegmentHours = segmentHours != null ? new ArrayList<>(segmentHours) : new ArrayList<>();
                }
            }
            if (bestOrder == null) {
                System.out.println("【航线规划】未找到可行的卸货顺序");
                return;
            }
            // 生成完整建议航线（含中转港口）
            List<String> fullRoute = new ArrayList<>();
            String curPort = startPort;
            for (String dest : bestOrder) {
                List<String> segment = routeGraph.getShortestPathSequence(curPort, dest);
                if (segment != null && !segment.isEmpty()) {
                    if (fullRoute.isEmpty()) {
                        fullRoute.addAll(segment);
                    } else {
                        // 避免重复添加起点
                        fullRoute.addAll(segment.subList(1, segment.size()));
                    }
                    curPort = dest;
                }
            }
            System.out.println("【航线规划】建议航线: " + fullRoute);

            // 计算每个目的港的预计到达（卸货完成）时间
            List<String> portArriveInfo = new ArrayList<>();
            String lastPort = startPort;
            LocalDateTime t = startTime;
            // 复制一份货物清单用于模拟
            List<Cargo> cargoSim = ship.loadedCargo != null ? new ArrayList<>(ship.loadedCargo) : new ArrayList<>();
            for (String dest : bestOrder) {
                // 航行到目的港
                Integer travelHours = routeGraph.shortestPath(lastPort, "time").get(dest);
                if (travelHours == null) travelHours = 120;
                t = t.plusHours(travelHours);
                // 统计要卸货的货物
                List<Cargo> toUnload = new ArrayList<>();
                for (Cargo c : cargoSim) {
                    if (c.destinationPort.equals(dest)) {
                        toUnload.add(c);
                    }
                }
                int unloadCount = toUnload.size();
                double unloadWeight = toUnload.stream().mapToDouble(c -> c.weight).sum();
                // 卸货作业时间
                double hourByBox = unloadCount * 1/20.0;
                double hourByWeight = unloadWeight * 1/20.0;
                int operationHours = (int)Math.ceil(Math.min(hourByBox, hourByWeight));
                if (operationHours < 1) operationHours = 1;
                t = t.plusHours(operationHours);
                portArriveInfo.add(dest + ": " + t);
                // 卸货
                cargoSim.removeIf(c -> c.destinationPort.equals(dest));
                lastPort = dest;
            }
            System.out.println("【航线规划】每段到达及卸货完成时间: " + portArriveInfo);
            System.out.println("【航线规划】建议卸货顺序: " + bestOrder);
            System.out.println("【航线规划】每段航行时间: " + bestSegmentHours);
            System.out.println("【航线规划】总航行时间: " + minTotalHours + " 小时");
        }

        // 工具：全排列
        private void permute(List<String> arr, int k, List<List<String>> result) {
            if (k == arr.size()) {
                result.add(new ArrayList<>(arr));
            } else {
                for (int i = k; i < arr.size(); i++) {
                    Collections.swap(arr, i, k);
                    permute(arr, k + 1, result);
                    Collections.swap(arr, i, k);
                }
            }
        }

        // 打印系统状态
        public void printSystemStatus() {
            System.out.println("\n===== 船舶状态 =====");

            Map<String, List<Cargo>> shipCargoMap = new HashMap<>();

            for (Ship ship : ships) {
                System.out.println(ship);
                System.out.println("  装载货物: " + ship.loadedCargo.size() + " 个");

                List<Cargo> cargos = ship.loadedCargo;
//            System.out.println("装载货物清单:" + JSONObject.toJSONString(cargos));
                List<String> target = cargos.stream().map(Cargo::getDestinationPort).distinct().collect(Collectors.toList());
                System.out.println("装载货物目的港:" + JSONObject.toJSONString(target));
                shipCargoMap.put(ship.name, ship.loadedCargo);
            }

            Map<String, List<Cargo>> portCargoMap = new HashMap<>();

            System.out.println("\n===== 港口货物 =====");
            for (Port port : ports.values()) {
                System.out.println("港口 " + port.name + ": " + port.cargoList.size() + " 个货物");
//            System.out.println("  货物清单:");
//            for (Cargo cargo : port.cargoList) {
//                System.out.println(cargo);
//            }
                List<Cargo> cargos = port.cargoList;

                List<Cargo> isOnePort = cargos.stream().filter(cargo -> cargo.isOnePort).collect(Collectors.toList());

                System.out.println("一港通货物数量:" + isOnePort.size());

//            System.out.println("一港通货物清单:" + JSONObject.toJSONString(isOnePort));

                List<String> start = cargos.stream().map(cargo -> cargo.originPort).distinct().collect(Collectors.toList());

//            System.out.println("起始港:" + JSONObject.toJSONString(start));

                List<String> target = cargos.stream().map(cargo -> cargo.destinationPort).distinct().collect(Collectors.toList());

//            System.out.println("目的港:" + JSONObject.toJSONString(target));

                //统计每个目的港的货物数量
                Map<String, Long> countByTarget = cargos.stream()
                        .collect(Collectors.groupingBy(Cargo::getDestinationPort, Collectors.counting()));
                System.out.println("目的港货物数量:" + JSONObject.toJSONString(countByTarget));

//            for (String portName:countByTarget.keySet()) {
//
//                //统计每个目的港的货物清单
//                List<Cargo> targetCargos = cargos.stream().filter(cargo -> cargo.destinationPort.equals(portName)).collect(Collectors.toList());
//                System.out.println("目的港口:" + portName);
//                for (Cargo cargo:targetCargos
//                     ) {
//                    System.out.println(cargo);
//                }
//
//            }

                portCargoMap.put(port.name, port.cargoList);
            }

        }
    }


    public static void main(String[] args) {
        ShipLoadingSystem system = new ShipLoadingSystem();
        // 创建港口
        Port shanghai = new Port("Shanghai");
        Port singapore = new Port("Singapore");
        Port rotterdam = new Port("Rotterdam");
        Port newyork = new Port("New York");
        Port tokyo = new Port("Tokyo");
        Port guangzhou = new Port("Guangzhou");
        system.addPort(shanghai);
        system.addPort(singapore);
        system.addPort(rotterdam);
        system.addPort(newyork);
        system.addPort(tokyo);
        system.addPort(guangzhou);
        // 添加航线（增加时间参数，假设时间单位为小时）
        system.addRoute("Rotterdam", "New York", 3000, 20);
        system.addRoute("Rotterdam", "Tokyo", 6100, 30);
        system.addRoute("New York", "Rotterdam", 3000, 20);
        system.addRoute("Tokyo", "Rotterdam", 6100, 30);
        system.addRoute("New York", "Tokyo", 3000, 20);
        system.addRoute("Tokyo", "New York", 3000, 20);
        system.addRoute("Shanghai", "Singapore", 3000, 20);

        system.addRoute("Guangzhou", "Shanghai", 1000, 10);
        system.addRoute("Shanghai","Guangzhou", 1000, 10);

        system.addRoute("Guangzhou", "Singapore", 1000, 9);
        system.addRoute("Singapore", "Guangzhou", 1000, 9);


        system.addRoute("Shanghai", "Rotterdam", 6100, 30);
        system.addRoute("Shanghai", "New York", 9200, 60);
        system.addRoute("Shanghai", "Tokyo", 12300, 120);
        system.addRoute("Singapore", "Shanghai", 3000, 20);
        system.addRoute("Rotterdam", "Shanghai", 6100, 30);
        system.addRoute("New York", "Shanghai", 9200, 60);
        system.addRoute("Tokyo", "Shanghai", 12300, 120);
        system.addRoute("Singapore", "Rotterdam", 3000, 20);
        system.addRoute("Singapore", "New York", 6100, 30);
        system.addRoute("Singapore", "Tokyo", 9200, 60);
        system.addRoute("Rotterdam", "Singapore", 3000, 20);
        system.addRoute("New York", "Singapore", 6100, 30);
        system.addRoute("Tokyo", "Singapore", 9200, 60);
        Map<String, Double> cargoLimits1 = new HashMap<>();
        cargoLimits1.put("General", 40000.00);
        cargoLimits1.put("Refrigerated", 30000.0);
        cargoLimits1.put("Dangerous", 10000.0);
        Ship ship11 = new Ship("Ever Given 11", 400.0, 59.0, 16.0, 80000.0, 20000, "Shanghai", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, true);
        Ship ship12 = new Ship("Ever Given 12", 400.0, 59.0, 16.0, 80000.0, 20000, "Shanghai", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship13 = new Ship("Ever Given 13", 400.0, 59.0, 16.0, 80000.0, 20000, "Shanghai", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);

        Ship ship21 = new Ship("Ever Given 21", 400.0, 59.0, 16.0, 80000.0, 20000, "Singapore", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, true);
        Ship ship22 = new Ship("Ever Given 22", 400.0, 59.0, 16.0, 80000.0, 20000, "Singapore", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship23 = new Ship("Ever Given 23", 400.0, 59.0, 16.0, 80000.0, 20000, "Singapore", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);

        Ship ship31 = new Ship("Ever Given 31", 400.0, 59.0, 16.0, 80000.0, 20000, "Rotterdam", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, true);
        Ship ship32 = new Ship("Ever Given 32", 400.0, 59.0, 16.0, 80000.0, 20000, "Rotterdam", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship33 = new Ship("Ever Given 33", 400.0, 59.0, 16.0, 80000.0, 20000, "Rotterdam", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);

        Ship ship41 = new Ship("Ever Given 41", 400.0, 59.0, 16.0, 80000.0, 20000, "New York", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, true);
        Ship ship42 = new Ship("Ever Given 42", 400.0, 59.0, 16.0, 80000.0, 20000, "New York", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship43 = new Ship("Ever Given 43", 400.0, 59.0, 16.0, 80000.0, 20000, "New York", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);

        Ship ship51 = new Ship("Ever Given 51", 400.0, 59.0, 16.0, 80000.0, 20000, "Tokyo", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, true);
        Ship ship52 = new Ship("Ever Given 52", 400.0, 59.0, 16.0, 80000.0, 20000, "Tokyo", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);
        Ship ship53 = new Ship("Ever Given 53", 400.0, 59.0, 16.0, 80000.0, 20000, "Tokyo", LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0)), cargoLimits1, false);

        system.addShip(ship11);
        system.addShip(ship12);
        system.addShip(ship13);
        system.addShip(ship21);
        system.addShip(ship22);
        system.addShip(ship23);
        system.addShip(ship31);
        system.addShip(ship32);
        system.addShip(ship33);
        system.addShip(ship41);
        system.addShip(ship42);
        system.addShip(ship43);
        system.addShip(ship51);
        system.addShip(ship52);
        system.addShip(ship53);

        // 小时级主循环
        LocalDateTime currentDateTime = LocalDateTime.of(LocalDate.now(), java.time.LocalTime.of(0,0));

        for (Port port : system.ports.values()) {
            for (int i = 0; i < 300; i++) {
                generateRandomCargo(port, currentDateTime.toLocalDate().minusDays(1));
            }
        }

        int totalHours = 24 * 30; // 30天
        for (int hour = 0; hour < totalHours; hour++) {
            System.out.println("\n==== 当前时间: " + currentDateTime + " ====\n");
            // 1. 到港且未作业的船，开始装卸
            for (Ship ship : system.ships) {
                if (ship.estimatedArrival != null && !ship.estimatedArrival.isAfter(currentDateTime) && ship.operationFinishTime == null) {
                    // 卸货
                    List<Cargo> unloaded = ship.unloadCargo(ship.currentDestination, true);
                    System.out.println(ship.name + "卸货数量：" + unloaded.size());
                    // 调用智能配载
                    system.reloadShip(ship, system.ports.get(ship.currentDestination), currentDateTime.toLocalDate());
                    System.out.println(ship.name + "装载货物数量：" + ship.loadedCargo.size());
                    if (!ship.loadedCargo.isEmpty()) {
//                        System.out.println(ship.name + "装货清单:");
//                        for (Cargo cargo : ship.loadedCargo) {
//                            System.out.println("  " + cargo);
//                        }
                        List<Cargo> loaded = ship.loadedCargo;



                    }
                    // 计算本次实际装卸的箱数/重量
                    int unloadCount = unloaded.size();
                    double unloadWeight = unloaded.stream().mapToDouble(c -> c.weight).sum();
                    int loadCount = ship.loadedCargo.size();
                    double loadWeight = ship.loadedCargo.stream().mapToDouble(c -> c.weight).sum();
                    int totalCount = unloadCount + loadCount;
                    double totalWeight = unloadWeight + loadWeight;
                    double hourByBox = totalCount * 1/20;
                    double hourByWeight = totalWeight * 1/20;
                    int operationHours = (int)Math.ceil(Math.min(hourByBox, hourByWeight));
                    if (operationHours < 1) operationHours = 1;
                    ship.operationFinishTime = currentDateTime.plusHours(operationHours);
                    System.out.println("[作业] " + ship.name + " 在 " + ship.currentDestination + " 开始装卸作业，预计耗时 " + operationHours + " 小时，完成时间: " + ship.operationFinishTime);
                    system.printBestUnloadOrder(ship,ship.currentDestination, ship.operationFinishTime);
                }
            }
            // 2. 作业完成的船，开始运输
            for (Ship ship : system.ships) {
                if (ship.operationFinishTime != null && !ship.operationFinishTime.isAfter(currentDateTime)) {
                    // 调度下一目的港
                    if (ship.loadedCargo != null && !ship.loadedCargo.isEmpty()) {
                        // 船上有货，优先以货物目的港为下一目的港
                        Set<String> destSet = new HashSet<>();
                        for (Cargo cargo : ship.loadedCargo) {
                            destSet.add(cargo.destinationPort);
                        }
                        // 获取最优卸货顺序
                        List<String> destList = new ArrayList<>(destSet);
                        List<List<String>> allOrders = new ArrayList<>();
                        // 全排列
                        ShipLoadingSystemDemo.permute(destList, 0, allOrders);
                        List<String> bestOrder = null;
                        int minTotalHours = Integer.MAX_VALUE;
                        for (List<String> order : allOrders) {
                            String curPort = ship.currentDestination;
                            int orderTotalHours = 0;
                            boolean allOnTime = true;
                            LocalDateTime t = currentDateTime;
                            for (String dest : order) {
                                Integer travelHours = system.routeGraph.shortestPath(curPort, "time").get(dest);
                                if (travelHours == null) { allOnTime = false; break; }
                                t = t.plusHours(travelHours);
                                orderTotalHours += travelHours;
                                // 检查所有去dest的货物
                                for (Cargo c : ship.loadedCargo) {
                                    if (c.destinationPort.equals(dest)) {
                                        if (t.toLocalDate().isAfter(c.deadline)) {
                                            allOnTime = false;
                                            break;
                                        }
                                    }
                                }
                                if (!allOnTime) break;
                                curPort = dest;
                            }
                            if (allOnTime && orderTotalHours < minTotalHours) {
                                minTotalHours = orderTotalHours;
                                bestOrder = new ArrayList<>(order);
                            }
                        }
                        // 选择第一个目的港为下一目的港
                        String nextDest;
                        if (bestOrder != null) {
                            nextDest = bestOrder.get(0);
                        } else {
                            nextDest = destSet.stream().skip(random.nextInt(destSet.size())).findFirst().orElse(ship.currentDestination);
                        }
                        // 获取完整路径（含中转港）
                        List<String> path = system.routeGraph.getShortestPathSequence(ship.currentDestination, nextDest);
                        if (path == null) path = new ArrayList<>();
                        if (path == null || path.size() < 2) {
                            // 直接到目的港
                            Map<String, Integer> timeMap = system.routeGraph.shortestPath(ship.currentDestination, "time");
                            int travelHours = timeMap.getOrDefault(nextDest, 120);
                            System.out.println("[运输] " + ship.name + " 从 " + ship.currentDestination + " 出发前往 " + nextDest + "，航行时间: " + travelHours + " 小时，预计到达: " + currentDateTime.plusHours(travelHours));
                            ship.currentDestination = nextDest;
                            ship.estimatedArrival = currentDateTime.plusHours(travelHours);
                        } else {
                            // 依次经过中转港口
                            String curPort = ship.currentDestination;
                            LocalDateTime t = currentDateTime;
                            for (int i = 1; i < path.size(); i++) {
                                String midPort = path.get(i);
                                Integer travelHours = system.routeGraph.shortestPath(curPort, "time").get(midPort);
                                if (travelHours == null) travelHours = 120;
                                t = t.plusHours(travelHours);
                                System.out.println("[运输] " + ship.name + " 从 " + curPort + " 出发前往 " + midPort + "，航行时间: " + travelHours + " 小时，预计到达: " + t);
                                curPort = midPort;
                                // 只在中转港口（非终点）装货
                                if (i < path.size() - 1) {
                                    // 记录装货前后数量和重量
                                    int beforeCount = ship.loadedCargo.size();
                                    double beforeWeight = ship.currentCargoWeight;
                                    // 备份船货物状态
                                    List<Cargo> backupCargo = new ArrayList<>(ship.loadedCargo);
                                    double backupWeight = ship.currentCargoWeight;
                                    system.reloadShip(ship, system.ports.get(midPort), t.toLocalDate());
                                    int afterCount = ship.loadedCargo.size();
                                    double afterWeight = ship.currentCargoWeight;
                                    int loadCount = afterCount - beforeCount;
                                    double loadWeight = afterWeight - beforeWeight;
                                    // 计算作业时间
                                    double hourByBox = loadCount * 1/20.0;
                                    double hourByWeight = loadWeight * 1/20.0;
                                    int operationHours = (int)Math.ceil(Math.min(hourByBox, hourByWeight));
                                    if (operationHours < 1) operationHours = 1;
                                    t = t.plusHours(operationHours); // 等待作业完成
                                    System.out.println("[中转港作业] " + ship.name + " 在 " + midPort + " 装货 " + loadCount + " 个，作业耗时 " + operationHours + " 小时，完成时间: " + t);
                                    // 校验：装货后所有货物的deadline是否满足（模拟后续所有目的港的到达与卸货）
                                    boolean allValid = true;
                                    if (bestOrder != null) {
                                        // 复制一份货物清单用于模拟
                                        List<Cargo> cargoSim = new ArrayList<>(ship.loadedCargo);
                                        String simLastPort = midPort;
                                        LocalDateTime simTime = t;
                                        for (String dest : bestOrder) {
                                            // 航行到目的港
                                            Integer simTravel = system.routeGraph.shortestPath(simLastPort, "time").get(dest);
                                            if (simTravel == null) simTravel = 120;
                                            simTime = simTime.plusHours(simTravel);
                                            // 统计要卸货的货物
                                            List<Cargo> toUnload = new ArrayList<>();
                                            for (Cargo c : cargoSim) {
                                                if (c.destinationPort.equals(dest)) {
                                                    toUnload.add(c);
                                                }
                                            }
                                            int unloadCountSim = toUnload.size();
                                            double unloadWeightSim = toUnload.stream().mapToDouble(c -> c.weight).sum();
                                            // 卸货作业时间
                                            double hourByBoxSim = unloadCountSim * 1/20.0;
                                            double hourByWeightSim = unloadWeightSim * 1/20.0;
                                            int opHourSim = (int)Math.ceil(Math.min(hourByBoxSim, hourByWeightSim));
                                            if (opHourSim < 1) opHourSim = 1;
                                            simTime = simTime.plusHours(opHourSim);
                                            // 检查deadline
                                            for (Cargo c : toUnload) {
                                                if (simTime.toLocalDate().isAfter(c.deadline)) {
                                                    allValid = false;
                                                    break;
                                                }
                                            }
                                            if (!allValid) break;
                                            // 卸货
                                            cargoSim.removeIf(c -> c.destinationPort.equals(dest));
                                            simLastPort = dest;
                                        }
                                    }
                                    if (!allValid) {
                                        // 回滚装货
                                        ship.loadedCargo.clear();
                                        ship.loadedCargo.addAll(backupCargo);
                                        ship.currentCargoWeight = backupWeight;
                                        System.out.println("[中转港装货回滚] " + ship.name + " 在 " + midPort + " 装货后会导致超时，已跳过装货");
                                    }
                                } else {
                                    // 最后到达目的港
                                    ship.currentDestination = midPort;
                                    ship.estimatedArrival = t;
                                }
                            }
                        }
                    } else {
                        // 船上没货，停留在港口
                        System.out.println("[调度] " + ship.name + " 暂无货物，停留在 " + ship.currentDestination);
                        ship.estimatedArrival = null;
                    }
                    ship.operationFinishTime = null;

                }
            }
            // 3. 每小时为每个港口生成新货物（可按需调整频率）
            if (hour % 24 == 0) { // 每天生成
                for (Port port : system.ports.values()) {
                    for (int i = 0; i < 300; i++) {
                        generateRandomCargo(port, currentDateTime.toLocalDate());
                    }
                }
            }

            // === 新增：让所有停留在港口的空船尝试装货 ===
            for (Ship ship : system.ships) {
                if (ship.estimatedArrival == null && ship.operationFinishTime == null && (ship.loadedCargo == null || ship.loadedCargo.isEmpty())) {
                    system.reloadShip(ship, system.ports.get(ship.currentDestination), currentDateTime.toLocalDate());
                    if (ship.loadedCargo != null && !ship.loadedCargo.isEmpty()) {
                        ShipLoadingSystemDemo.planNextDestination(ship, system, currentDateTime.toLocalDate());
                    }
                }
            }
            // 4. 检查所有港口货物是否超时
            for (Port port : system.ports.values()) {
                List<Cargo> overTimeCargo = new ArrayList<>();
                for (Cargo cargo : new ArrayList<>(port.cargoList)) {
                    if (cargo.deadline.isBefore(currentDateTime.toLocalDate())) {
//                        System.out.println("[超时警告] 港口 " + port.name + " 有超时货物: " + cargo);
                        overTimeCargo.add(cargo);
                    }
                }

                System.out.println("[超时货物] 港口 " + port.name + " 有 " + overTimeCargo.size() + " 超deadline货物");

            }
            // 5. 输出当天系统状态
            if (currentDateTime.getHour() == 0) {
                system.printSystemStatus();
            }
            currentDateTime = currentDateTime.plusHours(1);
        }
        // 让所有停留在港口的空船尝试装货
        for (Ship ship : system.ships) {
            if (ship.estimatedArrival == null && ship.operationFinishTime == null && ship.loadedCargo.isEmpty()) {
                system.reloadShip(ship, system.ports.get(ship.currentDestination), currentDateTime.toLocalDate());
                // 如果装上货物，分配目的港和到达时间
                if (!ship.loadedCargo.isEmpty()) {
                    ShipLoadingSystemDemo.planNextDestination(ship, system, currentDateTime.toLocalDate());
                }
            }
        }
    }
}