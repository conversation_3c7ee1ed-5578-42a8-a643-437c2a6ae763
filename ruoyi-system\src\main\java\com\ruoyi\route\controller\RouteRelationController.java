package com.ruoyi.route.controller;

import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.route.domain.RouteRelation;
import com.ruoyi.route.service.IRouteRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 码头连接关系Controller
 */
@Slf4j
@RestController
@RequestMapping("/route/relation")
public class RouteRelationController extends BaseController {
    @Autowired
    private IRouteRelationService routeRelationService;

    /**
     * 查询码头连接关系列表
     */
    @PreAuthorize("@ss.hasPermi('route:relation:list')")
    @GetMapping("/page")
    public AjaxResult page(@RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            RouteRelation routeRelation) {
        log.debug("分页查询码头连接关系 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, routeRelation);
        Page<RouteRelation> page = routeRelationService.selectRouteRelationPage(pageNumber, pageSize, routeRelation);
        log.debug("分页查询码头连接关系完成 - 总记录数: {}", page.getTotalRow());
        return AjaxResult.success(page);
    }

    /**
     * 导出码头连接关系列表
     */
    @PreAuthorize("@ss.hasPermi('route:relation:export')")
    @Log(title = "码头连接关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RouteRelation routeRelation) {
        log.debug("导出码头连接关系列表 - 查询条件: {}", routeRelation);
        List<RouteRelation> list = routeRelationService.selectRouteRelationList(routeRelation);
        ExcelUtil<RouteRelation> util = new ExcelUtil<>(RouteRelation.class);
        util.exportExcel(response, list, "码头连接关系数据");
    }

    /**
     * 获取码头连接关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('route:relation:query')")
    @GetMapping(value = "/{relationId}")
    public AjaxResult getInfo(@PathVariable("relationId") String relationId) {
        log.debug("获取码头连接关系详细信息 - 关系ID: {}", relationId);
        return success(routeRelationService.getById(relationId));
    }

    /**
     * 新增码头连接关系
     */
    @PreAuthorize("@ss.hasPermi('route:relation:add')")
    @Log(title = "码头连接关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RouteRelation routeRelation) {
        log.debug("新增码头连接关系 - 关系信息: {}", routeRelation);
        return toAjax(routeRelationService.save(routeRelation));
    }

    /**
     * 修改码头连接关系
     */
    @PreAuthorize("@ss.hasPermi('route:relation:edit')")
    @Log(title = "码头连接关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RouteRelation routeRelation) {
        log.debug("修改码头连接关系 - 关系信息: {}", routeRelation);
        return toAjax(routeRelationService.updateById(routeRelation));
    }

    /**
     * 删除码头连接关系
     */
    @PreAuthorize("@ss.hasPermi('route:relation:remove')")
    @Log(title = "码头连接关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{relationIds}")
    public AjaxResult remove(@PathVariable String[] relationIds) {
        log.debug("删除码头连接关系 - 关系ID列表: {}", (Object) relationIds);
        return toAjax(routeRelationService.removeByIds(Arrays.asList(relationIds)));
    }
} 