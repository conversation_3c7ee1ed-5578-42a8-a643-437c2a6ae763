package com.ruoyi.contract.domain;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.mybatisflex.annotation.Column;
import java.util.List;

/**
 * 合同流向 实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "contract_flow")
public class ContractFlow extends BaseEntity {
    
    /**
     * 流向ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String flowId;
    
    /**
     * 合同ID
     */
    @NotEmpty(message = "合同ID不能为空")
    private String contractId;
    
    /**
     * 流向名称
     */
    @NotEmpty(message = "流向名称不能为空")
    private String flowName;
    
    /**
     * 流向描述
     */
    private String flowDesc;
    
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 流向码头列表
     */
    @Column(ignore = true)
    private List<ContractFlowTerminal> terminals;
} 