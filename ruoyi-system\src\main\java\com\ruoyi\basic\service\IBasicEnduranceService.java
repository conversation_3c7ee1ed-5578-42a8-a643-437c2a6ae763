package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicEndurance;

import java.util.List;

public interface IBasicEnduranceService extends IService<BasicEndurance> {

    /**
     * 查询航时信息
     *
     * @param id 航时信息主键
     * @return 航时信息
     */
    public BasicEndurance selectBasicEnduranceById(String id);

    /**
     * 查询航时信息列表
     *
     * @param basicEndurance 航时信息
     * @return 航时信息集合
     */
    public List<BasicEndurance> selectBasicEnduranceList(BasicEndurance basicEndurance);

    /**
     * 新增航时信息
     *
     * @param basicEndurance 航时信息
     * @return 结果
     */
    public int insertBasicEndurance(BasicEndurance basicEndurance) throws Exception;

    /**
     * 修改航时信息
     *
     * @param basicEndurance 航时信息
     * @return 结果
     */
    public int updateBasicEndurance(BasicEndurance basicEndurance) throws Exception;

    /**
     * 批量删除航时信息
     *
     * @param ids 需要删除的航时信息主键集合
     * @return 结果
     */
    public int deleteBasicEnduranceByIds(List<String> ids);

    /**
     * 删除航时信息信息
     *
     * @param id 航时信息主键
     * @return 结果
     */
    public int deleteBasicEnduranceById(String id);

}
