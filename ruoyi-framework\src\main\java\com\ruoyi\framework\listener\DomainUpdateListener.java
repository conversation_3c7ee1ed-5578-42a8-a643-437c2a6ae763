package com.ruoyi.framework.listener;

import com.mybatisflex.annotation.UpdateListener;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.SecurityUtils;

import java.util.Date;

public class DomainUpdateListener<T extends BaseEntity> implements UpdateListener {
    /**
     * 实体类执行更新时，自动赋值更新者和更新时间
     * @param entity
     */
    @SuppressWarnings("unchecked")
    @Override
    public void onUpdate(Object entity) {
        if(entity instanceof BaseEntity) {
            T t = (T) entity;
            t.setUpdateBy(SecurityUtils.getUsername());
            t.setUpdateTime(new Date());
        }
    }
}
