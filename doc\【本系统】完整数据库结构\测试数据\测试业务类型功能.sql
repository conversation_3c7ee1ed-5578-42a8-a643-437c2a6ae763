-- ----------------------------
-- 测试业务类型功能
-- ----------------------------

-- 1. 首先确保字典数据已插入
-- 执行 CUS_BUSINESS_TYPE_DICT.sql

-- 2. 确保表已创建
-- 执行 CUS_BUSINESS_TYPE.sql

-- 3. 测试插入业务类型数据
INSERT INTO "SHIPPING"."CUS_BUSINESS_TYPE" ("CUS_MAIN_ID", "CUS_BUSINESS_TYPE") 
VALUES ('test_cus_id_001', 'container');

INSERT INTO "SHIPPING"."CUS_BUSINESS_TYPE" ("CUS_MAIN_ID", "CUS_BUSINESS_TYPE") 
VALUES ('test_cus_id_001', 'bulk');

INSERT INTO "SHIPPING"."CUS_BUSINESS_TYPE" ("CUS_MAIN_ID", "CUS_BUSINESS_TYPE") 
VALUES ('test_cus_id_002', 'dangerous');

-- 4. 查询测试数据
SELECT 
    cm.CUS_ID,
    cm.CUS_NAME,
    cm.CUS_ABBREVIATION,
    cbt.CUS_BUSINESS_TYPE
FROM "SHIPPING"."CUS_MAIN" cm
LEFT JOIN "SHIPPING"."CUS_BUSINESS_TYPE" cbt ON cm.CUS_ID = cbt.CUS_MAIN_ID
WHERE cm.DEL_FLAG = '0'
ORDER BY cm.CUS_ID, cbt.CUS_BUSINESS_TYPE;

-- 5. 清理测试数据（可选）
-- DELETE FROM "SHIPPING"."CUS_BUSINESS_TYPE" WHERE CUS_MAIN_ID IN ('test_cus_id_001', 'test_cus_id_002'); 