package com.ruoyi.common.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class RsaUtils {

    public static String publicKey = "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAqsGqrSitCxUiBDQFypHM\n" +
            "EllLUocnewixkXbHaYgmWepmOKDmYpxN9smCS2nKSPjH5jMBcjaKB2bYpkJ2sym+\n" +
            "mwvt993YZ0KhlsFzqUNMMdk+v5LKAE475qvUAa4Mlw/8tO2RYImsxXm37y8cFdGm\n" +
            "bhhes0/t+cMawHe3fSVfkiH1aGvls/u6cqHBvSAIY49nsOWGTvSretBTj2ejEqpU\n" +
            "YRs5gV9GWrxMisx6zIJepBTJBoS3EqyTox8AGZ7z1meOxfe0zX71B5WlfnB93JWa\n" +
            "ZRk185LhBdLWDjbGvifiD45Q5CgRmze+HLNDZ/ayHrsonlcDSltx+xRLlHIEIiee\n" +
            "HEkNzbT/j+4fAAAtVcl7/kGgl4uWZiAXRGrUd+GJzhbM5Trh0awgjv39SNkWCRZI\n" +
            "8UknG4z6IVJ86+MSrLG94hSUsYwL7wM7V6EJP2xW59gCAfJkUyEDXVh7QT7es6T9\n" +
            "lwCK+hy3ArhtZYeo3SO2xtD2Z3wMXHg8sdmLcvEH2Eh4S5pFpXDqzumfy8CAZ4QU\n" +
            "VJLHTvLVCndJvpBgpUzKtqRqS4P18pR6ylNZuRlbbb4hjnlfk2tMhEXjnKZ3NcRM\n" +
            "mVkFFJe++wtP49NsZT2tfKvE0mQSQZICGmsl3ScJGS0zUogIMSwT6t6pYTikOAA6\n" +
            "gNe9FosxYUE2f0eNk60BASECAwEAAQ==";

    // Rsa 私钥 登录用
    public static String privateKey = "MIIJRAIBADANBgkqhkiG9w0BAQEFAASCCS4wggkqAgEAAoICAQCqwaqtKK0LFSIE\n" +
            "NAXKkcwSWUtShyd7CLGRdsdpiCZZ6mY4oOZinE32yYJLacpI+MfmMwFyNooHZtim\n" +
            "QnazKb6bC+333dhnQqGWwXOpQ0wx2T6/ksoATjvmq9QBrgyXD/y07ZFgiazFebfv\n" +
            "LxwV0aZuGF6zT+35wxrAd7d9JV+SIfVoa+Wz+7pyocG9IAhjj2ew5YZO9Kt60FOP\n" +
            "Z6MSqlRhGzmBX0ZavEyKzHrMgl6kFMkGhLcSrJOjHwAZnvPWZ47F97TNfvUHlaV+\n" +
            "cH3clZplGTXzkuEF0tYONsa+J+IPjlDkKBGbN74cs0Nn9rIeuyieVwNKW3H7FEuU\n" +
            "cgQiJ54cSQ3NtP+P7h8AAC1VyXv+QaCXi5ZmIBdEatR34YnOFszlOuHRrCCO/f1I\n" +
            "2RYJFkjxSScbjPohUnzr4xKssb3iFJSxjAvvAztXoQk/bFbn2AIB8mRTIQNdWHtB\n" +
            "Pt6zpP2XAIr6HLcCuG1lh6jdI7bG0PZnfAxceDyx2Yty8QfYSHhLmkWlcOrO6Z/L\n" +
            "wIBnhBRUksdO8tUKd0m+kGClTMq2pGpLg/XylHrKU1m5GVttviGOeV+Ta0yEReOc\n" +
            "pnc1xEyZWQUUl777C0/j02xlPa18q8TSZBJBkgIaayXdJwkZLTNSiAgxLBPq3qlh\n" +
            "OKQ4ADqA170WizFhQTZ/R42TrQEBIQIDAQABAoICAA9Qq3Q4b7zMum5SFCrlBL6o\n" +
            "j57pTFs2nmaDlsMaWQY7hcuSctdpWXzG+g61gFUnKxa0gG60AZFEYi4XS/opxlS1\n" +
            "rzRKC3zHdyLEWxFVXeIisAQSsFdyrOC1jKBR8gMihfHcVOuprXtQvXSQbx5t3VWG\n" +
            "jgfbJWb/Xsjgc+6fiYDb1tz0wHdyHnjllTDXTKlRVWWCUJeITlEHmlcEXpXv/P1w\n" +
            "qam21+KPfPX9/Q2Wq9JfouAYyyq7m8pHn4SswwX5585Nt0OvHV7+xwceI8vDB0QG\n" +
            "DNa518Dj9hr0QsMPZLEsfIjaJyoax8xfL66Fa9hhxPTerQbgSMrSOS0pRMGjeAS/\n" +
            "6VBguqH0wHnOAqX0CvDjZy+XjBvxzSLyWTthW54sX/n+WjVTG7TmuVvSbNcXeV1G\n" +
            "x4XVkXbm3f+ETgGd852sZB7ixhcOa5ikPZIv2FQDXqUKFF19xCuB9vqxGh4g1C1z\n" +
            "Ri0gXl98GFI7/VX+0OTOaN8NybUGXnBtp8Ri1NLZUtDcvCtxfUqildA26w2ePPFH\n" +
            "MKAUgBv+0TdtSrXw/KWfqusZBgsdoOl+pIQICRuZfSClp9zp1BefNiH9MCkjr1I3\n" +
            "Ru46Yh3HRVESm83JhcjtpTJ3giloYQasGwgcB1+nffVIHl44bsE0tky7qnY5w2+i\n" +
            "GxquOFT4gn9YK95YUaUhAoIBAQDYpDk7AukcBmmW3z30/jUVoSsv4oEaExJ1dMLN\n" +
            "PTnFiKPHX4JnNq74OjVEUl6PRoy6b07pGJl3aZUxRD9/YBP/jfwvMYDWMlXoQebA\n" +
            "uq3rM9Mpk0RWGHgQ58EXAgoFUyv6VxFHw6p7NHcVUm8AxwsUH74EIt86aXM3w7p0\n" +
            "RxMRbq4nDtnibzTtJVCTlEZMaCnKGDRLx1NxEnqpuCyX/h2M9XEC+xgiBtBqxBGw\n" +
            "JypuzfyKlgjBsjIIhnroX27kJHlbeyQgsgBqIvRE5Tyet2S/iRgRySK0s1cq9McP\n" +
            "SfwfFuy6oJByE92IgJpK6v1NjlyMt7Pj8vuapHfsSQqySiUlAoIBAQDJx2D+85Ac\n" +
            "/LCbzk/7eabAKMEqgHAiGqKxr5gMPfdbBAsrueV3kD6DGBEQgstaIXTW+tfUQSah\n" +
            "+6En2omYOZX8jw70SQ94nQm+FTL6xAoH3cGyt9DxWGrkV84yoK5fiyKqEAFI0kBa\n" +
            "JkbYGCa0RmsyRPWn9ejmOK/GyoGV356IwZJR+3ggFudA1f48XyvgBWIW6HNc61/K\n" +
            "2E+NBYhPpTNAuGnLDlvPvZ4erQ5B3tRQFCbvM1lJqDHd/r7SS2pgnaIZMCf6/7tr\n" +
            "H4G4pfn2WIB5E2TBDhxm6e/i1CLwIvLyKtLERio6yQSP7lqoxZpULceL+AYtg8R4\n" +
            "g+yWGPXaDPFNAoIBAQCFWklUNs1xD81tLkvAA75/au7fkehwOW8yV8YVeeuW2S4f\n" +
            "an4ltHZtm77wKFfGj+HOXPfQ6fCFnbtVUTJY4LZOp4KlNi6dKuzQg8hbuE2YKxV/\n" +
            "UQROjs9RcKbd18Z9Up4OggvEBgBpyHjI6i4j52G9aHCZsTGBjil7GQMr0hg9sciH\n" +
            "iE+8X0HP/BkmTq4bGD8pBE8kmjSVtI9G63Sftq9P9kEzWYaYY2NlZP2SVfl7rVYX\n" +
            "pJUNTfgYYVdEtIHY8060bg9mBMvWiV+dDRKMjEz6zZPAVvQDESXF7lid46fLJ1xe\n" +
            "4LkpT2sdPDQ9i3qSg+AaSnX0Cbywiy66hpeL+bH1AoIBAQC/OXm+BNuV7Q/+iJaa\n" +
            "L58NS1JNUi1y1KVNGalSKMOr+PgRN3q1RflISd2aNSo8v6o4Nr+mQxw2tP70gxKV\n" +
            "NjXjhTxZ3IeR+dmK0BJhPrp22/0+vx+AKhgbUO90YXz/xR9T5hkhh0g/ZzkKgFDl\n" +
            "1jqvCFV7GPzgeeZz0eTfQW7AEhr2IQil+K6ubl8jndo500Azj5YNAG/nD/rMHesh\n" +
            "9+DLvDlem7v886nPTphNarzxKzf+xz4eJQ0lYtA7AgfNinZdwpqRMBzRF6nbcUij\n" +
            "4xnWJL/+ib329ktGP17hD24IfPcD1a8dyPXO5cf7XFG25y/OgX5xUgg3YmcrlHoi\n" +
            "bNOdAoIBAQCD9Hac/A9fDE6f5rvoFqzl/47+SDbS9AcTHbLLCvfwzJ0lg2QuHU9J\n" +
            "Q9QCSZKFDsy7xwtykhaTtOsueB3f124wi6g2ztJsTVP43uTyINHq4wZAuQ57uLNq\n" +
            "Zem24s/6kGC44cJU8sXj82wrGVA0ucFoIk7cWVtRu8qbrfqv+VVTqSzyJQTYw/Ay\n" +
            "t+G+ZF3H4Y0hHLmcJNnuCaS42YyD/F4lMxixn0mOp1OWcOM330JOZr2w95D1PPTg\n" +
            "SbWBibeVoIk1SqNob3BtIR7pHXFO6kBtpwar0MncDXxVYSmzUOJqJ5m2gnjstFXO\n" +
            "f3VE1CU/dmIy6OoDFpkpbnjng2uoL2tG";

    /**
     * 私钥解密
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String text) throws Exception
    {
        return decryptByPrivateKey(privateKey, text);
    }

    /**
     * 公钥解密
     *
     * @param publicKeyString 公钥
     * @param text 待解密的信息
     * @return 解密后的文本
     */
    public static String decryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyString 私钥
     * @param text 待加密的信息
     * @return 加密后的文本
     */
    public static String encryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 私钥解密
     *
     * @param privateKeyString 私钥
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 公钥加密
     *
     * @param publicKeyString 公钥
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    public static String encryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 构建RSA密钥对
     *
     * @return 生成后的公私钥信息
     */
    public static RsaKeyPair generateKeyPair() throws NoSuchAlgorithmException
    {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        return new RsaKeyPair(publicKeyString, privateKeyString);
    }

    /**
     * RSA密钥对对象
     */
    public static class RsaKeyPair
    {
        private final String publicKey;
        private final String privateKey;

        public RsaKeyPair(String publicKey, String privateKey)
        {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey()
        {
            return publicKey;
        }

        public String getPrivateKey()
        {
            return privateKey;
        }
    }
}
