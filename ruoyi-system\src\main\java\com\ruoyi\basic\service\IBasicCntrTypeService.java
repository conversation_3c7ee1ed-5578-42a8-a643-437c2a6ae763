package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCntrType;

import java.util.List;

public interface IBasicCntrTypeService extends IService<BasicCntrType> {

    /**
     * 查询箱型
     *
     * @param id 箱型主键
     * @return 箱型
     */
    public BasicCntrType selectBasicCntrTypeById(String id);

    /**
     * 查询箱型列表
     *
     * @param basicCntrType 箱型
     * @return 箱型集合
     */
    public List<BasicCntrType> selectBasicCntrTypeList(BasicCntrType basicCntrType);

    /**
     * 新增箱型
     *
     * @param basicCntrType 箱型
     * @return 结果
     */
    public int insertBasicCntrType(BasicCntrType basicCntrType);

    /**
     * 修改箱型
     *
     * @param basicCntrType 箱型
     * @return 结果
     */
    public int updateBasicCntrType(BasicCntrType basicCntrType);

    /**
     * 批量删除箱型
     *
     * @param ids 需要删除的箱型主键集合
     * @return 结果
     */
    public int deleteBasicCntrTypeByIds(List<String> ids);

    /**
     * 删除箱型信息
     *
     * @param id 箱型主键
     * @return 结果
     */
    public int deleteBasicCntrTypeById(String id);

}
