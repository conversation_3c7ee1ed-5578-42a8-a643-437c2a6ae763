<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.customer.mapper.CusMainMapper">

    <resultMap id="CusMainResult" type="com.ruoyi.customer.domain.CusMain">
        <id column="cus_id" property="cusId"/>
        <result column="cus_name" property="cusName"/>
        <result column="cus_abbreviation" property="cusAbbreviation"/>
        <result column="cus_code" property="cusCode"/>
        <result column="cus_credit_code" property="cusCreditCode"/>
        <result column="cus_identity" property="cusIdentity"/>
        <result column="status" property="status"/>
        <result column="industry" property="industry"/>
        <result column="legal_person_name" property="legalPersonName"/>
        <result column="reg_status" property="regStatus"/>
        <result column="reg_capital" property="regCapital"/>
        <result column="establish_time" property="establishTime"/>
        <result column="approved_time" property="approvedTime"/>
        <result column="from_time" property="fromTime"/>
        <result column="staff_num_range" property="staffNumRange"/>
        <result column="social_staff_num" property="socialStaffNum"/>
        <result column="reg_institute" property="regInstitute"/>
        <result column="history_name" property="historyName"/>
        <result column="reg_location" property="regLocation"/>
        <result column="business_scope" property="businessScope"/>
        <result column="invoice_header" property="invoiceHeader"/>
        <result column="invoice_number" property="invoiceNumber"/>
        <result column="invoice_address" property="invoiceAddress"/>
        <result column="invoice_phone" property="invoicePhone"/>
        <result column="invoice_bank" property="invoiceBank"/>
        <result column="invoice_bank_account" property="invoiceBankAccount"/>
        <result column="invoice_receive_address" property="invoiceReceiveAddress"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="del_flag" property="delFlag"/>
        <result column="container_owner" property="containerOwner"/>
        <collection property="cusBusinessTypes" javaType="java.util.List" resultMap="CusBusinessTypeResult"/>
    </resultMap>

    <resultMap id="CusBusinessTypeResult" type="com.ruoyi.customer.domain.CusBusinessType">
        <id column="cus_main_id" property="cusMainId"/>
        <id column="cus_business_type" property="cusBusinessType"/>
    </resultMap>

    <select id="selectCusMainList" resultMap="CusMainResult">
        select
            cm.* ,
            cbt.*
        from cus_main cm
        left join cus_business_type cbt on cm.cus_id = cbt.cus_main_id
        where cm.del_flag = '0'
        <if test="cusName != null and cusName != ''">
            and cm.cus_name like '%' || #{cusName} || '%'
        </if>
        <if test="cusType != null and cusType != ''">
            and cm.cus_type = #{cusType}
        </if>
        <if test="status != null and status != ''">
            and cm.status = #{status}
        </if>
        <if test="businessType != null and businessType.size() > 0">
            and cm.cus_id in (
                select cus_main_id 
                from cus_business_type 
                where cus_business_type in
                <foreach collection="businessType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
                group by cus_main_id
                having count(distinct cus_business_type) = #{businessTypeNum}
            )
        </if>
        order by cm.create_time desc
    </select>
</mapper>