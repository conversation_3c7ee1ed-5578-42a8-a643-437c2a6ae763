# 租赁箱新增错误分析

## 错误信息
```
{
    "msg": "\r\n### Error updating database.  Cause: com.mybatisflex.core.exception.MybatisFlexException: java.lang.RuntimeException: java.lang.IllegalArgumentException: object is not an instance of declaring class\r\n### Cause: com.mybatisflex.core.exception.MybatisFlexException: java.lang.RuntimeException: java.lang.IllegalArgumentException: object is not an instance of declaring class",
    "code": 500
}
```

## 可能的原因

### 1. 数据库表不存在
- 需要先执行建表语句 `create_cntr_tables.sql`
- 确保表名大小写匹配

### 2. 实体类字段映射问题
- BaseEntity中的字段与数据库字段不匹配
- 字段类型不匹配
- 注解配置错误

### 3. MyBatis-Flex配置问题
- 表名映射错误
- 字段映射错误
- 主键生成策略问题

## 解决步骤

### 第一步：创建数据库表
执行 `create_cntr_tables.sql` 中的建表语句

### 第二步：检查实体类配置
确保：
- 表名正确：`@Table("BASIC_CNTR_MAIN_LEASE")`
- 字段映射正确
- 主键配置正确
- 继承BaseEntity正确

### 第三步：检查字段类型匹配
确保Java字段类型与数据库字段类型匹配：
- String -> VARCHAR2
- BigDecimal -> NUMBER
- Date -> DATE
- Long -> NUMBER

### 第四步：简化测试
先测试最基本的字段，逐步添加复杂字段

## 当前状态
- 已修改表名为大写：`BASIC_CNTR_MAIN_LEASE`
- 已修改字段映射配置
- 需要执行建表语句
- 需要测试基本功能

## 下一步行动
1. 确认数据库表已创建
2. 测试简单的新增操作
3. 逐步排查字段映射问题
