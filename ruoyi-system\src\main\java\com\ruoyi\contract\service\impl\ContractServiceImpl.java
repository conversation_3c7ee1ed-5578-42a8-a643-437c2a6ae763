package com.ruoyi.contract.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;

import com.ruoyi.common.annotation.DataScopeFlex;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.contract.domain.ContractMain;
import com.ruoyi.contract.domain.ContractCustom;
import com.ruoyi.contract.domain.ContractPaymentRelation;
import com.ruoyi.contract.domain.ContractFlow;
import com.ruoyi.contract.domain.ContractFlowTerminal;
import com.ruoyi.contract.domain.table.ContractCustomTableDef;
import com.ruoyi.contract.mapper.ContractCustomMapper;
import com.ruoyi.contract.mapper.ContractMapper;
import com.ruoyi.contract.mapper.ContractPaymentRelationMapper;
import com.ruoyi.contract.mapper.ContractFlowMapper;
import com.ruoyi.contract.mapper.ContractFlowTerminalMapper;
import com.ruoyi.contract.service.IContractService;

import com.ruoyi.customer.domain.CusMain;
import com.ruoyi.customer.domain.table.CusMainTableDef;
import com.ruoyi.customer.mapper.CusMainMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.mybatisflex.core.query.QueryMethods.distinct;
import static com.mybatisflex.core.query.QueryMethods.groupConcat;
import static com.ruoyi.contract.domain.table.ContractCustomTableDef.CONTRACT_CUSTOM;
import static com.ruoyi.contract.domain.table.ContractMainTableDef.CONTRACT_MAIN;
import static com.ruoyi.contract.domain.table.ContractPaymentRelationTableDef.CONTRACT_PAYMENT_RELATION;
import static com.ruoyi.customer.domain.table.CusMainTableDef.CUS_MAIN;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ContractServiceImpl extends ServiceImpl<ContractMapper, ContractMain> implements IContractService {

    @Autowired
    private ContractPaymentRelationMapper contractPaymentRelationMapper;

    @Autowired
    private CusMainMapper cusMainMapper;

    @Autowired
    private ContractCustomMapper contractCustomMapper;

    @Autowired
    private ContractMapper contractMapper;

    @Autowired
    private ContractFlowMapper contractFlowMapper;

    @Autowired
    private ContractFlowTerminalMapper contractFlowTerminalMapper;

    @Override
    @DataScopeFlex(deptAlias = "c")
    public Page<ContractMain> selectPage(ContractMain contract, Integer pageNum, Integer pageSize) {
        Page<ContractMain> page = new Page<>(pageNum, pageSize);
        // return null;

        ContractCustomTableDef cc1 = CONTRACT_CUSTOM.as("cc1");
        ContractCustomTableDef cc2 = CONTRACT_CUSTOM.as("cc2");
        ContractCustomTableDef cc3 = CONTRACT_CUSTOM.as("cc3");

        CusMainTableDef cd1 = CUS_MAIN.as("cd1");
        CusMainTableDef cd2 = CUS_MAIN.as("cd2");
        CusMainTableDef cd3 = CUS_MAIN.as("cd3");

        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        ContractMain::getContractId,
                        ContractMain::getContractNo,
                        ContractMain::getContractName,
                        ContractMain::getContractType,
                        ContractMain::getValidityStartDate,
                        ContractMain::getValidityEndDate,
                        ContractMain::getDeptId,
                        ContractMain::getCreateBy,
                        ContractMain::getCreateTime,
                        ContractMain::getUpdateBy,
                        ContractMain::getUpdateTime,
                        ContractMain::getIsValidity,
                        ContractMain::getRemark)
                .select("listagg(distinct cpr.relation_code,',') within group ( order by cpr.relation_code ) as relation_codes")
                .select("listagg(distinct cd1.cus_name,',') within group ( order by cd1.cus_name ) as firstCustom")
                .select("listagg(distinct cd2.cus_name,',') within group ( order by cd2.cus_name ) as secondCustom")
                .select("listagg(distinct cd3.cus_name,',') within group ( order by cd3.cus_name ) as thirdCustom")
                .from(ContractMain.class).as("c")
                .leftJoin(ContractPaymentRelation.class).as("cpr").on("cpr.contract_id = c.contract_id")
//                .leftJoin(ContractCustom.class).as("cc1")
//                .on("cc1.contract_id = c.contract_id and cc1.contract_role = 'first_custom'")
//                .leftJoin(ContractCustom.class).as("cc2")
//                .on("cc2.contract_id = c.contract_id and cc2.contract_role = 'second_custom'")
//                .leftJoin(ContractCustom.class).as("cc3")
//                .on("cc3.contract_id = c.contract_id and cc3.contract_role = 'third_custom'")
                .leftJoin(cc1).on(cc1.CONTRACT_ID.eq(CONTRACT_MAIN.CONTRACT_ID).and(cc1.CONTRACT_ROLE.eq("first_custom")))
                .leftJoin(cc2).on(cc2.CONTRACT_ID.eq(CONTRACT_MAIN.CONTRACT_ID).and(cc2.CONTRACT_ROLE.eq("second_custom")))
                .leftJoin(cc3).on(cc3.CONTRACT_ID.eq(CONTRACT_MAIN.CONTRACT_ID).and(cc3.CONTRACT_ROLE.eq("third_custom")))
//                .leftJoin(CusMain.class).as("cd1").on("cd1.cus_id = cc1.cus_id")
//                .leftJoin(CusMain.class).as("cd2").on("cd2.cus_id = cc2.cus_id")
//                .leftJoin(CusMain.class).as("cd3").on("cd3.cus_id = cc3.cus_id")
                .leftJoin(cd1).on(cd1.CUS_ID.eq(cc1.CUS_ID))
                .leftJoin(cd2).on(cd2.CUS_ID.eq(cc2.CUS_ID))
                .leftJoin(cd3).on(cd3.CUS_ID.eq(cc3.CUS_ID))
                .like(ContractMain::getContractName, contract.getContractName())
                .like(ContractMain::getContractNo, contract.getContractNo())
                .eq(ContractMain::getContractType, contract.getContractType())
                .eq(ContractMain::getDeptId, contract.getDeptId())
                .eq(ContractMain::getDelFlag, "0")
                .groupBy(
                        ContractMain::getContractId,
                        ContractMain::getContractNo,
                        ContractMain::getContractName,
                        ContractMain::getContractType,
                        ContractMain::getValidityStartDate,
                        ContractMain::getValidityEndDate,
                        ContractMain::getDeptId,
                        ContractMain::getCreateBy,
                        ContractMain::getCreateTime,
                        ContractMain::getUpdateBy,
                        ContractMain::getUpdateTime,
                        ContractMain::getIsValidity,
                        ContractMain::getRemark);

        if (StringUtils.isNotEmpty(contract.getFirstCustom())) {
            queryWrapper.and("cc1.cus_id = '" + contract.getFirstCustom() + "'");
        }

        if (StringUtils.isNotNull(contract.getFirstCustoms()) && !contract.getFirstCustoms().isEmpty()) {
            queryWrapper.in("cc1.cus_id", contract.getFirstCustoms());
        }

        if (StringUtils.isNotEmpty(contract.getSecondCustom())) {
            queryWrapper.and("cc2.cus_id = '" + contract.getSecondCustom() + "'");
        }

        if (StringUtils.isNotNull(contract.getSecondCustoms()) && !contract.getSecondCustoms().isEmpty()) {
            queryWrapper.in("cc2.cus_id", contract.getSecondCustoms());
        }

        if (StringUtils.isNotEmpty(contract.getThirdCustom())) {
            queryWrapper.and("cc3.cus_id = '" + contract.getThirdCustom() + "'");
        }

        if (StringUtils.isNotNull(contract.getThirdCustoms()) && !contract.getThirdCustoms().isEmpty()) {
            queryWrapper.in("cc3.cus_id", contract.getThirdCustoms());
        }

        if (StringUtils.isNotNull(contract.getParams().get("dataScope"))) {
            String str = contract.getParams().get("dataScope").toString();
            // System.out.println("dataScope:" + str);
            if (StringUtils.isNotEmpty(str)) {
                queryWrapper.and(str);
            }
        }

        QueryWrapper exQuery = new QueryWrapper().with("cm_temp").asSelect(queryWrapper).select().from("cm_temp");

        if (!contract.getPayRelation().isEmpty()) {

            for (String str : contract.getPayRelation()) {

                String sql = "find_in_set('" + str + "',relation_codes) > 0";

                exQuery.and(sql);

            }

        }

        Page<ContractMain> result = super.page(page, exQuery);

        List<ContractMain> records = result.getRecords();

        if (!records.isEmpty()) {

            for (ContractMain record : records) {
                // 查找收付关系
                List<String> payRelation = contractPaymentRelationMapper.selectListByQueryAs(QueryWrapper.create()
                        .select(distinct(ContractPaymentRelation::getRelationCode))
                        .eq(ContractPaymentRelation::getContractId, record.getContractId()), String.class);
                if (!payRelation.isEmpty()) {
                    record.setPayRelation(payRelation);
                }

            }

        }

        result.setRecords(records);

        return result;
    }

    @Override
    public AjaxResult saveContract(ContractMain contract) {
        super.save(contract);
        String contractId = contract.getContractId();

        saveOrUpdateRelation(contractId, contract.getPayRelation());
        saveOrUpdateCustom(contractId, contract.getFirstCustoms(), "first_custom");
        saveOrUpdateCustom(contractId, contract.getSecondCustoms(), "second_custom");
        saveOrUpdateCustom(contractId, contract.getThirdCustoms(), "third_custom");

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateContract(ContractMain contract) throws Exception {
        String contractId = contract.getContractId();
        checkValidity(contractId);

        saveOrUpdateRelation(contractId, contract.getPayRelation());
        saveOrUpdateCustom(contractId, contract.getFirstCustoms(), "first_custom");
        saveOrUpdateCustom(contractId, contract.getSecondCustoms(), "second_custom");
        saveOrUpdateCustom(contractId, contract.getThirdCustoms(), "third_custom");

        UpdateChain<ContractMain> chain = UpdateChain.of(ContractMain.class)
                .from(ContractMain.class)
                .eq(ContractMain::getContractId, contract.getContractId())
                .set(ContractMain::getContractNo, contract.getContractNo())
                .set(ContractMain::getContractName, contract.getContractName())
                .set(ContractMain::getValidityStartDate, contract.getValidityStartDate())
                .set(ContractMain::getValidityEndDate, contract.getValidityEndDate())
                .set(ContractMain::getContractType, contract.getContractType())
                .set(ContractMain::getDeptId, contract.getDeptId())
                .set(ContractMain::getRemark, contract.getRemark());

        if (!chain.update()) {
            throw new Exception("修改合同失败");
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getOneById(String id) {

        ContractMain contract = super.getById(id);

        if (StringUtils.isNotNull(contract)) {

            // 查找收付关系
            List<String> payRelation = contractPaymentRelationMapper.selectListByQueryAs(
                    QueryWrapper.create()
                            .select(distinct(ContractPaymentRelation::getRelationCode))
                            .from(ContractPaymentRelation.class)
                            .eq(ContractPaymentRelation::getContractId, id),
                    String.class);

            contract.setPayRelation(payRelation);

            // 查找甲方
            List<String> firstCustoms = contractCustomMapper.selectListByQueryAs(
                    QueryWrapper.create()
                            .select(distinct(ContractCustom::getCusId))
                            .from(ContractCustom.class)
                            .eq(ContractCustom::getContractId, id)
                            .eq(ContractCustom::getContractRole, "first_custom"),
                    String.class);

            contract.setFirstCustoms(firstCustoms);

            List<String> secondCustoms = contractCustomMapper.selectListByQueryAs(
                    QueryWrapper.create()
                            .select(distinct(ContractCustom::getCusId))
                            .from(ContractCustom.class)
                            .eq(ContractCustom::getContractId, id)
                            .eq(ContractCustom::getContractRole, "second_custom"),
                    String.class);

            contract.setSecondCustoms(secondCustoms);

            List<String> thirdCustoms = contractCustomMapper.selectListByQueryAs(
                    QueryWrapper.create()
                            .select(distinct(ContractCustom::getCusId))
                            .from(ContractCustom.class)
                            .eq(ContractCustom::getContractId, id)
                            .eq(ContractCustom::getContractRole, "third_custom"),
                    String.class);

            contract.setThirdCustoms(thirdCustoms);

        }

        return AjaxResult.success(contract);
    }

    @Override
    public AjaxResult delete(List<String> conIds) throws Exception {

        for (String conId : conIds) {
            checkValidity(conId);
        }

        if (!super.removeByIds(conIds)) {
            throw new Exception("批量删除合同失败");
        }

        return AjaxResult.success();
    }

    @Override
    public boolean validity(List<String> conIds, String isValidity) throws Exception {
        UpdateChain<ContractMain> updateChain = UpdateChain.of(ContractMain.class)
                .from(ContractMain.class).set(ContractMain::getIsValidity, isValidity)
                .in(ContractMain::getContractId, conIds);

        if (!updateChain.update()) {
            throw new Exception("修改合同状态失败");
        }

        return true;
    }

    @Override
    public AjaxResult getContractsByCustomerId(String customerId) {
        if (StringUtils.isEmpty(customerId)) {
            return AjaxResult.error("客户ID不能为空");
        }

        try {
            // 构建查询条件
            QueryWrapper queryWrapper = QueryWrapper.create()
                    // 选择必要的合同字段
                    .select(
                            ContractMain::getContractId, // 合同ID
                            ContractMain::getContractNo, // 合同编号
                            ContractMain::getContractName, // 合同名称
                            ContractMain::getContractType, // 合同类型
                            ContractMain::getValidityStartDate, // 有效期开始日期
                            ContractMain::getValidityEndDate, // 有效期结束日期
                            ContractMain::getDeptId, // 部门ID
                            ContractMain::getIsValidity, // 是否有效
                            ContractMain::getRemark, // 备注
                            ContractMain::getCreateBy, // 创建者
                            ContractMain::getCreateTime, // 创建时间
                            ContractMain::getUpdateBy, // 更新者
                            ContractMain::getUpdateTime, // 更新时间
                            ContractMain::getVersion // 版本号
                    )
                    .from(ContractMain.class).as("contract")
                    // 使用左连接查询合同客户关系
                    .leftJoin(ContractCustom.class).as("cc")
                    .on(ContractMain::getContractId, ContractCustom::getContractId)
                    // 查询条件
                    .where(ContractCustom::getCusId).eq(customerId)
                    // 添加排序
                    .orderBy(ContractMain::getCreateTime, false);

            // 查询合同列表
            List<ContractMain> contracts = contractMapper.selectListByQueryAs(queryWrapper, ContractMain.class);

            if (contracts == null || contracts.isEmpty()) {
                return AjaxResult.success("未找到相关合同", new ArrayList<>());
            }

            // 补充合同关联信息
            for (ContractMain contract : contracts) {
                // 查询收付关系
                List<String> payRelation = contractPaymentRelationMapper.selectListByQueryAs(
                        QueryWrapper.create()
                                .select(distinct(ContractPaymentRelation::getRelationCode))
                                .from(ContractPaymentRelation.class)
                                .eq(ContractPaymentRelation::getContractId, contract.getContractId()),
                        String.class);
                contract.setPayRelation(payRelation);

                // 查询合同关联的客户及角色
                List<ContractCustom> contractCustoms = contractCustomMapper.selectListByQuery(
                        QueryWrapper.create()
                                .from(ContractCustom.class)
                                .eq(ContractCustom::getContractId, contract.getContractId()));

                // 获取客户信息
                List<String> firstCustoms = new ArrayList<>();
                List<String> secondCustoms = new ArrayList<>();
                List<String> thirdCustoms = new ArrayList<>();

                // 获取当前客户在合同中的角色
                String currentCustomerRole = null;

                for (ContractCustom cc : contractCustoms) {
                    // 根据角色分类客户
                    if ("first_custom".equals(cc.getContractRole())) {
                        firstCustoms.add(cc.getCusId());
                    } else if ("second_custom".equals(cc.getContractRole())) {
                        secondCustoms.add(cc.getCusId());
                    } else if ("third_custom".equals(cc.getContractRole())) {
                        thirdCustoms.add(cc.getCusId());
                    }

                    // 检查当前客户角色
                    if (customerId.equals(cc.getCusId())) {
                        currentCustomerRole = cc.getContractRole();
                    }
                }

                contract.setFirstCustoms(firstCustoms);
                contract.setSecondCustoms(secondCustoms);
                contract.setThirdCustoms(thirdCustoms);

                // 将角色信息添加到合同对象中
                if (currentCustomerRole != null) {
                    contract.setCustomerRole(currentCustomerRole);
                }
            }

            // 直接返回合同列表，适合前端下拉选择使用
            return AjaxResult.success("查询成功", contracts);
        } catch (Exception e) {
            log.error("查询客户相关合同失败, customerId: {}", customerId, e);
            return AjaxResult.error("查询客户相关合同失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult autoMatchContract(String customerId, String bookingDate, String loadTerminalId,
                                        String unloadTerminalId) {
        // 1. 参数校验
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(bookingDate)) {
            return AjaxResult.error("客户ID和订舱日期不能为空");
        }
        try {
            // 2. 查询条件：客户关联、有效期覆盖、未删除
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .select(
                            ContractMain::getContractId,
                            ContractMain::getContractNo,
                            ContractMain::getContractName,
                            ContractMain::getContractType,
                            ContractMain::getValidityStartDate,
                            ContractMain::getValidityEndDate,
                            ContractMain::getDeptId,
                            ContractMain::getIsValidity,
                            ContractMain::getRemark,
                            ContractMain::getCreateBy,
                            ContractMain::getCreateTime,
                            ContractMain::getUpdateBy,
                            ContractMain::getUpdateTime,
                            ContractMain::getVersion)
                    .from(ContractMain.class).as("contract")
                    .leftJoin(ContractCustom.class).as("cc")
                    .on(ContractMain::getContractId, ContractCustom::getContractId)
                    .where(ContractCustom::getCusId).eq(customerId)
                    .and("contract.validity_start_date <= to_date(?,'yyyy-mm-dd')", bookingDate)
                    .and("contract.validity_end_date >= to_date(?,'yyyy-mm-dd')", bookingDate)
                    .and(ContractMain::getDelFlag).eq("0")
                    .orderBy(ContractMain::getValidityStartDate, false);
            // 3. 预留：如合同表有装卸码头字段，可加如下条件
            // .and(ContractMain::getLoadTerminalId).eq(loadTerminalId)
            // .and(ContractMain::getUnloadTerminalId).eq(unloadTerminalId)
            List<ContractMain> contracts = contractMapper.selectListByQueryAs(queryWrapper, ContractMain.class);
            if (contracts == null || contracts.isEmpty()) {
                return AjaxResult.success("未找到匹配合同", new ArrayList<>());
            }
            // 4. 默认选最新有效合同
            ContractMain best = contracts.get(0);
            return AjaxResult.success("匹配成功", contracts).put("best", best);
        } catch (Exception e) {
            log.error("自动匹配合同失败", e);
            return AjaxResult.error("自动匹配合同失败：" + e.getMessage());
        }
    }

    public void checkValidity(String contractId) throws Exception {

        ContractMain contract = super.getById(contractId);

        if ("Y".equals(contract.getIsValidity())) {
            throw new Exception("合同名称:" + contract.getContractName() + "已生效,不允许修改/删除");
        }

    }

    public void saveOrUpdateCustom(String contractId, List<String> customs, String contractRole) {

        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ContractCustom::getContractId, contractId)
                .eq(ContractCustom::getContractRole, contractRole);

        List<ContractCustom> oldCustoms = contractCustomMapper.selectListByQuery(queryWrapper);

        if (!oldCustoms.isEmpty()) {
            contractCustomMapper.deleteByQuery(queryWrapper);
        }

        for (String custom : customs) {

            ContractCustom contractCustom = new ContractCustom();

            contractCustom.setContractId(contractId);
            contractCustom.setCusId(custom);
            contractCustom.setContractRole(contractRole);

            contractCustomMapper.insert(contractCustom);

        }

    }

    public void saveOrUpdateRelation(String contractId, List<String> payRelation) {

        // 判断有没有旧数据
        QueryWrapper queryWrapper = QueryWrapper.create().eq(ContractPaymentRelation::getContractId, contractId);

        List<ContractPaymentRelation> oldRelation = contractPaymentRelationMapper.selectListByQuery(queryWrapper);

        if (!oldRelation.isEmpty()) {
            contractPaymentRelationMapper.deleteByQuery(queryWrapper);
        }

        for (String relation : payRelation) {

            ContractPaymentRelation cpr = new ContractPaymentRelation();

            cpr.setContractId(contractId);
            cpr.setRelationCode(relation);

            contractPaymentRelationMapper.insert(cpr);
        }

    }

    /**
     * 保存或更新合同流向
     * @param contractId 合同ID
     * @param flows 流向列表
     */
    private void saveOrUpdateFlows(String contractId, List<ContractFlow> flows) {
        if (flows == null || flows.isEmpty()) {
            return;
        }

        // 删除旧的流向数据
        QueryWrapper flowQuery = QueryWrapper.create()
                .eq(ContractFlow::getContractId, contractId);
        List<ContractFlow> oldFlows = contractFlowMapper.selectListByQuery(flowQuery);

        if (!oldFlows.isEmpty()) {
            // 删除旧的流向码头数据
            for (ContractFlow flow : oldFlows) {
                QueryWrapper terminalQuery = QueryWrapper.create()
                        .eq(ContractFlowTerminal::getFlowId, flow.getFlowId());
                contractFlowTerminalMapper.deleteByQuery(terminalQuery);
            }
            // 删除旧的流向数据
            contractFlowMapper.deleteByQuery(flowQuery);
        }

        // 保存新的流向数据
        for (ContractFlow flow : flows) {
            flow.setContractId(contractId);

            contractFlowMapper.insert(flow);

            // 保存流向码头数据
            if (flow.getTerminals() != null && !flow.getTerminals().isEmpty()) {
                for (ContractFlowTerminal terminal : flow.getTerminals()) {
                    terminal.setFlowId(flow.getFlowId());

                    contractFlowTerminalMapper.insert(terminal);
                }
            }
        }
    }

}
