package com.ruoyi.route.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.route.domain.RouteBranch;
import com.ruoyi.route.mapper.RouteBranchMapper;

/**
 * 航线支线Service接口
 */
public interface IRouteBranchService extends IService<RouteBranch> {
    /**
     * 获取Mapper
     */
    RouteBranchMapper getMapper();

    /**
     * 分页查询航线支线
     */
    Page<RouteBranch> selectRouteBranchPage(int pageNumber, int pageSize, RouteBranch routeBranch);

    /**
     * 统计指定主航线下的支线数量
     */
    int countByMainId(String mainId);

    /**
     * 批量删除支线
     */
    int deleteRouteBranchByIds(String[] branchIds);

    /**
     * 判断支线名称是否已存在（全局唯一，排除自身）
     */
    boolean existsBranchName(String branchName, String mainId, String excludeId);

} 