package com.ruoyi.plan.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.plan.domain.VoyageSeq;
import com.ruoyi.plan.mapper.VoyageSeqMapper;
import com.ruoyi.plan.service.IVoyageSeqService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


@Service
public class VoyageSeqServiceImpl extends ServiceImpl<VoyageSeqMapper, VoyageSeq> implements IVoyageSeqService {

    @Override
    public VoyageSeq selectVoyageSeqById(String id) {
        return this.getById(id);
    }

    @Override
    public int insertVoyageSeq(VoyageSeq voyageSeq) {
        return this.save(voyageSeq) ? 1 : 0;
    }

    @Override
    public int updateVoyageSeq(VoyageSeq voyageSeq) {
        return this.updateById(voyageSeq) ? 1 : 0;
    }

    @Override
    public int deleteVoyageSeqByIds(String[] ids) {
        return this.removeByIds(Arrays.asList(ids)) ? ids.length : 0;
    }

    @Override
    public int deleteVoyageSeqById(String id) {
        return this.removeById(id) ? 1 : 0;
    }
} 