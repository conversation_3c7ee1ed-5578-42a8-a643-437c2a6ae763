# 数据库账号密码及连接信息说明

> 本文档仅限内部运维及开发人员查阅，严禁外泄！

## 1. Oracle 主库连接信息
- **连接地址（IP）**：`*************:1521/cwdb`
- **用户名**：`SHIPPING`
- **密码**：`Shipping@2025`
- **驱动**：`oracle.jdbc.OracleDriver`
- **JDBC URL**：
  ```
  *****************************************
  ```

## 2. Druid 管理后台
- **管理用户名**：`ruoyi`
- **管理密码**：`123456`
- **访问路径**：`/druid/`

## 3. Redis 配置信息
- **地址**：`localhost`
- **端口**：`6379`
- **数据库索引**：`6`
- **密码**：无（默认空）

## 4. 其他说明
- 生产环境数据库账号密码如有变更，请及时同步更新本文件。
- 如需其他环境（测试/开发/旧系统等）账号密码，请联系系统管理员。

