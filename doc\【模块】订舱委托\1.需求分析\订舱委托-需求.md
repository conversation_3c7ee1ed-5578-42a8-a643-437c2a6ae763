# 订舱委托需求文档

## 一、手动录入模式
1. **必填项**：订舱客户、订舱日期、装货码头、卸货码头为必填项，支持简称和代码快速填充。
2. **合同自动带出**：根据上述四项自动带出执行合同。
   - 与旧系统不同：若无合同，允许建单但不可推送至配载。建单后，系统将该委托挂入用户任务并风控中心监控，待商务补录合同后，用户可补填合同信息并重新推送配载。
   - 目的：防止因商务延迟导致单证无法登记订舱，避免AA表2.0问题。
3. **历史信息自动带出**：贸易类型、装货代理、卸货代理根据历史相同条件自动带出，无历史则保存本次填写内容，供下次自动填充。
4. **粗略箱信息优化**：取消箱型录入（初步登记无需箱型，不影响配载，减轻操作负担），支持TAB键加速录入。
5. **明细与粗略箱信息联动**：录入明细箱信息时，粗略箱信息自动同步变动。若后续补录明细，同样自动联动。
6. **页签操作**：
   - 复制：复制当前页签所有内容，新增一个页签。
   - 新增：新增空白页签。
7. **清空与关闭**：
   - 清空：清空并关闭当前页签（首个页签仅可清空，不可关闭）。
8. **批量提交**：点击提交，将当前所有页签内容批量新增为订舱委托，每个页签对应一条委托。

## 二、导入模式
1. **文件拖拽识别**：支持托运单、舱单等Excel文件拖拽，自动解析并填充表单。
   - 反馈：希望同一框内可拖入任意Excel，自动识别多种格式。若无AI辅助，纯代码实现难度较大，需评估可行性。
2. **多流向自动分页**：若文件含多个流向，系统自动新建多个页签。
3. **多客户处理**：如文件含多个客户，仅取第一个客户，不支持一次新建多个客户委托。
   - 说明：舱单多客户场景一般在配载后出现，新增时无需考虑，避免系统复杂化。

## 三、AI模式（中期规划）
1. **智能对话录入**：预留通过大语言模型对话方式读取订舱委托的功能，支持输入邮件内容、提单PDF等，通过对话框"应用到表单"自动填充。
2. **开发计划**：AI功能因算力和开发成本暂缓，先完善手动与导入模式，待基础功能上线及算力问题解决后再推进。
