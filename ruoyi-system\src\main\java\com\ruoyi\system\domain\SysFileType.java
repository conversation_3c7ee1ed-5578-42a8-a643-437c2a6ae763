package com.ruoyi.system.domain;

import com.mybatisflex.annotation.*;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.crypto.KeyGenerator;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("sys_file_type")

public class SysFileType extends BaseEntity {

    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    String fileTypeId;

    @NotBlank(message = "文件类型(一级)不能为空")
    String fileType;

    @NotBlank(message = "文件类型(二级)不能为空")
    String fileBusinessType;

//    @TableLogic
    String delFlag;

    Integer limitNum;

    Integer fileSort;

}
