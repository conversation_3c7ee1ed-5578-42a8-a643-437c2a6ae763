package com.ruoyi.booking.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.LogisticsMain;
import com.ruoyi.booking.mapper.LogisticsMainMapper;
import com.ruoyi.booking.service.ILogisticsMainService;
import org.springframework.stereotype.Service;

/**
 * 物流主表服务实现类
 * 继承ServiceImpl获得基础CRUD方法实现
 */
@Service
public class LogisticsMainServiceImpl extends ServiceImpl<LogisticsMainMapper, LogisticsMain> implements ILogisticsMainService {
    // 继承ServiceImpl后自动获得基础CRUD方法的实现
}