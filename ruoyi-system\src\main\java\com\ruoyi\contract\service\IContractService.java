package com.ruoyi.contract.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.contract.domain.ContractMain;

import java.util.List;

public interface IContractService extends IService<ContractMain> {
    Page<ContractMain> selectPage(ContractMain contract, Integer pageNum, Integer pageSize);

    AjaxResult saveContract(ContractMain contract);

    AjaxResult updateContract(ContractMain contract) throws Exception;

    AjaxResult getOneById(String id);

    AjaxResult delete(List<String> conIds) throws Exception;

    boolean validity(List<String> conIds, String isValidity) throws Exception;

    // 根据客户ID获取相关合同
    AjaxResult getContractsByCustomerId(String customerId);

    /**
     * 自动匹配合同（根据客户、日期、装货码头、卸货码头）
     */
    AjaxResult autoMatchContract(String customerId, String bookingDate, String loadTerminalId, String unloadTerminalId);
}
