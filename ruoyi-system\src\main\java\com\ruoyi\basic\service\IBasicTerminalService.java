package com.ruoyi.basic.service;


import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicTerminal;

import java.util.List;

public interface IBasicTerminalService extends IService<BasicTerminal> {
    /**
     * 查询码头管理
     *
     * @param id 码头管理主键
     * @return 码头管理
     */
    public BasicTerminal selectBasicTerminalById(String id);

    /**
     * 查询码头管理列表
     *
     * @param basicTerminal 码头管理
     * @return 码头管理集合
     */
    public List<BasicTerminal> selectBasicTerminalList(BasicTerminal basicTerminal);

    /**
     * 新增码头管理
     *
     * @param basicTerminal 码头管理
     * @return 结果
     */
    public int insertBasicTerminal(BasicTerminal basicTerminal) throws Exception;

    /**
     * 修改码头管理
     *
     * @param basicTerminal 码头管理
     * @return 结果
     */
    public int updateBasicTerminal(BasicTerminal basicTerminal) throws Exception;

    /**
     * 批量删除码头管理
     *
     * @param ids 需要删除的码头管理主键集合
     * @return 结果
     */
    public int deleteBasicTerminalByIds(List<String> ids);

    /**
     * 删除码头管理信息
     *
     * @param id 码头管理主键
     * @return 结果
     */
    public int deleteBasicTerminalById(String id);
}
