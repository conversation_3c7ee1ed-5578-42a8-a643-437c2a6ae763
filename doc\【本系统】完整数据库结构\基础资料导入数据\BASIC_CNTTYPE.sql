INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10041', '02', '02', NULL, NULL, TO_DATE('2019-12-31 09:16:02', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10040', '01', '01', NULL, NULL, TO_DATE('2019-12-31 09:15:34', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10047', '02', '02', NULL, NULL, TO_DATE('2019-12-31 09:27:53', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10043', '03', '03', NULL, NULL, TO_DATE('2019-12-31 09:17:24', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('1', NULL, 'FL', NULL, '特殊平板柜FL', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('2', NULL, 'FR', NULL, '特殊平板柜FR', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('3', NULL, 'GP', 'GP', '普通柜GP', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('4', NULL, 'HD', 'GP', '普通柜HD', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('5', NULL, 'WD', NULL, '特殊超重柜WD', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('6', NULL, 'OT', NULL, '特殊开顶柜OT', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('7', NULL, 'RF', NULL, '特殊冷冻柜RF', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('8', NULL, 'TK', NULL, '特殊槽罐柜TK', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('9', NULL, 'HC', 'GP', '普通柜HC', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10', NULL, 'HQ', 'GP', '普通柜HQ', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('11', NULL, 'RH', 'RF', '特殊冷冻柜RH', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('12', NULL, 'RQ', 'RF', '特殊冷冻柜RQ', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('13', NULL, 'HT', NULL, '特殊挂衣柜HT', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('14', NULL, 'PL', NULL, '特殊框架柜PL', TO_DATE('2019-12-29 09:56:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10053', NULL, 'PH', 'GP', '普通柜PH', TO_DATE('2020-10-15 10:21:35', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10054', NULL, 'OH', NULL, '特殊开顶超高柜OH', TO_DATE('2020-10-28 16:41:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10056', NULL, 'OM', NULL, '特殊柜OM', TO_DATE('2021-05-31 16:55:38', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10057', NULL, 'FS', NULL, '特殊柜FS', TO_DATE('2021-05-31 16:55:58', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10071', NULL, 'PW', NULL, NULL, TO_DATE('2024-04-08 10:08:44', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10048', 'HR', 'HR', NULL, '特殊冷冻柜HR', TO_DATE('2020-01-01 12:19:06', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10049', NULL, 'FC', NULL, '普通柜FC', TO_DATE('2020-01-16 15:40:36', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10064', NULL, 'NOR', NULL, '特殊冷冻柜', TO_DATE('2021-08-20 16:14:40', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10066', NULL, 'RHPW', NULL, '特殊冷藏箱', TO_DATE('2023-04-04 15:32:01', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10050', NULL, 'DG', NULL, '危险品柜', TO_DATE('2020-06-03 14:33:50', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10058', NULL, 'FT', NULL, '特殊柜', TO_DATE('2021-06-07 21:11:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10060', NULL, 'HW', NULL, '特殊柜', TO_DATE('2021-06-13 20:00:11', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10061', NULL, 'PF', NULL, '特殊平板柜PF', TO_DATE('2021-07-27 15:45:22', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10065', NULL, 'CAR', NULL, '特殊汽车柜', TO_DATE('2022-01-18 08:46:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10069', NULL, 'CS', NULL, NULL, TO_DATE('2023-11-23 21:10:12', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10070', NULL, 'FM', NULL, '普通折叠柜', TO_DATE('2024-03-22 15:55:43', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10072', NULL, 'FW', NULL, NULL, TO_DATE('2024-04-29 12:25:35', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10051', NULL, 'HH', NULL, '普通柜HH', TO_DATE('2020-06-08 11:15:46', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10052', NULL, 'DC', 'GP', '普通柜DC', TO_DATE('2020-08-17 14:45:00', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10055', NULL, 'FH', NULL, '特殊平板柜FH', TO_DATE('2020-12-21 14:59:29', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10059', NULL, '45HW', NULL, '特殊柜', TO_DATE('2021-06-13 16:44:22', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10062', NULL, '40G0', NULL, NULL, TO_DATE('2021-08-19 22:41:57', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10063', NULL, 'G0', NULL, NULL, TO_DATE('2021-08-19 22:42:12', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10067', NULL, 'LEG0', NULL, NULL, TO_DATE('2023-06-02 16:39:54', 'SYYYY-MM-DD HH24:MI:SS'), '0');
INSERT INTO "BASIC_CNTR_TYPE" ("ID", "TYPE_ISO", "TYPE_CODE", "TYPE_COMMON_CODE", "TYPE_NAME", "CREATE_TIME", "DEL_FLAG") VALUES ('10068', NULL, 'ST', NULL, NULL, TO_DATE('2023-08-31 10:27:21', 'SYYYY-MM-DD HH24:MI:SS'), '0');
UPDATE BASIC_CNTR_TYPE SET DEL_FLAG = '2' WHERE  DEL_FLAG != '0'