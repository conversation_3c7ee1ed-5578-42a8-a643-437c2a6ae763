package com.ruoyi.framework.config;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.tenant.TenantFactory;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.framework.listener.DomainInsertListener;
import com.ruoyi.framework.listener.DomainUpdateListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class MybatisFlexConfig {

    public MybatisFlexConfig() {
        globalConfig();
        //正式环境如果想记录sql 可以打开这个
//        sqlLog();
    }

    /**
     * 全局配置
     */
    private void globalConfig() {
        FlexGlobalConfig globalConfig = FlexGlobalConfig.getDefaultConfig();
        // 注册 insertListener
        globalConfig.registerInsertListener(new DomainInsertListener<>(), BaseEntity.class);


        // 注册 updateListener
        globalConfig.registerUpdateListener(new DomainUpdateListener<>(), BaseEntity.class);



//        TenantManager.withoutTenantCondition(()->CusDataService.class);

        globalConfig.setTenantColumn("tenant_id");

    }

    /**
     * SQL打印
     */
    private void sqlLog() {
        // 开启审计功能（SQL打印分析的功能是使用 SQL审计模块完成的）
        AuditManager.setAuditEnable(true);

        // 设置 SQL审计收集器
        AuditManager.setMessageCollector(auditMessage ->
                log.info("SQL:【{}】, 耗时:【{}ms】", auditMessage.getFullSql(), auditMessage.getElapsedTime())
        );
    }

}
