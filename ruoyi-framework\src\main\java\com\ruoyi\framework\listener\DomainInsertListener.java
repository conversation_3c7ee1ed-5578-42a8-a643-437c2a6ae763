package com.ruoyi.framework.listener;

import com.mybatisflex.annotation.InsertListener;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.SecurityUtils;

import java.util.Date;

public class DomainInsertListener<T extends BaseEntity> implements InsertListener {
    @Override
    public void onInsert(Object entity) {
        if(entity instanceof BaseEntity) {
            T t = (T) entity;
            t.setCreateBy(SecurityUtils.getUsername());
            t.setCreateTime(new Date());
        }
    }
}
