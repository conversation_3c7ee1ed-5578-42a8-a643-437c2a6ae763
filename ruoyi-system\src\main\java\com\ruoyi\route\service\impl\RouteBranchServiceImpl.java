package com.ruoyi.route.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.route.domain.RouteBranch;
import com.ruoyi.route.mapper.RouteBranchMapper;
import com.ruoyi.route.service.IRouteBranchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

import static com.ruoyi.route.domain.table.RouteBranchTableDef.ROUTE_BRANCH;
import static com.ruoyi.route.domain.table.RouteMainTableDef.ROUTE_MAIN;

/**
 * 航线支线Service业务层处理
 */
@Slf4j
@Service
public class RouteBranchServiceImpl implements IRouteBranchService {
    @Autowired
    private RouteBranchMapper routeBranchMapper;

    @Override
    public RouteBranchMapper getMapper() {
        return routeBranchMapper;
    }

    /**
     * 分页查询航线支线
     */
    @Override
    public Page<RouteBranch> selectRouteBranchPage(int pageNumber, int pageSize, RouteBranch routeBranch) {
        log.debug("分页查询航线支线 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, routeBranch);
        
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                    ROUTE_BRANCH.ALL_COLUMNS,
                    ROUTE_MAIN.MAIN_NAME.as("mainName")
                )
                .from(ROUTE_BRANCH)
                .leftJoin(ROUTE_MAIN).on(ROUTE_BRANCH.MAIN_ID.eq(ROUTE_MAIN.MAIN_ID))
                .where(ROUTE_BRANCH.BRANCH_NAME.like(routeBranch.getBranchName(), StringUtils.isNotBlank(routeBranch.getBranchName())))
                .and(ROUTE_BRANCH.MAIN_ID.eq(routeBranch.getMainId(), StringUtils.isNotBlank(routeBranch.getMainId())))
                .orderBy(ROUTE_BRANCH.CREATE_TIME.desc());
        
        Page<RouteBranch> result = routeBranchMapper.paginate(pageNumber, pageSize, queryWrapper);
        
        log.debug("分页查询航线支线完成 - 总记录数: {}", result.getTotalRow());
        
        return result;
    }

    /**
     * 统计指定主航线下的支线数量
     */
    @Override
    public int countByMainId(String mainId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(ROUTE_BRANCH)
                .where(ROUTE_BRANCH.MAIN_ID.eq(mainId));
        return (int) routeBranchMapper.selectCountByQuery(queryWrapper);
    }

    /**
     * 批量删除航线支线
     */
    @Override
    public int deleteRouteBranchByIds(String[] branchIds) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(ROUTE_BRANCH.BRANCH_ID.in(Arrays.asList(branchIds)));
        return routeBranchMapper.deleteByQuery(queryWrapper);
    }

    /**
     * 判断同主线下支线名称是否已存在（排除自身）
     */
    public boolean existsBranchName(String branchName, String mainId, String excludeId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(ROUTE_BRANCH)
            .where(ROUTE_BRANCH.BRANCH_NAME.eq(branchName))
            .and(ROUTE_BRANCH.MAIN_ID.eq(mainId));
        if (excludeId != null) {
            queryWrapper.and(ROUTE_BRANCH.BRANCH_ID.ne(excludeId));
        }
        return routeBranchMapper.selectCountByQuery(queryWrapper) > 0;
    }
} 