package com.ruoyi.plan.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.plan.domain.RelationBookingVoyageMain;
import com.ruoyi.plan.mapper.RelationBookingVoyageMainMapper;
import com.ruoyi.plan.service.IRelationBookingVoyageMainService;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
public class RelationBookingVoyageMainServiceImpl extends ServiceImpl<RelationBookingVoyageMainMapper, RelationBookingVoyageMain>
        implements IRelationBookingVoyageMainService {

    @Override
    public RelationBookingVoyageMain selectById(String id) {
        return this.getById(id);
    }

    @Override
    public int insert(RelationBookingVoyageMain entity) {
        return this.save(entity) ? 1 : 0;
    }

    @Override
    public int update(RelationBookingVoyageMain entity) {
        return this.updateById(entity) ? 1 : 0;
    }

    @Override
    public int deleteByIds(String[] ids) {
        return this.removeByIds(Arrays.asList(ids)) ? ids.length : 0;
    }

    @Override
    public int deleteById(String id) {
        return this.removeById(id) ? 1 : 0;
    }
} 