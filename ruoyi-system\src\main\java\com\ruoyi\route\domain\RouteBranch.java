package com.ruoyi.route.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 航线支线对象 route_branch
 */
@Data
@Table("ROUTE_BRANCH")
@EqualsAndHashCode(callSuper = true)
public class RouteBranch extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 支线ID */
    @Id(keyType = KeyType.Generator, value = "snowFlakeId")
    @Excel(name = "支线ID")
    private String branchId;

    /** 支线名称 */
    @Excel(name = "支线名称")
    private String branchName;

    /** 所属主线ID */
    @Excel(name = "所属主线ID")
    private String mainId;

    /** 所属主线名称 */
    @Excel(name = "所属主线名称")
    private String mainName;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 乐观锁 */
    private Long version;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("branchId", getBranchId())
                .append("branchName", getBranchName())
                .append("mainId", getMainId())
                .append("mainName", getMainName())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("version", getVersion())
                .toString();
    }
} 