package com.ruoyi.customer.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.customer.domain.CusMain;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【CUS_MAIN】的数据库操作Service
* @createDate 2025-04-27 09:26:46
*/
public interface CusMainService extends IService<CusMain> {

    // 客户列表
    Page<CusMain> selectCusMainList(CusMain cusMain, Integer pageNum, Integer pageSize);

    // 远程搜索客户
    List<CusMain> remoteSearchCusData(String searchKey);

    // 新增客户
    AjaxResult insertCusMain(CusMain cusMain);

    // 修改客户
    AjaxResult updateCusMain(CusMain cusMain);

    // 删除客户
    AjaxResult deleteCusMain(String id);

    // 客户详情
    AjaxResult getCusMainById(String id);

}
