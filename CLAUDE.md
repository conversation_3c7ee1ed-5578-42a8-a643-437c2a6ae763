# CLAUDE.md

- This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
- 在使用ElementPlus图标时，你经常引用一些不存在的图标，导致报错，你可以用MCP方式，比如：use context7，获取最新的文档和代码示例。
- 关于图标的markdown文档，你可以看ELEMENT_PLUS_ICONS.md，避免你虚构一些不存在的图标导致报错。
- 必要的时候，帮我commit代码到仓库，但是不要push到远程仓库。
- 如果你要测试，那你只进行语法测试，功能测试我自己来。

## Development Commands

### Development Server
```bash
npm run dev
# or
yarn dev
# Starts development server on port 85
```

### Build Commands
```bash
npm run build:prod        # Production build
npm run build:stage       # Staging build
npm run preview           # Preview production build
```

### Installation
```bash
npm install
# or
yarn install --registry=https://registry.npmmirror.com
```

### Testing & Linting
No specific test or lint commands are configured in package.json. The project relies on Vite's built-in development features and manual testing.

## Project Architecture

### Tech Stack
- **Frontend**: Vue 3 + Composition API
- **UI Framework**: Element Plus
- **Build Tool**: Vite
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **HTTP Client**: Axios with custom interceptors
- **Styling**: SCSS + Element Plus theming

### Application Structure
This is a shipping management system (集装箱数智航运系统) built on the RuoYi framework with three main business modules:

1. **Customer Portal** (`src/views/booking/customerPortal/`) - Customer self-service booking system
2. **Documents Clerk Workstation** (`src/views/booking/documentsClerk/`) - Document processing and management
3. **Documents Clerk Workstation V2** (`src/views/booking-v2/`) - **当前主要开发版本**
4. **Planner Workstation** (`src/views/booking/planner/`) - Schedule planning and vessel allocation

## 单证员工作台详细架构

### V1 版本（已弃用）
- **主入口**: `src/views/booking/documentsClerk/index.vue`
- **组件目录**: `src/views/booking/documentsClerk/components/`
- **状态**: 已弃用，仅做历史参考

### V2 版本（主要开发版本）⭐⭐⭐
单证员工作台 V2 是当前的主要开发版本，采用现代化的架构设计和完整的软件工程流程管理。

#### 核心架构
- **主入口**: `src/views/booking-v2/ClerkWorkbench.vue`
- **核心组件**: `src/views/booking-v2/components/entrust-panels/BookingPanel.vue`
- **侧边栏面板**: `src/views/booking-v2/components/entrust-panels/EntrustSidePanel.vue`
- **其他面板**: `PreAllocationPanel.vue`, `ShippingPanel.vue`, `TerminalPanel.vue`
- **Options系统**: `src/composables/` 目录下的各种业务选项composable
- **API接口**: `src/api/booking/options.js`

#### 重要文档系统 (doc/)
单证员工作台 V2 包含完整的软件工程文档，位于 `src/views/booking-v2/doc/`：

**📁 文档结构**:
```
src/views/booking-v2/doc/
├── README.md                        # 文档总体概述
├── 01-需求分析/                      # 需求分析阶段
│   ├── AI协作规则/                   # AI协作规范
│   ├── QA问答/                      # 6轮需求调研问答
│   ├── 背景信息/                    # AA表、两湾快航、共同体
│   └── 需求规格说明书/               # 业务需求规格
├── 02-系统设计/                      # 系统设计阶段
│   ├── 数据库设计/                   # ⭐ 重要SQL文件
│   └── 原型设计/                    # 交互设计文档
└── 03-06各阶段文档...
```

#### ⭐ 关键数据库设计文件
位于 `src/views/booking-v2/doc/02-系统设计/数据库设计/`：

1. **订单物流系统V3.0数据库DDL脚本.sql** （最重要）
   - 完整的数据库表结构定义
   - 三层架构：ORDER_MAIN → BOOKING_MAIN → LOGISTICS_MAIN → LOGISTIC_WATERWAY
   - 支持雪花ID、统一命名规范、策划员拆单功能
   - 包含订单、订舱、物流、柜量等核心业务表

2. **视图v_booking_entrust.sql**
   - 订舱委托统一视图，整合三层架构数据
   - 为单证员工作台提供高性能列表查询
   - 包含业务状态判断、优先级计算、柜量汇总等

3. **create_sequences.sql**
   - 订单号和订舱号生成序列
   - 解决唯一约束冲突问题
   - 支持 WT+日期+序号 和 BK+日期+序号 格式

4. **若依字典配置脚本.sql**
   - 配置订舱表单的所有下拉选项
   - 包含运输模式、报关类型、结算方式、共同体等字典

#### 业务特色
- **三层解耦架构**: 委托层-订舱层-物流层分离设计
- **AI协作规范**: 完整的AI辅助开发流程（6轮QA问答）
- **多轮需求调研**: 深度业务分析，涵盖复杂业务场景
- **黄埔共同体**: 特殊的多方协作业务模式
- **两湾快航**: 复杂的多层级驳船运输场景
- **AA表替代**: 解决腾讯文档2.7万行数据的性能瓶颈

### 数据流向
1. 用户在新增委托Tab填写表单
2. 表单数据经过验证后提交
3. 数据同步到委托列表
4. 状态统计实时更新
5. 生成通知和沟通记录

### 关键业务逻辑
- 柜量信息为必填项，新增时预置一条默认记录
- 支持草稿保存和正式提交两种模式
- 表单分Tab设计支持复杂业务流程
- 集成AI解析提升录入效率

## Vue 3 Composables Options 架构 🔥

### 设计原则
- **分散式设计**: 每个业务选项独立 composable，便于团队协作
- **简洁优先**: 移除复杂缓存和降级逻辑，专注核心功能
- **优雅降级**: API 失败时返回空数组，不阻塞界面渲染
- **直接引用**: 避免 `options.xxxOptions` 包装，直接使用 `xxxOptions`

### Composables 文件结构
```
src/composables/
├── useCustomerOptions.js    # 客户选项管理
├── usePortOptions.js        # 港口选项管理  
├── useTerminalOptions.js    # 码头选项管理
├── useAgentOptions.js       # 代理选项管理
├── useSupplierOptions.js    # 供应商选项管理
└── README.md               # 详细使用指南
```

### 正确使用方式
```vue
<script setup>
import { useCustomerOptions } from '@/composables/useCustomerOptions'
import { usePortOptions } from '@/composables/usePortOptions'

const { customerOptions, getCustomerName } = useCustomerOptions()
const { portOptions, getPortName } = usePortOptions()
</script>

<template>
  <el-select v-model="selectedCustomer">
    <el-option 
      v-for="customer in customerOptions"  ✅ 正确
      :key="customer.value" 
      :label="customer.label" 
      :value="customer.value" 
    />
  </el-select>
</template>
```


### 最佳实践
1. 参考 `src/composables/README.md` 详细指南
2. 保持分散式设计，避免聚合到单一 composable
3. API 失败优雅降级，确保界面可用
4. 遵循项目命名和结构规范


### 核心业务背景

#### 业务现状问题
- **AA表痛点**: 腾讯文档2.7万行数据导致卡顿、覆盖、同步错误
- **协作效率低**: 单证员与策划员信息流转不畅，重复工作多
- **资源冲突**: 船舶资源重复分配，缺乏冲突检测机制
- **信息不完整**: 重点备注等关键信息缺失，影响优先级判断

#### 特殊业务场景
- **两湾快航**: 广西北部湾到大湾区多层级驳船运输
- **黄埔共同体**: 多家船务公司联合运营，统一操盘模式
- **木材纸柜项目**: 需要柜期/舱期字段的特殊货物管理

#### 核心用户群体
- **单证员**: 客户委托信息收集和订舱数据录入
- **策划员**: 船期计划制定和船舶资源配置  
- **客户**: 通过自助门户提交需求和查询状态

#### 业务价值目标
- 📈 **效率提升**: 订舱处理效率提升30%以上
- 🎯 **准确性提高**: 减少人工错误90%以上
- 🔗 **协作优化**: 实现多角色实时协同，信息透明化
- 📱 **移动支持**: 支持移动端操作，提升工作灵活性

### AI协作机制
- **文档维护**: AI自动维护文档结构和内容更新
- **问题生成**: AI根据调研进度自动生成新问题
- **质量检查**: AI自动进行质量检查和推进建议
- **进度跟踪**: 通过AI分析获取各模块完成度


### Key Directories
- `src/api/` - API service modules organized by business domain
- `src/components/` - Reusable UI components
- `src/views/` - Page components organized by business modules
- `src/utils/` - Utility functions and helpers
- `src/store/modules/` - Pinia store modules
- `src/assets/` - Static assets (styles, images, icons)

### Development Environment Setup
- Development server runs on port 85
- API proxy configured for `/dev-api` → `http://localhost:8088`
- File service proxy for `/shippingfile` → `http://*************:9000`
- File viewer proxy for `/fileView` → `http://*************:8012`

## Code Conventions

### Component Structure
- Use Vue 3 Composition API with `<script setup>`
- Follow Element Plus design patterns
- Components are organized by business domain
- Shared components are in `src/components/`

### API Integration
- All API calls use the centralized axios instance from `src/utils/request.js`
- API modules are organized by business domain in `src/api/`
- Request/response interceptors handle authentication and error handling
- Mock data is available in component-specific `mockData.js` files

### Authentication & Authorization
- JWT token-based authentication
- Tokens stored using `src/utils/auth.js`
- Role-based access control with permissions
- Automatic token refresh and logout handling

### State Management
- Pinia stores are used for complex state management
- Store modules: `user`, `app`, `permission`, `settings`, `dict`, `tagsView`
- Local component state preferred for simple cases

### Styling
- SCSS preprocessing with design tokens
- Element Plus theme customization in `src/assets/styles/`
- Responsive design with mobile support
- Icon system using SVG sprites

## Business Context

### Main Business Modules
1. **Basic Data Management** - Ports, vessels, cargo types, container specifications
2. **Customer Management** - Customer profiles, barge suppliers, contact information
3. **Booking System** - Multi-role booking workflow with customer portal and clerk workstation
4. **Contract Management** - Contract workflows and document processing
5. **Route Planning** - Vessel routing and schedule optimization
6. **CODECO Processing** - Container status message generation and parsing

### Key Business Rules
- Customer booking workflow: Draft → Pending → Under Review → Confirmed/Partially Confirmed/Rejected
- Container allocation based on available capacity and customer priority
- Special handling for dangerous goods and refrigerated cargo
- Multi-level approval workflows for contract and booking modifications

## Development Notes

### Mock Data Usage
- Business modules include comprehensive mock data for development
- Mock data files are co-located with their respective components
- Real API integration requires backend service setup

### Prototyping Support
- Prototypes include full business workflow simulations
- Documentation in Chinese reflects the local business context

### Performance Considerations
- Virtual scrolling for large data sets
- Lazy loading for route components
- Image optimization and compression
- Bundle splitting configured in Vite

### Security Features
- Request deduplication to prevent duplicate submissions
- Input validation and sanitization
- Secure token storage and transmission
- File upload security controls

## Testing & Quality

### Browser Support
- Modern browsers with ES6+ support
- Mobile-responsive design
- Touch-friendly interactions

### Error Handling
- Centralized error handling in HTTP interceptors
- User-friendly error messages
- Fallback UI states for network errors
- Comprehensive logging for debugging

## Deployment

### Build Configuration
- Production builds are optimized and minified
- Environment-specific configurations via `.env` files
- Static asset optimization with Vite
- Staging environment support for testing

### Integration Points
- Backend API service integration
- File storage service integration
- Database connectivity for persistent data
- Authentication service integration