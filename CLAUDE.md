# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目简介

这是一个基于若依框架的船务管理系统，使用Spring Boot 3.3.5 + Vue 2.6.12前后端分离架构。项目专门为船务物流业务设计，包含基础资料管理、订舱委托、客户管理、航线管理、计划管理等核心功能模块。

## 构建和开发命令

### 后端 (Java/Spring Boot)
```bash
# 编译项目
mvn clean compile

# 打包项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# 运行应用
mvn spring-boot:run -pl ruoyi-admin

# 使用脚本运行 (Linux/Mac)
./ry.sh start    # 启动
./ry.sh stop     # 停止
./ry.sh restart  # 重启
./ry.sh status   # 状态查看

# 使用脚本运行 (Windows)
ry.bat
```

### 前端 (Vue.js)
```bash
# 进入前端目录
cd ruoyi-ui

# 安装依赖
npm install

# 开发环境运行
npm run dev

# 生产环境构建
npm run build:prod

# 代码检查
npm run lint
```

## 项目架构

### 模块结构
- **ruoyi-admin**: 主应用模块，包含启动类和Web控制器配置
- **ruoyi-framework**: 框架核心模块，包含安全配置、数据源配置、缓存配置等
- **ruoyi-system**: 业务系统模块，包含所有业务功能实现
- **ruoyi-common**: 公共工具模块，包含工具类、常量、异常处理等
- **ruoyi-generator**: 代码生成器模块
- **ruoyi-quartz**: 定时任务模块

### 业务模块结构 (ruoyi-system)
- **basic**: 基础资料模块 - 管理港口、码头、箱型、区域等基础数据
- **customer**: 客户管理模块 - 管理客户信息、联系人、银行账户等
- **booking**: 订舱委托模块 - 处理订舱申请和集装箱配载
- **route**: 航线管理模块 - 管理主航线、支线及航线关系
- **plan**: 计划管理模块 - 处理航次计划和订舱关联
- **contract**: 合同管理模块 - 管理运输合同和费用类别
- **order**: 订单管理模块 - 处理业务订单
- **system**: 系统管理模块 - 用户、角色、菜单等系统功能

### 技术栈
- **后端**: Spring Boot 3.3.5, Spring Security, MyBatis-Flex 1.10.4, Redis, JWT
- **前端**: Vue 2.6.12, Element UI 2.15.14, Axios, Vuex
- **数据库**: Oracle (生产), MySQL (开发测试)
- **构建工具**: Maven 3.x, npm/yarn

## 配置说明

### 应用配置
- 默认端口: 8088
- 数据库方言: Oracle (PageHelper配置)
- Redis数据库索引: 6
- 文件上传路径: D:/ruoyi/uploadPath (Windows示例)

### 开发环境设置
- JDK版本: 17
- Node.js版本: >=8.9
- 热部署: 已启用 (spring-devtools)
- 代码生成器: 支持CRUD代码生成

## 代码规范

### 包结构约定
```
com.ruoyi.[module].controller  - REST控制器
com.ruoyi.[module].domain      - 实体类和VO/DTO
com.ruoyi.[module].mapper      - MyBatis映射器
com.ruoyi.[module].service     - 服务接口和实现
```

### 命名约定
- Controller: 以Controller结尾
- Service接口: 以I开头，Service结尾
- Service实现: 以ServiceImpl结尾
- Mapper: 以Mapper结尾
- 实体类: 直接使用业务名称

### 数据库约定
- 逻辑删除字段: del_flag (0=正常, 2=删除)
- 主键策略: 自增ID
- 版本控制字段: version
- 所有表都应包含创建时间、更新时间等审计字段

## API文档
- Swagger UI地址: http://localhost:8088/swagger-ui.html
- API文档路径: http://localhost:8088/v3/api-docs

## MyBatis-Flex ORM框架

### 框架特点
- **轻量**: 除MyBatis外无任何第三方依赖，无拦截器，通过SqlProvider实现
- **灵活**: 内置QueryWrapper支持多表查询、关联查询、子查询等复杂场景
- **高性能**: 无SQL解析过程，带来极高性能表现
- **功能强大**: 支持多主键、逻辑删除、乐观锁、数据脱敏、数据审计、数据填充等

### 文档位置和路径 (bijingrui专用)
项目内置完整MyBatis-Flex文档，正确路径如下：
- **文档根目录**: `/Users/<USER>/Documents/companyProject/shipping/SHIPPING-SPRING3/doc/mybatisFlexDocs/`
- **主文档**: `/Users/<USER>/Documents/companyProject/shipping/SHIPPING-SPRING3/doc/mybatisFlexDocs/index.md`
- **QueryWrapper文档**: `/Users/<USER>/Documents/companyProject/shipping/SHIPPING-SPRING3/doc/mybatisFlexDocs/zh/base/querywrapper.md`
- **Service层文档**: `/Users/<USER>/Documents/companyProject/shipping/SHIPPING-SPRING3/doc/mybatisFlexDocs/zh/base/service.md`
- **乐观锁文档**: `/Users/<USER>/Documents/companyProject/shipping/SHIPPING-SPRING3/doc/mybatisFlexDocs/zh/core/version.md`
- **增删改文档**: `/Users/<USER>/Documents/companyProject/shipping/SHIPPING-SPRING3/doc/mybatisFlexDocs/zh/base/add-delete-update.md`

### 开发最佳实践 (bijingrui开发规范)

#### IService优先原则
- **推荐**: 多使用IService自带方法
- **避免**: 过度使用UpdateWrapper，优先使用实体对象操作
- **原因**: IService方法更简洁、更稳定、更符合框架设计理念

#### Service层极简设计示例
```java
@Service
public class BookingTemplateServiceImpl extends ServiceImpl<BookingTemplateMapper, BookingTemplate> 
        implements IBookingTemplateService {

    // 推荐：使用IService自带方法
    @Override
    public boolean updateTemplate(String templateId, String newName) {
        BookingTemplate template = getById(templateId);
        if (template != null) {
            template.setTemplateName(newName);
            return updateById(template);  // 使用IService自带方法
        }
        return false;
    }
    
    // 推荐：简单查询使用QueryWrapper
    @Override
    public List<BookingTemplate> getUserTemplates(String userId) {
        return list(QueryWrapper.create()
            .where(BOOKING_TEMPLATE.CREATE_BY.eq(userId))
            .orderBy(BOOKING_TEMPLATE.CREATE_TIME.desc()));
    }
}
```

#### 避免的模式
```java
// 避免：复杂的UpdateWrapper用法
UpdateWrapper<BookingTemplate> updateWrapper = new UpdateWrapper<>();
updateWrapper.eq("id", templateId);
updateWrapper.setRaw("usage_count", "usage_count + 1");
mapper.update(null, updateWrapper);

// 推荐：使用实体对象
BookingTemplate template = getById(templateId);
template.setUsageCount(template.getUsageCount() + 1);
updateById(template);
```

## 重要注意事项
- 系统使用MyBatis-Flex 1.10.4作为ORM框架，不是MyBatis-Plus
- 我们真正的前端是在另一个project中（vue3），本目录下的前端我们未使用。
- 数据库主要面向Oracle，分页等配置已针对Oracle优化
- 系统包含Minio文件存储配置，用于文件上传管理
- 验证码类型配置为数学计算验证码