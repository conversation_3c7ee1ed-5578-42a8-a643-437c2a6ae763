package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicMileage;
import com.ruoyi.basic.service.IBasicMileageService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/system/mileage")
public class BasicMileageController extends BaseController {
    @Autowired
    private IBasicMileageService basicMileageService;

    /**
     * 查询码头里程列表
     */
    @PreAuthorize("@ss.hasPermi('system:mileage:list')")
    @GetMapping("/list")
    public Page<BasicMileage> list(BasicMileage basicMileage)
    {
        // startPage();
        // List<BasicMileage> list = basicMileageService.selectBasicMileageList(basicMileage);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicMileage.getStartTerminalId())) {
            queryWrapper.eq("start_terminal_id", basicMileage.getStartTerminalId());
        }
        if (StringUtils.isNotEmpty(basicMileage.getEndTerminalId())) {
            queryWrapper.eq("end_terminal_id", basicMileage.getEndTerminalId());
        }
        queryWrapper.orderBy("create_time", true);
        return basicMileageService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出码头里程列表
     */
    @PreAuthorize("@ss.hasPermi('system:mileage:export')")
    @Log(title = "码头里程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicMileage basicMileage)
    {
        List<BasicMileage> list = basicMileageService.selectBasicMileageList(basicMileage);
        ExcelUtil<BasicMileage> util = new ExcelUtil<BasicMileage>(BasicMileage.class);
        util.exportExcel(response, list, "码头里程数据");
    }

    /**
     * 获取码头里程详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:mileage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicMileageService.selectBasicMileageById(id));
    }

    /**
     * 新增码头里程
     */
    @PreAuthorize("@ss.hasPermi('system:mileage:add')")
    @Log(title = "码头里程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicMileage basicMileage)
    {
        try {
            return toAjax(basicMileageService.insertBasicMileage(basicMileage));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改码头里程
     */
    @PreAuthorize("@ss.hasPermi('system:mileage:edit')")
    @Log(title = "码头里程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicMileage basicMileage)
    {
        try {
            return toAjax(basicMileageService.updateBasicMileage(basicMileage));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除码头里程
     */
    @PreAuthorize("@ss.hasPermi('system:mileage:remove')")
    @Log(title = "码头里程", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicMileageService.deleteBasicMileageByIds(ids));
    }
}
