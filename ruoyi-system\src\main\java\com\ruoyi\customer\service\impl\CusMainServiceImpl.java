package com.ruoyi.customer.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.customer.domain.*;
import com.ruoyi.customer.mapper.CusBankMapper;
import com.ruoyi.customer.mapper.CusBargeSupplierMapper;
import com.ruoyi.customer.mapper.CusContactMapper;
import com.ruoyi.customer.service.*;
import com.ruoyi.customer.mapper.CusMainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【CUS_MAIN】的数据库操作Service实现
 * @createDate 2025-04-27 09:26:46
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CusMainServiceImpl extends ServiceImpl<CusMainMapper, CusMain>
        implements CusMainService{

    @Autowired
    private CusMainMapper cusMainMapper;

    @Autowired
    private CusContactMapper cusContactMapper;

    @Autowired
    private CusBankMapper cusBankMapper;

    @Autowired
    private CusBusinessTypeService cusBusinessTypeService;

    @Autowired
    private CusTypeService cusTypeService;

    @Autowired
    private CusCompanyTypeService cusCompanyTypeService;

    @Autowired
    private CusHistoryService cusHistoryService;

    @Autowired
    private CusBargeSupplierMapper cusBargeSupplierMapper;

    @Autowired
    private CusBargeSupplierService cusBargeSupplierService;

    @Override
    public Page<CusMain> selectCusMainList(CusMain cusMain, Integer pageNum, Integer pageSize) {

        Page<CusMain> page = new Page<>(pageNum,pageSize);

        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        CusMain::getCusId,
                        CusMain::getCusName,
                        CusMain::getCusAbbreviation,
                        CusMain::getCusCode,
                        CusMain::getCusCreditCode,
                        CusMain::getCusIdentity,
                        CusMain::getStatus,
                        CusMain::getIndustry,
                        CusMain::getLegalPersonName,
                        CusMain::getRegStatus,
                        CusMain::getRegCapital,
                        CusMain::getActualCapital,
                        CusMain::getEstablishTime,
                        CusMain::getApprovedTime,
                        CusMain::getFromTime,
                        CusMain::getStaffNumRange,
                        CusMain::getSocialStaffNum,
                        CusMain::getRegInstitute,
                        CusMain::getHistoryName,
                        CusMain::getRegLocation,
                        CusMain::getBusinessScope,
                        CusMain::getInvoiceHeader,
                        CusMain::getInvoiceNumber,
                        CusMain::getInvoiceAddress,
                        CusMain::getInvoiceBank,
                        CusMain::getInvoicePhone,
                        CusMain::getInvoiceBankAccount,
                        CusMain::getInvoiceReceiveAddress,
                        CusMain::getContainerOwner,
                        CusMain::getCreateBy,
                        CusMain::getCreateTime,
                        CusMain::getUpdateBy,
                        CusMain::getUpdateTime,
                        CusMain::getRemark,
                        CusMain::getDelFlag,
                        CusMain::getCusArea,
                        CusMain::getCusRoute
                )
                .select("listagg(distinct cbt.cus_business_type, ',') within group (order by cbt.cus_business_type) as business_types")
                .select("listagg(distinct ct.cus_type, ',') within group (order by ct.cus_type) as cus_types")
                .select("listagg(distinct cct.company_type, ',') within group (order by cct.company_type) as company_types")
                .from(CusMain.class).as("cm")
                .leftJoin(CusBusinessType.class).as("cbt").on(CusMain::getCusId,CusBusinessType::getCusMainId)
                .leftJoin(CusType.class).as("ct").on(CusMain::getCusId,CusType::getCusMainId)
                .leftJoin(CusCompanyType.class).as("cct").on(CusMain::getCusId,CusCompanyType::getCusMainId)
                .like(CusMain::getCusName, cusMain.getCusName())
                .eq(CusMain::getStatus,cusMain.getStatus())
                .eq(CusMain::getDelFlag,"0")
                .groupBy(
                        CusMain::getCusId,
                        CusMain::getCusName,
                        CusMain::getCusAbbreviation,
                        CusMain::getCusCode,
                        CusMain::getCusCreditCode,
                        CusMain::getCusIdentity,
                        CusMain::getStatus,
                        CusMain::getIndustry,
                        CusMain::getLegalPersonName,
                        CusMain::getRegStatus,
                        CusMain::getRegCapital,
                        CusMain::getActualCapital,
                        CusMain::getEstablishTime,
                        CusMain::getApprovedTime,
                        CusMain::getFromTime,
                        CusMain::getStaffNumRange,
                        CusMain::getSocialStaffNum,
                        CusMain::getRegInstitute,
                        CusMain::getHistoryName,
                        CusMain::getRegLocation,
                        CusMain::getBusinessScope,
                        CusMain::getInvoiceHeader,
                        CusMain::getInvoiceNumber,
                        CusMain::getInvoiceAddress,
                        CusMain::getInvoiceBank,
                        CusMain::getInvoicePhone,
                        CusMain::getInvoiceBankAccount,
                        CusMain::getInvoiceReceiveAddress,
                        CusMain::getContainerOwner,
                        CusMain::getCreateBy,
                        CusMain::getCreateTime,
                        CusMain::getUpdateBy,
                        CusMain::getUpdateTime,
                        CusMain::getRemark,
                        CusMain::getDelFlag,
                        CusMain::getCusArea,
                        CusMain::getCusRoute
                );

        QueryWrapper exQuery = new QueryWrapper().with("cm_temp").asSelect(queryWrapper).select().from("cm_temp");

        if(StringUtils.isNotNull(cusMain.getBusinessType()) && !cusMain.getBusinessType().isEmpty()){

            for (String str : cusMain.getBusinessType()){

                String sql = "find_in_set('" + str + "',business_types) > 0";

                exQuery.and(sql);

            }

        }

        if(StringUtils.isNotNull(cusMain.getCusType()) && !cusMain.getCusType().isEmpty()){

            for (String str : cusMain.getCusType()){

                String sql = "find_in_set('" + str + "',cus_types) > 0";

                exQuery.and(sql);

            }

        }

        if(StringUtils.isNotNull(cusMain.getCompanyType()) && !cusMain.getCompanyType().isEmpty()){

            for (String str : cusMain.getCompanyType()){
                String sql = "find_in_set('" + str + "',company_types) > 0";
                exQuery.and(sql);
            }

        }

        exQuery.orderBy("create_time",false);

        Page<CusMain> result = cusMainMapper.paginate(page,exQuery);

        List<CusMain> cusMainList = result.getRecords();

        // 为每个客户查询业务类型等数据
        for (CusMain main : cusMainList) {

            List<String> businessType = cusBusinessTypeService.listAs(QueryWrapper.create().select(CusBusinessType::getCusBusinessType).eq(CusBusinessType::getCusMainId, main.getCusId()),String.class);

            main.setBusinessType(businessType);

            List<String> cusType = cusTypeService.listAs(QueryWrapper.create().select(CusType::getCusType).eq(CusType::getCusMainId, main.getCusId()),String.class);

            main.setCusType(cusType);

            List<String> companyType = cusCompanyTypeService.listAs(QueryWrapper.create().select(CusCompanyType::getCompanyType).eq(CusCompanyType::getCusMainId, main.getCusId()),String.class);

            main.setCompanyType(companyType);

            List<CusBargeSupplier> bargeSuppliers = cusBargeSupplierService.list(QueryWrapper.create()
                    .eq(CusBargeSupplier::getCusMainId, main.getCusId()));

            String bargeSupplier = "";

            for (CusBargeSupplier supplier : bargeSuppliers) {
                bargeSupplier += supplier.getRouteName() + "-" + supplier.getBargeSupplierName() + ";";
            }

            //如果最后一个是逗号 干掉
            if (bargeSupplier.endsWith(";")) {
                bargeSupplier = bargeSupplier.substring(0, bargeSupplier.length() - 1);
            }

            main.setCusBargeSuppliers(bargeSuppliers);
            main.setCusBargeSupplier(bargeSupplier);
        }

        result.setRecords(cusMainList);

        return result;
//        return cusMainList;
    }

    @Override
    public List<CusMain> remoteSearchCusData(String searchKey) {
        // 参数校验
        if (StringUtils.isEmpty(searchKey)) {
            return List.of();
        }

        // 创建查询条件，使用链式操作和实体类引用
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(CusMain.class)
                .where(CusMain::getCusId).eq(searchKey)
                .or(CusMain::getCusName).like(searchKey)
                .or(CusMain::getCusAbbreviation).like(searchKey)
                .or(CusMain::getCusCode).like(searchKey)
                .orderBy(CusMain::getUpdateTime, false)
                .limit(100);

        return list(queryWrapper);
    }

    @Override
    public AjaxResult insertCusMain(CusMain cusMain) {

        if(StringUtils.isNotNull(cusMain.getCusAreas()) && !cusMain.getCusAreas().isEmpty()){

            //将字符串数组转换为逗号分隔的字符串
            String cusArea = String.join(",", cusMain.getCusAreas());
            cusMain.setCusArea(cusArea);

        }

        if(StringUtils.isNotNull(cusMain.getCusRoutes()) && !cusMain.getCusRoutes().isEmpty()){
            //将字符串数组转换为逗号分隔的字符串
            String cusRoute = String.join(",", cusMain.getCusRoutes());
            cusMain.setCusRoute(cusRoute);
        }

        if(cusMainMapper.insert(cusMain) > 0) {

            // 客户新增成功后，新增客户联系人
            List<CusContact> cusContacts = cusMain.getCusContacts();
            if (cusContacts != null && !cusContacts.isEmpty()) {
                for (CusContact cusContact : cusContacts) {
                    cusContact.setCusMainId(cusMain.getCusId());
                    cusContactMapper.insert(cusContact);
                }
            }

            // 客户新增成功后，新增客户银行信息
            List<CusBank> cusBanks = cusMain.getCusBankAccounts();
            if (cusBanks != null && !cusBanks.isEmpty()) {
                for (CusBank cusBank : cusBanks) {
                    cusBank.setCusMainId(cusMain.getCusId());
                    cusBankMapper.insert(cusBank);
                }
            }

            List<CusBargeSupplier> cusBargeSuppliers = cusMain.getCusBargeSuppliers();
            if (StringUtils.isNotNull(cusBargeSuppliers) && !cusBargeSuppliers.isEmpty()) {
                for (CusBargeSupplier cusBargeSupplier : cusBargeSuppliers) {
                    cusBargeSupplier.setCusMainId(cusMain.getCusId());
                    cusBargeSupplierMapper.insert(cusBargeSupplier);
                }
            }

            // 客户新增成功后，新增客户业务类型
            if (cusMain.getBusinessType() != null && !cusMain.getBusinessType().isEmpty()) {
                List<String> businessTypes = cusMain.getBusinessType().stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                for (String businessType : businessTypes) {
                    CusBusinessType cusBusinessType = new CusBusinessType();
                    cusBusinessType.setCusMainId(cusMain.getCusId());
                    cusBusinessType.setCusBusinessType(businessType.trim());
                    cusBusinessTypeService.save(cusBusinessType);
                }
            }

            if(StringUtils.isNotNull(cusMain.getCusType()) && !cusMain.getCusType().isEmpty()){

                List<String> cusTypes = cusMain.getCusType();

                for (String type : cusTypes) {

                    CusType cusType = new CusType();
                    cusType.setCusMainId(cusMain.getCusId());
                    cusType.setCusType(type.trim());
                    cusTypeService.save(cusType);

                }

            }

            if(StringUtils.isNotNull(cusMain.getCompanyType()) && !cusMain.getCompanyType().isEmpty()){

                List<String> companyTypes = cusMain.getCompanyType();

                for (String type : companyTypes) {
                    CusCompanyType cusCompanyType = new CusCompanyType();
                    cusCompanyType.setCusMainId(cusMain.getCusId());
                    cusCompanyType.setCompanyType(type.trim());
                    cusCompanyTypeService.save(cusCompanyType);
                }

            }

            return AjaxResult.success("新增客户成功");
        }
        return AjaxResult.error("新增客户失败");
    }

    @Override
    public AjaxResult updateCusMain(CusMain cusMain) {

        if(StringUtils.isNotNull(cusMain.getCusAreas()) && !cusMain.getCusAreas().isEmpty()){

            //将字符串数组转换为逗号分隔的字符串
            String cusArea = String.join(",", cusMain.getCusAreas());
            cusMain.setCusArea(cusArea);

        }

        if(StringUtils.isNotNull(cusMain.getCusRoutes()) && !cusMain.getCusRoutes().isEmpty()){
            //将字符串数组转换为逗号分隔的字符串
            String cusRoute = String.join(",", cusMain.getCusRoutes());
            cusMain.setCusRoute(cusRoute);
        }
        //保存客户变更历史
        saveCusHistoryInfo(cusMain.getCusId());
        if(cusMainMapper.update(cusMain) > 0) {

            // 处理联系人信息更新，先删除所有联系人，再新增
            // List<CusContact> cusContacts = cusMain.getCusContacts();
            // if (cusContacts != null && !cusContacts.isEmpty()) {
            //     // 删除旧的联系人
            //     cusContactMapper.deleteCusContactByCusId(cusMain.getCusId());
            //
            //     // 新增新的联系人
            //     for (CusContact cusContact : cusContacts) {
            //         cusContact.setCusContactId(null);// 清空主键，避免重复
            //         cusContact.setCusMainId(cusMain.getCusId());
            //         cusContactMapper.insert(cusContact);
            //     }
            // }
            updateCusContact(cusMain.getCusId(), cusMain.getCusContacts());
            // 处理银行信息更新，先删除所有银行信息，再新增
            // List<CusBank> cusBanks = cusMain.getCusBankAccounts();
            // if (cusBanks != null && !cusBanks.isEmpty()) {
            //     // 删除旧的银行信息
            //     cusBankMapper.deleteCusBankByCusId(cusMain.getCusId());
            //
            //     // 新增新的银行信息
            //     for (CusBank cusBank : cusBanks) {
            //         cusBank.setCusBankId(null);// 清空主键，避免重复
            //         cusBank.setCusMainId(cusMain.getCusId());
            //         cusBankMapper.insert(cusBank);
            //     }
            // }
            updateCusBank(cusMain.getCusId(), cusMain.getCusBankAccounts());

            // 处理业务类型更新，先删除所有业务类型，再新增
            updateCusBusinessType(cusMain.getCusId(), cusMain.getBusinessType());

            updateCusType(cusMain.getCusId(), cusMain.getCusType());

            updateCusCompanyType(cusMain.getCusId(), cusMain.getCompanyType());

            updateCusBargeSupplier(cusMain.getCusId(), cusMain.getCusBargeSuppliers());

            return AjaxResult.success("修改客户成功");
        }
        return AjaxResult.error("修改客户失败");
    }

    private void updateCusContact(String cusMainId,List<CusContact> newContacts){
        // 获取原数据库中的联系人列表
        List<CusContact> oldCusContacts = cusContactMapper.selectCusContactListByCusId(cusMainId);

        // 转换为map方便查找
        Map<String, CusContact> oldMap = oldCusContacts.stream()
                .collect(Collectors.toMap(CusContact::getCusContactId, item -> item));
        Map<String, CusContact> newMap = newContacts.stream()
                .filter(item -> item.getCusMainId() != null && item.getCusContactId() != null)
                .collect(Collectors.toMap(CusContact::getCusContactId, item -> item));
        // 1. 处理需要删除的记录
        oldCusContacts.stream()
                .filter(old -> !newMap.containsKey(old.getCusContactId()))
                .forEach(old -> cusContactMapper.deleteById(old.getCusContactId()));

        // 2. 处理需要更新的记录
        newContacts.stream()
                .filter(newContact -> newContact.getCusContactId() != null)
                .forEach(newContact -> {
                    newContact.setCusMainId(cusMainId);
                    cusContactMapper.update(newContact);
                });

        // 3. 处理需要新增的记录
        newContacts.stream()
                .filter(newContact -> newContact.getCusContactId() == null)
                .forEach(newContact -> {
                    newContact.setCusMainId(cusMainId);
                    cusContactMapper.insert(newContact);
                });

    }

    private void updateCusBank(String cusMainId,List<CusBank> newBanks){
        // 获取原数据库中的联系人列表
        List<CusBank> oldCusBanks = cusBankMapper.selectCusBankListByCusId(cusMainId);

        // 转换为map方便查找
        Map<String, CusBank> oldMap = oldCusBanks.stream()
                .collect(Collectors.toMap(CusBank::getCusBankId, item -> item));
        Map<String, CusBank> newMap = newBanks.stream()
                .filter(item -> item.getCusMainId() != null && item.getCusBankId() != null)
                .collect(Collectors.toMap(CusBank::getCusBankId, item -> item));
        // 1. 处理需要删除的记录
        oldCusBanks.stream()
                .filter(old -> !newMap.containsKey(old.getCusBankId()))
                .forEach(old -> cusBankMapper.deleteById(old.getCusBankId()));

        // 2. 处理需要更新的记录
        newBanks.stream()
                .filter(newBank -> newBank.getCusBankId() != null)
                .forEach(newBank -> {
                    newBank.setCusMainId(cusMainId);
                    cusBankMapper.update(newBank);
                });

        // 3. 处理需要新增的记录
        newBanks.stream()
                .filter(newBank -> newBank.getCusBankId() == null)
                .forEach(newBank -> {
                    newBank.setCusMainId(cusMainId);
                    cusBankMapper.insert(newBank);
                });

    }

    private void updateCusBargeSupplier(String cusMainId,List<CusBargeSupplier> newBargeSuppliers){
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(CusBargeSupplier::getCusMainId, cusMainId);

        cusBargeSupplierService.remove(queryWrapper);

        if(StringUtils.isNotNull(newBargeSuppliers) && !newBargeSuppliers.isEmpty()){

            for (CusBargeSupplier bargeSupplier:newBargeSuppliers){
                bargeSupplier.setCusMainId(cusMainId);
                cusBargeSupplierService.save(bargeSupplier);
            }

        }

    }

    private void updateCusType(String cusMainId,List<String> cusType){

        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(CusType::getCusMainId, cusMainId);

        cusTypeService.remove(queryWrapper);

        if(StringUtils.isNotNull(cusType) && !cusType.isEmpty()){

            for (String type:cusType){

                if(StringUtils.isNotEmpty(type)){

                    CusType ct = new CusType();

                    ct.setCusMainId(cusMainId);
                    ct.setCusType(type);
                    cusTypeService.save(ct);

                }

            }

        }

    }

    private void updateCusCompanyType(String cusMainId,List<String> cusCompanyType){

        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(CusCompanyType::getCusMainId, cusMainId);

        cusCompanyTypeService.remove(queryWrapper);

        if(StringUtils.isNotNull(cusCompanyType) && !cusCompanyType.isEmpty()){

            for (String type:cusCompanyType){
                CusCompanyType ct = new CusCompanyType();
                ct.setCusMainId(cusMainId);
                ct.setCompanyType(type);
                cusCompanyTypeService.save(ct);
            }

        }

    }

    private void updateCusBusinessType(String cusMainId, List<String> businessType) {

        System.out.println(cusMainId);
        System.out.println(businessType);

        // 先删除该客户的所有业务类型
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(CusBusinessType.class)
                .where(CusBusinessType::getCusMainId).eq(cusMainId);
        cusBusinessTypeService.remove(queryWrapper);

        // 新增新的业务类型
        if (businessType != null && !businessType.isEmpty()) {

            for (String type : businessType) {
                if (type != null && !type.trim().isEmpty()) {
                    CusBusinessType cusBusinessType = new CusBusinessType();
                    cusBusinessType.setCusMainId(cusMainId);
                    cusBusinessType.setCusBusinessType(type.trim());
                    cusBusinessTypeService.save(cusBusinessType);
                }
            }
        }
    }

    @Override
    public AjaxResult deleteCusMain(String id) {
        if (cusMainMapper.deleteById(id) > 0) {
            return AjaxResult.success("删除客户成功");
        }
        return AjaxResult.error("删除客户失败");
    }

    @Override
    public AjaxResult getCusMainById(String id) {

//        CusMain cusMain = cusMainMapper.selectOneWithRelationsById(id);
//
//        if(StringUtils.isNotNull(cusMain)){
//
//            if(cusMain.getCusBankAccounts().isEmpty()){
//                cusMain.setCusBankAccounts(null);
//            }
//
//            return AjaxResult.success("查询客户成功", cusMain);
//        }else
//            return AjaxResult.error("查询客户失败");

        CusMain cusMain = cusMainMapper.selectOneById(id);
        if (cusMain != null) {

            //将cusMain转换为数组
            if(StringUtils.isNotEmpty(cusMain.getCusArea())){
                String[] cusAreas = cusMain.getCusArea().split(",");
                cusMain.setCusAreas(Arrays.asList(cusAreas));
            }

            if(StringUtils.isNotEmpty(cusMain.getCusRoute())){

                String[] cusRoutes = cusMain.getCusRoute().split(",");
                cusMain.setCusRoutes(Arrays.asList(cusRoutes));

            }

            // 查询客户联系人
            List<CusContact> cusContacts = cusContactMapper.selectCusContactListByCusId(id);
            // 查询客户银行信息
            List<CusBank> cusBanks = cusBankMapper.selectCusBankListByCusId(id);
            // 查询客户业务类型
            QueryWrapper cbtQuery = QueryWrapper.create()
                    .select(CusBusinessType::getCusBusinessType)
                    .from(CusBusinessType.class)
                    .where(CusBusinessType::getCusMainId).eq(id);
            List<String> cusBusinessTypes = cusBusinessTypeService.listAs(cbtQuery,String.class);

            QueryWrapper ctQuery = QueryWrapper.create()
                    .select(CusType::getCusType)
                    .eq(CusType::getCusMainId, id);

            List<String> cusTypes = cusTypeService.listAs(ctQuery,String.class);

            QueryWrapper cptQuery = QueryWrapper.create()
                    .select(CusCompanyType::getCompanyType)
                    .eq(CusCompanyType::getCusMainId, id);

            List<String> cusCompanyType = cusCompanyTypeService.listAs(cptQuery,String.class);

            List<CusBargeSupplier> cusBargeSuppliers = cusBargeSupplierService.list(QueryWrapper.create().eq(CusBargeSupplier::getCusMainId,id));

            // 设置客户联系人
            cusMain.setCusContacts(cusContacts);
            // 设置客户银行信息
            cusMain.setCusBankAccounts(cusBanks);

            cusMain.setBusinessType(cusBusinessTypes);

            cusMain.setCusType(cusTypes);

            cusMain.setCompanyType(cusCompanyType);

            cusMain.setCusBargeSuppliers(cusBargeSuppliers);

            return AjaxResult.success("查询客户成功", cusMain);
        }
        return AjaxResult.error("查询客户失败");
    }


    // 保存客户变更历史
    private void saveCusHistoryInfo(String cusMainId) {
        AjaxResult cusMainById = getCusMainById(cusMainId);
        if(cusMainById.get(AjaxResult.CODE_TAG).equals(HttpStatus.ERROR)){
            return;
        }
        CusMain cusMain = (CusMain) cusMainById.get(AjaxResult.DATA_TAG);
        cusHistoryService.saveCusHistory(cusMain);
    }
}




