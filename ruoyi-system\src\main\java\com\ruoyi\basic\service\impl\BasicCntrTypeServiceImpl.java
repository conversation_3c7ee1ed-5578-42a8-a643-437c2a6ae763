package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicCntrType;
import com.ruoyi.basic.mapper.BasicCntrTypeMapper;
import com.ruoyi.basic.service.IBasicCntrTypeService;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCntrTypeTableDef.BASIC_CNTR_TYPE;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicCntrTypeServiceImpl extends ServiceImpl<BasicCntrTypeMapper, BasicCntrType> implements IBasicCntrTypeService {
    @Autowired
    private BasicCntrTypeMapper basicCntrTypeMapper;

    @Override
    public BasicCntrTypeMapper getMapper() {
        return basicCntrTypeMapper;
    }

    /**
     * 查询箱型
     *
     * @param id 箱型主键
     * @return 箱型
     */
    @Override
    public BasicCntrType selectBasicCntrTypeById(String id)
    {
        return basicCntrTypeMapper.selectOneById(id);
    }

    /**
     * 查询箱型列表
     *
     * @param basicCntrType 箱型
     * @return 箱型
     */
    @Override
    public List<BasicCntrType> selectBasicCntrTypeList(BasicCntrType basicCntrType)
    {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_CNTR_TYPE)
            .where(BASIC_CNTR_TYPE.TYPE_CODE.like(basicCntrType.getTypeCode()))
            .and(BASIC_CNTR_TYPE.TYPE_NAME.like(basicCntrType.getTypeName()))
            .and(BASIC_CNTR_TYPE.DEL_FLAG.eq("0"));
        return this.list(queryWrapper);
    }

    /**
     * 新增箱型
     *
     * @param basicCntrType 箱型
     * @return 结果
     */
    @Override
    public int insertBasicCntrType(BasicCntrType basicCntrType)
    {
        return basicCntrTypeMapper.insert(basicCntrType);
    }

    /**
     * 修改箱型
     *
     * @param basicCntrType 箱型
     * @return 结果
     */
    @Override
    public int updateBasicCntrType(BasicCntrType basicCntrType)
    {
        return basicCntrTypeMapper.update(basicCntrType);
    }

    /**
     * 批量删除箱型
     *
     * @param ids 需要删除的箱型主键集合
     * @return 结果
     */
    @Override
    public int deleteBasicCntrTypeByIds(List<String> ids)
    {
        return basicCntrTypeMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除箱型信息
     *
     * @param id 箱型主键
     * @return 结果
     */
    @Override
    public int deleteBasicCntrTypeById(String id)
    {
        return basicCntrTypeMapper.deleteById(id);
    }

    public void check(BasicCntrType basicCntrType) throws Exception {
        // 检查箱型代码是否唯一
        QueryChain queryChain = QueryChain.of(BasicCntrType.class)
                .and(BASIC_CNTR_TYPE.ID.ne(basicCntrType.getId()).when(StringUtils.isNotEmpty(basicCntrType.getId())))
                .and(BASIC_CNTR_TYPE.TYPE_CODE.eq(basicCntrType.getTypeCode()));

        long checkCode = basicCntrTypeMapper.selectCountByQuery(queryChain);
        if(checkCode > 0){
            throw new Exception("箱型代码不唯一");
        }

        // 检查ISO代码是否唯一
        if(StringUtils.isNotEmpty(basicCntrType.getTypeIso())){
            queryChain = QueryChain.of(BasicCntrType.class)
                    .and(BASIC_CNTR_TYPE.ID.ne(basicCntrType.getId()).when(StringUtils.isNotEmpty(basicCntrType.getId())))
                    .and(BASIC_CNTR_TYPE.TYPE_ISO.eq(basicCntrType.getTypeIso()));

            long checkIso = basicCntrTypeMapper.selectCountByQuery(queryChain);
            if(checkIso > 0){
                throw new Exception("ISO代码不唯一");
            }
        }

        // 检查公共箱型代码是否唯一
        if(StringUtils.isNotEmpty(basicCntrType.getTypeCommonCode())){
            queryChain = QueryChain.of(BasicCntrType.class)
                    .and(BASIC_CNTR_TYPE.ID.ne(basicCntrType.getId()).when(StringUtils.isNotEmpty(basicCntrType.getId())))
                    .and(BASIC_CNTR_TYPE.TYPE_COMMON_CODE.eq(basicCntrType.getTypeCommonCode()));

            long checkCommon = basicCntrTypeMapper.selectCountByQuery(queryChain);
            if(checkCommon > 0){
                throw new Exception("公共箱型代码不唯一");
            }
        }
    }

    private boolean checkUnique(BasicCntrType basicCntrType) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(BASIC_CNTR_TYPE)
            .where(BASIC_CNTR_TYPE.TYPE_CODE.eq(basicCntrType.getTypeCode()))
            .and(BASIC_CNTR_TYPE.DEL_FLAG.eq("0"));
        if (basicCntrType.getId() != null) {
            queryWrapper.and(BASIC_CNTR_TYPE.ID.ne(basicCntrType.getId()));
        }
        return this.count(queryWrapper) == 0;
    }

}
