package com.ruoyi.contract.domain;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同流向码头顺序表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "contract_flow_terminal")
public class ContractFlowTerminal extends BaseEntity {
    
    /**
     * 流向码头ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String flowTerminalId;
    
    /**
     * 流向ID
     */
    @NotEmpty(message = "流向ID不能为空")
    private String flowId;
    
    /**
     * 码头ID
     */
    @NotEmpty(message = "码头ID不能为空")
    private String terminalId;
    
    /**
     * 顺序号
     */
    @NotNull(message = "顺序号不能为空")
    private Integer orderNum;
    
    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String delFlag;
    
    /**
     * 版本号
     */
    private Integer version;
} 