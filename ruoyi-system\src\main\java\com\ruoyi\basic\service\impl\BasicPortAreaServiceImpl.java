package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicPortArea;
import com.ruoyi.basic.mapper.BasicPortAreaMapper;
import com.ruoyi.basic.service.IBasicPortAreaService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class BasicPortAreaServiceImpl extends ServiceImpl<BasicPortAreaMapper, BasicPortArea> implements IBasicPortAreaService {

    @Autowired
    private BasicPortAreaMapper basicPortAreaMapper;

    /**
     * 查询港区管理
     *
     * @param id 港区管理主键
     * @return 港区管理
     */
    @Override
    public BasicPortArea selectBasicPortAreaById(String id)
    {
        return super.getById(id);
    }

    /**
     * 查询港区管理列表
     *
     * @param basicPortArea 港区管理
     * @return 港区管理
     */
    @Override
    public List<BasicPortArea> selectBasicPortAreaList(BasicPortArea basicPortArea)
    {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicPortArea.getPortAreaCode())) {
            queryWrapper.like("port_area_code", basicPortArea.getPortAreaCode());
        }
        if (StringUtils.isNotEmpty(basicPortArea.getPortAreaName())) {
            queryWrapper.like("port_area_name", basicPortArea.getPortAreaName());
        }
        queryWrapper.orderBy("create_time", true);
        return super.list(queryWrapper);
    }

    /**
     * 新增港区管理
     *
     * @param basicPortArea 港区管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBasicPortArea(BasicPortArea basicPortArea) throws Exception {
        checkValid(basicPortArea);
        return basicPortAreaMapper.insert(basicPortArea);
    }

    /**
     * 修改港区管理
     *
     * @param basicPortArea 港区管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBasicPortArea(BasicPortArea basicPortArea) throws Exception {
        checkValid(basicPortArea);
        return basicPortAreaMapper.update(basicPortArea);
    }

    /**
     * 批量删除港区管理
     *
     * @param ids 需要删除的港区管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBasicPortAreaByIds(List<String> ids)
    {
        return basicPortAreaMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除港区管理信息
     *
     * @param id 港区管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBasicPortAreaById(String id)
    {
        return basicPortAreaMapper.deleteById(id);
    }

    public void checkValid(BasicPortArea basicPortArea) throws Exception {

        if(StringUtils.isEmpty(basicPortArea.getPortAreaCode())){
            throw new Exception("港区代码不能为空");
        }

        if(StringUtils.isEmpty(basicPortArea.getPortAreaName())){
            throw new Exception("港区名称不能为空");
        }

    }

}
