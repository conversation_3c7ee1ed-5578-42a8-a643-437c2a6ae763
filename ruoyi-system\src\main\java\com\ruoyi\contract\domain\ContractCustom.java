package com.ruoyi.contract.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 合同 客户 关联 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Table(value = "contract_custom")
@Data
public class ContractCustom {

    /**
     * 合同ID
     */
    @Column(value = "contract_id")
    private String contractId;

    /**
     * 客户ID
     */
    @Column(value = "cus_id")
    private String cusId;

    /**
     * 合同角色 关联字典 contract_role
     */
    @Column(value = "contract_role")
    private String contractRole;


}
