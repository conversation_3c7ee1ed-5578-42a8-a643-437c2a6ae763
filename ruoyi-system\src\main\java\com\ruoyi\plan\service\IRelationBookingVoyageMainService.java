package com.ruoyi.plan.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.plan.domain.RelationBookingVoyageMain;
import java.util.List;

public interface IRelationBookingVoyageMainService extends IService<RelationBookingVoyageMain> {
    RelationBookingVoyageMain selectById(String id);
    int insert(RelationBookingVoyageMain entity);
    int update(RelationBookingVoyageMain entity);
    int deleteByIds(String[] ids);
    int deleteById(String id);
} 