-- 重建码头连接关系表
-- 创建时间：2025年6月5日

-- 1. 删除原有的码头连接关系表
DROP TABLE ROUTE_RELATION;

-- 2. 重新创建码头连接关系表
CREATE TABLE ROUTE_RELATION (
    RELATION_ID VARCHAR2(32) NOT NULL,
    BRANCH_ID VARCHAR2(32) NOT NULL,
    START_POINT_ID VARCHAR2(50) NOT NULL,
    END_POINT_ID VARCHAR2(50) NOT NULL,
    DISTANCE_KM NUMBER(10,2),
    DURATION_HOUR NUMBER(10,2),
    REMARK VARCHAR2(255),
    CREATE_BY VARCHAR2(50),
    CREATE_TIME DATE,
    UPDATE_BY VARCHAR2(50),
    UPDATE_TIME DATE,
    DEL_FLAG VARCHAR2(1) DEFAULT '0',
    VERSION NUMBER(10) DEFAULT 0,
    CONSTRAINT PK_ROUTE_RELATION PRIMARY KEY (RELATION_ID),
    CONSTRAINT FK_RELATION_BRANCH FOREIGN KEY (BRANCH_ID) 
        REFERENCES ROUTE_BRANCH(BRANCH_ID),
    CONSTRAINT FK_RELATION_START_TERMINAL FOREIGN KEY (START_POINT_ID) 
        REFERENCES BASIC_TERMINAL(ID),
    CONSTRAINT FK_RELATION_END_TERMINAL FOREIGN KEY (END_POINT_ID) 
        REFERENCES BASIC_TERMINAL(ID)
);

-- 添加表注释
COMMENT ON TABLE ROUTE_RELATION IS '码头连接关系表';

-- 添加字段注释
COMMENT ON COLUMN ROUTE_RELATION.RELATION_ID IS '连接ID（主键），雪花ID';
COMMENT ON COLUMN ROUTE_RELATION.BRANCH_ID IS '关联支线表ID（外键）';
COMMENT ON COLUMN ROUTE_RELATION.START_POINT_ID IS '起点码头ID（外键，关联BASIC_TERMINAL表）';
COMMENT ON COLUMN ROUTE_RELATION.END_POINT_ID IS '终点码头ID（外键，关联BASIC_TERMINAL表）';
COMMENT ON COLUMN ROUTE_RELATION.DISTANCE_KM IS '距离（公里）';
COMMENT ON COLUMN ROUTE_RELATION.DURATION_HOUR IS '时间（小时）';
COMMENT ON COLUMN ROUTE_RELATION.REMARK IS '备注';
COMMENT ON COLUMN ROUTE_RELATION.CREATE_BY IS '创建人';
COMMENT ON COLUMN ROUTE_RELATION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN ROUTE_RELATION.UPDATE_BY IS '更新人';
COMMENT ON COLUMN ROUTE_RELATION.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN ROUTE_RELATION.DEL_FLAG IS '逻辑删除标志';
COMMENT ON COLUMN ROUTE_RELATION.VERSION IS '乐观锁'; 