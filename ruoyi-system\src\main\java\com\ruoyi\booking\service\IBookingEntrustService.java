package com.ruoyi.booking.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.booking.domain.BookingMain;
import com.ruoyi.booking.domain.dto.BookingEntrustDTO;
import com.ruoyi.booking.domain.vo.BatchDeleteResultVO;

/**
 * 订舱委托服务接口
 * 核心业务逻辑接口，处理单证员工作台的四大核心功能
 */
public interface IBookingEntrustService extends IService<BookingMain> {
    

    /**
     * 新增订舱委托
     * 
     * @param dto 委托数据传输对象，包含订单主表、订舱主表、物流主表列表等完整信息
     * @return 是否成功
     * @apiNote 核心业务逻辑：新增订舱委托
     *          功能特性：
     *          1. 自动生成委托编号（WT+yyyyMMdd+6位序号）和订舱号（BK+yyyyMMdd+6位序号）
     *          2. 支持批量保存物流明细、箱量信息、中转港信息
     *          3. 为每个物流段自动创建对应的水路运输记录（LOGISTIC_WATERWAY）
     *          4. 完整的事务回滚保障和乐观锁版本控制
     *          5. 自动填充托运单位信息（从bookingMain复制到orderMain）
     *          6. 支持空数据过滤和字段默认值设置
     * @throws Exception 当数据验证失败、业务逻辑错误或数据库操作失败时抛出
     * @since V2.0 单证员工作台新增模式，增加自动创建水路运输记录功能
     */
    boolean insertEntrust(BookingEntrustDTO dto);
    
    /**
     * 修改订舱委托
     * @param dto 委托数据传输对象
     * @return 是否成功
     */
    boolean updateEntrust(BookingEntrustDTO dto);
    
    /**
     * 根据订舱ID查询委托详情
     * 
     * @param bookingId 订舱ID (booking_id)，注意：此参数为booking_main表的主键ID，不是order_main的ID
     * @return 委托详情DTO，包含三层架构数据：orderMain → bookingMain → logisticsMainList
     * @apiNote 该方法通过booking_id查询完整的委托数据，支持单证员工作台查看/编辑模式
     * @since V2.0 单证员工作台查看模式
     */
    BookingEntrustDTO selectEntrustById(String bookingId);
    
    /**
     * 批量删除订舱委托（智能级联删除）
     * 
     * @param bookingIds BookingMain的ID数组，支持单个或多个订舱记录的批量删除
     * @return 删除操作的详细统计结果，包含删除的订舱数量、订单数量以及子表清理统计
     * @throws Exception 当数据库操作失败或发生业务逻辑错误时抛出异常
     * @apiNote 核心删除逻辑：
     *          阶段1：删除BookingMain关联的所有子表记录（物流、箱量、中转港、水路运输）
     *          阶段2：删除BookingMain记录本身
     *          阶段3：检测关联的OrderMain是否还有其他BookingMain
     *          阶段4：删除无其他关联的OrderMain记录（智能清理无关联订单）
     *          
     *          技术特性：
     *          1. 使用MyBatis-Flex原生逻辑删除机制，自动设置del_flag标志
     *          2. 完整的事务保障，任何阶段失败都会回滚所有操作
     *          3. 批量操作优化，避免N+1查询问题
     *          4. 详细的删除统计和日志记录，便于审计和故障排查
     *          5. 优雅的命名设计，避免使用"孤儿"等不专业词汇
     * @since V2.0 单证员工作台批量删除功能
     */
    BatchDeleteResultVO deleteBatchBookingEntrusts(String[] bookingIds);
}