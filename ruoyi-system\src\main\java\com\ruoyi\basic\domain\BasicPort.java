package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_port")
public class BasicPort extends BaseEntity {
    /** 港口ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 港口代码 */
    @Excel(name = "港口代码")
    private String portCode;

    /** 国家/地区 */
    @Excel(name = "国家/地区")
    private String couId;

    /** 区域 */
    @Excel(name = "区域")
    private String areaId;

    /** 港口名称 */
    @Excel(name = "港口名称")
    private String portName;

    /** 港口英文名称 */
    @Excel(name = "港口英文名称")
    private String portEnglishName;

    /** 港口X坐标 */
    @Excel(name = "港口X坐标")
    private Long positionX;

    /** 港口Y坐标 */
    @Excel(name = "港口Y坐标")
    private Long positionY;

    /** 支线名称 */
    @Excel(name = "支线名称")
    private String kilometers;

    /** 乐观锁 */
    @Column(version = true)
    private Long version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;
}
