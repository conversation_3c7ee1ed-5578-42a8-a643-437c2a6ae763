import request from '@/utils/request'

/**
 * 获取待运输货物列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getPendingCargoList(params) {
    return request({
        url: '/booking/logisticWaterway/list',
        method: 'get',
        params
    })
}


/**
 * 拆解货物
 * @param {String|Number} id 货物ID
 * @param {Array} splitRecords 拆解记录
 * @returns {Promise}
 */
export function splitCargo(id, splitRecords) {
    return request({
        url: '/booking/logisticWaterway/split',
        method: 'post',
        data: {
            id,
            splitRecords
        }
    })
}

/**
 * 合并货物
 * @param {Array} ids 要合并的货物ID数组
 * @returns {Promise}
 */
export function mergeCargo(ids) {
  return request({
    url: '/booking/logisticWaterway/merge',
    method: 'post',
    data: ids
  })
}

export function getInfo(id){
    return request({
        url: '/booking/logisticWaterway/info/' + id,
        method: 'get'
    })
}