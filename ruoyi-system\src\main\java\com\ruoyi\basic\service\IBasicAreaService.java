package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicArea;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface IBasicAreaService extends IService<BasicArea> {
    /**
     * 查询区域管理
     *
     * @param id 区域管理主键
     * @return 区域管理
     */
    public BasicArea selectBasicAreaById(String id);

    /**
     * 查询区域管理列表
     *
     * @param basicArea 区域管理
     * @return 区域管理集合
     */
    public List<BasicArea> selectBasicAreaList(BasicArea basicArea);

    /**
     * 新增区域管理
     *
     * @param basicArea 区域管理
     * @return 结果
     */
    public int insertBasicArea(BasicArea basicArea) throws Exception;

    /**
     * 修改区域管理
     *
     * @param basicArea 区域管理
     * @return 结果
     */
    public int updateBasicArea(BasicArea basicArea) throws Exception;

    /**
     * 批量删除区域管理
     *
     * @param ids 需要删除的区域管理主键集合
     * @return 结果
     */
    public int deleteBasicAreaByIds(List<String> ids) throws Exception;


    @Transactional(rollbackFor = Exception.class)
    int deleteBasicAreaById(String id);
}
