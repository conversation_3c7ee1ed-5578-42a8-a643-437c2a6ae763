package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicZoneTerminal;
import com.ruoyi.basic.mapper.BasicZoneTerminalMapper;
import com.ruoyi.basic.service.IBasicZoneTerminalService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicZoneTerminalServiceImpl extends ServiceImpl<BasicZoneTerminalMapper, BasicZoneTerminal> implements IBasicZoneTerminalService {
    @Autowired
    private BasicZoneTerminalMapper basicZoneTerminalMapper;

    /**
     * 查询关区码头关联
     *
     * @param id 关区码头关联主键
     * @return 关区码头关联
     */
    @Override
    public BasicZoneTerminal selectBasicZoneTerminalById(String id)
    {
        return basicZoneTerminalMapper.selectOneById(id);
    }

    /**
     * 查询关区码头关联列表
     *
     * @param basicZoneTerminal 关区码头关联
     * @return 关区码头关联
     */
    @Override
    public List<BasicZoneTerminal> selectBasicZoneTerminalList(BasicZoneTerminal basicZoneTerminal)
    {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicZoneTerminal.getZoneId())) {
            queryWrapper.eq("zone_id", basicZoneTerminal.getZoneId());
        }
        if (StringUtils.isNotEmpty(basicZoneTerminal.getTerminalId())) {
            queryWrapper.eq("terminal_id", basicZoneTerminal.getTerminalId());
        }
        queryWrapper.orderBy("create_time", true);
        return basicZoneTerminalMapper.selectListByQuery(queryWrapper);
    }

    /**
     * 新增关区码头关联
     *
     * @param basicZoneTerminal 关区码头关联
     * @return 结果
     */
    @Override
    public int insertBasicZoneTerminal(BasicZoneTerminal basicZoneTerminal) throws Exception {
        return basicZoneTerminalMapper.insert(basicZoneTerminal);
    }

    /**
     * 修改关区码头关联
     *
     * @param basicZoneTerminal 关区码头关联
     * @return 结果
     */
    @Override
    public int updateBasicZoneTerminal(BasicZoneTerminal basicZoneTerminal) throws Exception {
        return basicZoneTerminalMapper.update(basicZoneTerminal);
    }

    /**
     * 批量删除关区码头关联
     *
     * @param ids 需要删除的关区码头关联主键集合
     * @return 结果
     */
    @Override
    public int deleteBasicZoneTerminalByIds(List<String> ids)
    {
        return basicZoneTerminalMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除关区码头关联信息
     *
     * @param id 关区码头关联主键
     * @return 结果
     */
    @Override
    public int deleteBasicZoneTerminalById(String id)
    {
        return basicZoneTerminalMapper.deleteById(id);
    }
} 