INSERT INTO "BASIC_ANC_BERTH" ("ID", "<PERSON>B_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMAR<PERSON>", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "<PERSON>B_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10001', '42SJ', '42SJ', NULL, NULL, NULL, '1', '10001', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "<PERSON>B_DEPTH", "REMARK", "<PERSON><PERSON>_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10021', 'WIE', '待装锚位', '33', '33', '33', '1', '10020', '33', '33', '33', '33');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10022', 'IEW', '危险品锚地', NULL, '22', '22', '1', '10020', '22', '22', '22', '22');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10023', 'RIE', '进口锚地', '55', '55', '55', '1', '10021', '44', '55', '55', '55');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10024', 'IIE', '11', '11', '11', '11', '0', '10022', '11', '11', '11', '11');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10077', '112343', '112343', NULL, NULL, NULL, '0', '10047', NULL, '123', NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10078', '112343455', '112343455', NULL, NULL, NULL, '0', '10047', NULL, '1 2 3 ', NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10079', '8SM', '8SM', NULL, '22.5', NULL, '0', '10096', '2000', '21°01′00″N    113°56′00″E', '泥沙', '超大型船舶作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10080', 'LA', 'LA', NULL, NULL, NULL, '0', '10091', NULL, NULL, '泥沙', NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10081', '18GS', '18GS', NULL, NULL, NULL, '1', '10052', '3700', NULL, '泥沙', '四点连线范围内水域为大型船舶候潮、检疫、引航及防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10082', '21DY', '21DY', NULL, '19', NULL, '1', '10053', '800', '22°12′20″N    113°48′22″E', '泥沙', '油轮作业、防台、锚地');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10083', '37SJ', '37SJ', NULL, '11.5', NULL, '0', '10092', '370', '22°41′57″N   113°41′38″E', '沙泥', '油轮作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10084', 'DCF6X7', 'DCF6X7', NULL, '9', NULL, '0', '10095', '250', NULL, '泥沙', '油轮、货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10085', 'DCF5X6', 'DCF5X6', NULL, '11', NULL, '0', '10095', '250', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10086', 'DCF4X5', 'DCF4X5', NULL, '10', NULL, '0', '10095', '260', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10087', 'DCF2X3', 'DCF2X3', NULL, '9.5', NULL, '0', '10095', '230', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10088', 'DCF1X2', 'DCF1X2', NULL, '10', NULL, '0', '10095', '270', NULL, '泥沙', '油轮、货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10089', 'DF8X9', 'DF8X9', NULL, '9.5', NULL, '1', '10094', '260', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10090', 'DF7X8', 'DF7X8', NULL, '9.5', NULL, '0', '10094', '300', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10091', 'DF6X7', 'DF6X7', NULL, '9.5', NULL, '0', '10094', '240', NULL, '泥沙', '货轮');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10092', 'DF5X6', 'DF5X6', NULL, '9.5', NULL, '0', '10094', '240', NULL, '泥沙', '货轮');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10093', 'DF4X5', 'DF4X5', NULL, '9.5', NULL, '0', '10094', '240', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10094', 'DF3X4', 'DF3X4', NULL, '9.5', NULL, '0', '10094', '220', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10095', 'NF1X2', 'NF1X2', NULL, '12.5', NULL, '0', '10093', '330', NULL, '泥沙', '货轮、油轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10096', 'NF0X2', 'NF0X2', NULL, '11.5', NULL, '0', '10093', '330', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10097', '48SJ', '48SJ', NULL, '9.5', NULL, '0', '10092', '370', '22°45′41″N   113°38′51″E', '沙泥', '货船作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10098', '46SJ', '46SJ', NULL, '12.5', NULL, '0', '10092', '370', '22°45′05.7″N   113°38′34.3″E', '沙泥', '货船作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10099', '42SJ', '42SJ', NULL, '13.5', NULL, '0', '10092', '370', '22°43′44″N   113°40′22″E', '沙泥', '货船作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10100', '41SJ', '41SJ', NULL, '14.5', NULL, '0', '10092', '370', '22°43′23″N   113°40′39″E', '沙泥', '货船作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10101', '40SJ', '40SJ', NULL, '14.5', NULL, '0', '10092', '370', '22°42′58″N   113°40′50″E', '岩', '货船、油轮作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10102', '39SJ', '39SJ', NULL, '13.2', NULL, '0', '10092', '370', '22°42′37.3″N   113°41′05.9″E', '沙泥', '油轮作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10103', '38SJ', '38SJ', NULL, '13', NULL, '0', '10092', '370', '22°42′17.2″N   113°41′21.9″E', '沙泥', '油轮作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10025', 'QQW', 'QQW', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10026', 'QQE', 'QQE', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10027', 'QQR', 'QQR', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10028', 'QQY', 'QQT', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10029', 'QQU', 'QQY', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10030', 'QQI', 'QQU', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10031', 'QQO', 'QQI', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10032', 'QQP', 'QQO', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10033', 'QQA', 'QQP', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10034', 'QQS', 'QQA', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10035', 'QQD', 'QQS', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10036', 'QQF', 'QQD', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10037', 'QQG', 'QQF', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10038', 'QQH', 'QQG', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10039', 'QQJ', 'QQH', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10040', 'QQK', 'QQJ', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10041', 'QQL', 'QQK', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10042', 'QWE', 'QWE', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10043', 'QWR', 'QWR', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10044', 'QWT', 'QWY', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10045', 'QWY', 'QWU', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10046', 'QWU', 'QWI', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10047', 'QWI', 'QWO', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10048', 'QWO', 'QWP', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10049', 'QWP', 'QWA', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10050', 'QWA', 'QWS', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10051', 'QWS', 'QWD', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10052', 'QWD', 'QWF', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10053', 'QWF', 'QWG', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10054', 'QWG', 'QWH', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10055', 'QWH', 'QWJ', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10056', 'QWJ', 'QWK', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10057', 'QWK', 'QWL', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10058', 'QWL', 'QWZ', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10059', 'QWZ', 'QWX', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10060', 'QWX', 'QWC', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10061', 'QWC', 'QWV', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10062', 'QWV', 'QWB', NULL, '50', NULL, '0', '10091', '100', NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10063', 'QWB', 'QWM', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10064', 'QWN', 'QWN', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10065', 'QWM', 'QAZ', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10066', 'AAQ', 'WSX', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10067', 'SSW', 'EDC', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10068', 'DDE', 'RRF', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10069', 'FFR', 'RFV', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10070', 'GGT', 'TGB', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10071', 'HHY', 'YHN', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10072', 'JJU', 'UJM', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10073', 'KKI', 'IKM', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10074', 'LLO', 'OOL', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10075', 'LLP', 'PPL', NULL, NULL, NULL, '1', '10051', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10002', '2SJ', '2SJ', NULL, NULL, NULL, '1', '10001', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10000', '12', '12', '12', '12', '123', '1', '10000', '12', '12', '12', '12');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10003', '22SJ', '4SJ', NULL, NULL, NULL, '1', '10001', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10004', '23SJ', '4SJ', NULL, NULL, NULL, '1', '10001', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10005', '1', '1', '1', '1', '1', '0', '10003', '1', '1', '1', '1');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10076', '111', '12', NULL, '21', NULL, '0', '10043', '21', '23', '12', NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10104', '22DY', '22DY', NULL, NULL, NULL, '1', '10053', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10108', '1W', '1W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10113', '3X4W', '3X4W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10119', 'Y1', '21DY', NULL, NULL, NULL, '0', '10097', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10120', 'Y2', '22DY', NULL, NULL, NULL, '0', '10097', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10121', 'Y3', '23DY', NULL, NULL, NULL, '0', '10097', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10122', 'HJ1W', '1W', NULL, NULL, NULL, '0', '10112', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10123', 'HJ1X2W', '1X2W', NULL, NULL, NULL, '0', '10112', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10124', 'HJ2W', '2W', NULL, NULL, NULL, '0', '10112', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10135', 'NSSQ16', '16W', NULL, NULL, NULL, '0', '34', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10136', 'NSSQ15', '15W', NULL, NULL, NULL, '0', '34', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10137', 'NSSQ14', '14W', NULL, NULL, NULL, '0', '34', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10138', 'NSSQ13', '13W', NULL, NULL, NULL, '0', '34', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10139', 'NSSQ1415', '14X15W', NULL, NULL, NULL, '0', '34', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10145', 'XJ2W', '2W', NULL, NULL, NULL, '0', '23', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10158', 'XS7', '7W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10161', '7SM', '7SM', NULL, '22.5', NULL, '0', '10096', '2000', NULL, '泥沙', '超大型船舶作业、防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10165', '150', '交椅沙', NULL, NULL, NULL, '1', '10092', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10166', 'JYS', '交椅沙', NULL, NULL, NULL, '1', '10092', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10167', 'THG', '天后宫', NULL, NULL, NULL, '1', '10092', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10168', '32LD', '32LD', NULL, NULL, NULL, '1', '10092', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10109', '1X2W', '1X2W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10110', '2W', '2W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10111', '2X3W', '2X3W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10112', '3W', '3W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10126', 'XG1W', '1W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10127', 'XG2X3W', '2W', NULL, NULL, NULL, '1', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10128', 'XG3X4W', '3W', NULL, NULL, NULL, '1', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10129', 'XG4X5W', '4W', NULL, NULL, NULL, '1', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10130', 'XG5X6W', '5W', NULL, NULL, NULL, '1', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10134', 'XG4X5W', '5W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10140', 'XS5', '5W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10141', 'XS6', '6W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10142', 'XS4', '4W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10146', 'XG2X3', '2X3W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10147', 'XG3x4', '3X4W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10148', 'XG4x5', '4X5W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10149', 'XG1X2', '1X2W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10150', 'HP1', '1W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10151', 'HP2', '2W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10152', 'HP3', '3W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10153', 'HP12', '1X2W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10154', 'HP23', '2X3W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10155', 'HPHSS', '洪1W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10156', 'HPHSS1', '洪2W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10157', 'HPHSS2', '洪1X2W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10160', 'DCF3×4', 'DCF3×4', NULL, '9', NULL, '0', '10095', '150', NULL, '泥沙', '货轮作业');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10162', 'HPLG52', '52W', NULL, NULL, NULL, '0', '10065', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10163', '100', '1W', NULL, NULL, NULL, '0', '124', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10169', 'XS11', '11W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10170', 'XS12', '12W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10105', '18GS', '18GS', NULL, NULL, NULL, '0', '10091', '3700', NULL, NULL, '四点连线范围内水域为大型船舶候潮、检疫、引航及防台');
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10106', 'NSSQ1516', '13X14W', NULL, NULL, NULL, '0', '34', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10107', 'NSSQ1314', '15X16W', NULL, NULL, NULL, '0', '34', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10114', '4W', '4W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10115', '4X5W', '4X5W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10116', '5W', '5W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10117', '5X6W', '5X6W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10118', '6W', '6W', NULL, NULL, NULL, '0', '37', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10125', '新港1W', NULL, NULL, NULL, NULL, '1', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10131', 'XG1X2W', '2W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10132', 'XG2X3W', '3W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10133', 'XG3X4W', '4W', NULL, NULL, NULL, '0', '25', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10143', 'XS2X3', '2X3W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10144', 'XJ1W', '1W', NULL, NULL, NULL, '0', '23', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10159', 'XS8', '8W', NULL, NULL, NULL, '0', '32', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10164', '9', '交椅沙', NULL, NULL, NULL, '1', '10092', NULL, NULL, NULL, NULL);
INSERT INTO "BASIC_ANC_BERTH" ("ID", "ANB_CODE", "ANB_CNAME", "ANB_TON", "ANB_DEPTH", "REMARK", "DEL_FLAG", "TERMINAL_ID", "ANB_LENGTH", "ANB_CENTER", "ANB_MATERIAL", "ANB_FUNCTION") VALUES ('10171', '36SJ', '36SJ', NULL, '10.7', NULL, '0', '10092', '370', '22°41′37″N   113°41′54″E', '沙泥', '货船、油轮作业、防台');
UPDATE BASIC_ANC_BERTH SET DEL_FLAG = '2' WHERE  DEL_FLAG != '0'