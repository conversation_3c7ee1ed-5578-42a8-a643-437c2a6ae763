package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_cntr_type")
public class BasicCntrType extends BaseEntity {

    /** 箱型ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 箱型代码 */
    @Excel(name = "箱型代码")
    private String typeCode;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String typeName;

    /** 箱型ISO代码 */
    @Excel(name = "箱型ISO代码")
    private String typeIso;

    /** 公共箱型代码 */
    @Excel(name = "公共箱型代码")
    private String typeCommonCode;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

}
