package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_ship_main")
public class BasicShipMain extends BaseEntity {

    /**
     * 船舶主键ID
     */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /**
     * 船舶中文名
     */
    @Column("ship_chinese_name")
    private String shipChineseName;

    /**
     * 船舶英文名
     */
    @Column("ship_english_name")
    private String shipEnglishName;

    /**
     * 船舶代码
     */
    @Column("ship_code")
    private String shipCode;

    /**
     * MMSI
     */
    @Column("mmsi")
    private String mmsi;

    /**
     * IMO
     */
    @Column("imo")
    private String imo;

    /**
     * 呼号
     */
    @Column("call_sign")
    private String callSign;

    /**
     * 海关编码
     */
    @Column("hs_code")
    private String hsCode;

    /**
     * 所属船公司
     */
    @Column("shipping_line")
    private String shippingLine;

    /**
     * 船舶类型
     */
    @Column("ship_type")
    private String shipType;

    /**
     * 船长
     */
    @Column("ship_length")
    private BigDecimal shipLength;

    /**
     * 船宽
     */
    @Column("ship_width")
    private BigDecimal shipWidth;

    /**
     * 船高
     */
    @Column("ship_height")
    private BigDecimal shipHeight;

    /**
     * 净吨
     */
    @Column("net_tonnage")
    private BigDecimal netTonnage;

    /**
     *  载重吨
     */
    @Column("dead_weight_tonnage")
    private BigDecimal deadWeightTonnage;

    /**
     * 载箱量
     */
    @Column("container_capacity")
    private BigDecimal containerCapacity;

    /**
     * 吃水
     */
    @Column("draft")
    private BigDecimal draft;

    /**
     * 乐观锁
     */
    @Column("version")
    private Long version;

    @Column(isLogicDelete = true)
    private String delFlag;

}
