package com.ruoyi.contract.service.impl;


import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.contract.domain.FeeCategory;
import com.ruoyi.contract.mapper.FeeCategoryMapper;
import com.ruoyi.contract.service.FeeCategoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.ruoyi.contract.domain.table.FeeCategoryTableDef.FEE_CATEGORY;

/**
 * 费目分类 服务层实现。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class FeeCategoryServiceImpl extends ServiceImpl<FeeCategoryMapper, FeeCategory> implements FeeCategoryService {

    @Override
    public Page<FeeCategory> selectPage(FeeCategory feeCategory, Integer pageNum, Integer pageSize) {
        Page<FeeCategory> page = new Page<>(pageNum, pageSize);
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(FEE_CATEGORY);
                
        if (StringUtils.isNotEmpty(feeCategory.getCategoryName())) {
            queryWrapper.like(FeeCategory::getCategoryName, feeCategory.getCategoryName());
        }
        
        if (StringUtils.isNotEmpty(feeCategory.getFeeType())) {
            queryWrapper.like(FeeCategory::getFeeType, feeCategory.getFeeType());
        }
        
        return super.page(page, queryWrapper);
    }
}