package com.ruoyi.order.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.order.domain.OrderMain;
import com.ruoyi.order.mapper.OrderMainMapper;
import com.ruoyi.order.service.IOrderMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Service
public class OrderMainServiceImpl extends ServiceImpl<OrderMainMapper, OrderMain> implements IOrderMainService {
    
    @Override
    public Page<OrderMain> selectOrderMainPage(int pageNumber, int pageSize, OrderMain orderMain) {
        log.debug("分页查询订单主表 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, orderMain);
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from("ORDER_MAIN")
                .where("DEL_FLAG = '0'");
                
        if (orderMain != null) {
            if (StringUtils.isNotBlank(orderMain.getOrderNo())) {
                queryWrapper.and("ORDER_NO LIKE ?", "%" + orderMain.getOrderNo() + "%");
            }
            if (StringUtils.isNotBlank(orderMain.getShipperId())) {
                queryWrapper.and("SHIPPER_ID = ?", orderMain.getShipperId());
            }
            if (orderMain.getOrderDate() != null) {
                queryWrapper.and("ORDER_DATE = ?", orderMain.getOrderDate());
            }
        }
        
        queryWrapper.orderBy("CREATE_TIME DESC");
        Page<OrderMain> result = this.getMapper().paginate(pageNumber, pageSize, queryWrapper);
        log.debug("分页查询订单主表完成 - 总记录数: {}", result.getTotalRow());
        return result;
    }
    
    @Override
    public OrderMainMapper getMapper() {
        return super.getMapper();
    }
}