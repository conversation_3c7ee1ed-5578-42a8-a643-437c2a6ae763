package com.ruoyi.plan.service;

import com.mybatisflex.core.service.IService;
import com.mybatisflex.core.paginate.Page;
import com.ruoyi.plan.domain.VoyageMain;
import com.ruoyi.plan.domain.VoyageMainVO;

import java.util.List;

public interface IVoyageMainService extends IService<VoyageMain> {
    
    /**
     * 分页查询航次主表
     * 
     * @param pageNumber  页码
     * @param pageSize    每页大小
     * @param voyageMain  查询条件
     * @return 分页结果
     */
    Page<VoyageMain> selectVoyageMainPage(int pageNumber, int pageSize, VoyageMain voyageMain);
    
    /**
     * 分页查询航次主表（包含船名）
     * 
     * @param pageNumber  页码
     * @param pageSize    每页大小
     * @param voyageMain  查询条件
     * @return 分页结果
     */
    Page<VoyageMainVO> selectVoyageMainVOPage(int pageNumber, int pageSize, VoyageMain voyageMain);
    
    /**
     * 根据ID查询航次
     * 
     * @param id 航次ID
     * @return 航次信息
     */
    VoyageMain selectVoyageMainById(String id);
    
    /**
     * 查询航次列表
     * 
     * @param voyageMain 查询条件
     * @return 航次列表
     */
    List<VoyageMain> selectVoyageMainList(VoyageMain voyageMain);
    
    /**
     * 查询航次列表（包含船名）
     * 
     * @param voyageMain 查询条件
     * @return 航次列表
     */
    List<VoyageMainVO> selectVoyageMainVOList(VoyageMain voyageMain);
    
    /**
     * 新增航次
     * 
     * @param voyageMain 航次信息
     * @return 结果
     */
    int insertVoyageMain(VoyageMain voyageMain);
    
    /**
     * 修改航次
     * 
     * @param voyageMain 航次信息
     * @return 结果
     */
    int updateVoyageMain(VoyageMain voyageMain);
    
    /**
     * 批量删除航次
     * 
     * @param ids 需要删除的航次ID数组
     * @return 结果
     */
    int deleteVoyageMainByIds(String[] ids);
    
    /**
     * 删除航次信息
     * 
     * @param id 航次ID
     * @return 结果
     */
    int deleteVoyageMainById(String id);
    
    /**
     * 生成航次号
     * 格式：YYMMDD序号，序号为当天航次数量+1
     * 
     * @return 生成的航次号
     */
    String generateVoyageNo();
} 