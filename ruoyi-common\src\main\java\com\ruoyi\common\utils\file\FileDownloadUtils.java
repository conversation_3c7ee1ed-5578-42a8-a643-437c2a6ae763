package com.ruoyi.common.utils.file;

import io.minio.errors.*;

import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public class FileDownloadUtils {

    /**
     * Minio默认上传的地址
     */
    private static String bucketName = MinioConfig.getBucketName();

    public static String getBucketName()
    {
        return bucketName;
    }

    public static final InputStream getMinio(String filePath) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return MinioUtil.getFile(getBucketName(),filePath);
    }

    public static final InputStream getMinio(String bucketName,String filePath) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return MinioUtil.getFile(bucketName,filePath);
    }


}
