package com.ruoyi.contract.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.contract.domain.ContractFlow;
import com.ruoyi.contract.service.IContractFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同流向管理
 */
@RestController
@RequestMapping("/contract/flow")
public class ContractFlowController {
    
    @Autowired
    private IContractFlowService contractFlowService;
    
    /**
     * 保存合同流向
     */
    @PostMapping("/save")
    public AjaxResult save(@RequestBody List<ContractFlow> flows) {
        return contractFlowService.saveFlows(flows);
    }
    
    /**
     * 获取合同流向列表
     */
    @GetMapping("/list")
    public AjaxResult list(@RequestParam String contractId) {
        return contractFlowService.getFlowsByContractId(contractId);
    }
    
    /**
     * 删除合同流向
     */
    @DeleteMapping("/{flowId}")
    public AjaxResult delete(@PathVariable String flowId) {
        return contractFlowService.deleteFlow(flowId);
    }
} 