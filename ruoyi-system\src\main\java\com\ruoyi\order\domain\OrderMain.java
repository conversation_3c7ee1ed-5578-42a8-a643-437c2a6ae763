package com.ruoyi.order.domain;

import com.mybatisflex.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.core.domain.BaseEntity;
import java.util.Date;
import java.math.BigDecimal;
import com.mybatisflex.core.keygen.KeyGenerators;

/**
 * 委托主表（三层架构第一层）
 * 客户委托的顶层抽象，一个委托可包含多个不同业务类型的订舱
 */
@Data
@Table("ORDER_MAIN")
@EqualsAndHashCode(callSuper = false)
public class OrderMain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键，雪花ID全局唯一标识符 */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /** 委托编号，格式：WT+日期+6位序号，如WT202507220000001 */
    @Column("ORDER_NO")
    private String orderNo;

    /** 托运单位ID，关联客户基础资料表 */
    @Column("SHIPPER_ID")
    private String shipperId;

    /** 托运单位名称（冗余存储便于查询） */
    @Column("SHIPPER_NAME")
    private String shipperName;

    /** 委托创建日期，业务发生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column("ORDER_DATE")
    private Date orderDate;

    /** 业务类型：EXPORT（出口）/IMPORT（进口）/DOMESTIC（内贸） */
    @Column("BUSINESS_TYPE")
    private String businessType;

    /** 贸易类型：DOMESTIC（内贸）/FOREIGN（外贸） */
    @Column("TRADE_TYPE")
    private String tradeType;

    /** 所属共同体：中山共同体、黄埔共同体（支持联合运营业务） */
    @Column("CONSORTIUM")
    private String consortium;

    /** 客户协议号，运输合同编号 */
    @Column("CUSTOMER_AGREEMENT")
    private String customerAgreement;

    /** 结算方式：MONTHLY（月结）/PREPAID（预付）/COD（货到付款） */
    @Column("SETTLEMENT_METHOD")
    private String settlementMethod;

    /** 总箱量，所有订舱的箱量汇总（统计字段） */
    @Column("TOTAL_CONTAINERS")
    private Integer totalContainers;

    /** 总重量（吨），所有订舱的重量汇总 */
    @Column("TOTAL_WEIGHT")
    private BigDecimal totalWeight;

    /** 委托状态：DRAFT（草稿）/SUBMITTED（已提交）/CONFIRMED（已确认）/COMPLETED（已完成）/CANCELLED（已取消） */
    @Column("STATUS")
    private String status;

    /** 备注信息，客户特殊要求或注意事项 */
    @Column("REMARK")
    private String remark;

    /** 逻辑删除标志：0正常 2删除 */
    @Column(value = "DEL_FLAG", isLogicDelete = true)
    private String delFlag;

    /** 乐观锁版本号，防止并发修改 */
    @Column(value = "VERSION", version = true)
    private Integer version;
}