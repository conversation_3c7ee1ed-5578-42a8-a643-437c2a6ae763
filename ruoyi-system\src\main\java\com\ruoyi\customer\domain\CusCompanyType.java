package com.ruoyi.customer.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

/**
 * 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@Table(value = "CUS_COMPANY_TYPE")
public class CusCompanyType {

    /**
     * 客户主表id
     */
    @Id
    private String cusMainId;

    /**
     * 客户企业类型
     */
    @Id
    private String companyType;


}
