# 箱子管理模块开发总结

## 概述
根据用户提供的表单设计，为自有箱和租赁箱分别创建了完整的前后端代码，包括数据库建表语句、后端实体类、Service、Controller以及前端页面和API。

## 数据库设计

### 1. 自有箱表 (BASIC_CNTR_MAIN_OWN)
- 表名：`BASIC_CNTR_MAIN_OWN`
- 主要字段：箱号、尺寸、箱型、空箱重量、最大总重量、箱属、采购日期、采购金额、制造商、生产日期、默认货类等
- 特色字段：采购相关信息（采购日期、采购金额、制造商、生产日期）

### 2. 租赁箱表 (BASIC_CNTR_MAIN_LEASE)
- 表名：`BASIC_CNTR_MAIN_LEASE`
- 主要字段：箱号、尺寸、箱型、空箱重量、最大总重量、箱属、起租时间、租赁期、起租地点、还箱地点、默认货类等
- 特色字段：租赁相关信息（起租时间、租赁期、起租地点、还箱地点）

## 后端代码结构

### 实体类 (Domain)
- `BasicCntrMainOwn.java` - 自有箱实体类
- `BasicCntrMainLease.java` - 租赁箱实体类

### 数据访问层 (Mapper)
- `BasicCntrMainOwnMapper.java` - 自有箱Mapper接口
- `BasicCntrMainLeaseMapper.java` - 租赁箱Mapper接口

### 业务逻辑层 (Service)
- `IBasicCntrMainOwnService.java` - 自有箱Service接口
- `BasicCntrMainOwnServiceImpl.java` - 自有箱Service实现
- `IBasicCntrMainLeaseService.java` - 租赁箱Service接口
- `BasicCntrMainLeaseServiceImpl.java` - 租赁箱Service实现

### 控制器层 (Controller)
- `BasicCntrMainOwnController.java` - 自有箱控制器
- `BasicCntrMainLeaseController.java` - 租赁箱控制器

## 前端代码结构

### API接口
- `src/api/basic/cntrMainOwn.js` - 自有箱API接口
- `src/api/basic/cntrMainLease.js` - 租赁箱API接口

### 页面组件
- `src/views/basic/cntrMainOwn/index.vue` - 自有箱管理页面
- `src/views/basic/cntrMainLease/index.vue` - 租赁箱管理页面

## 功能特性

### 1. 基础CRUD操作
- ✅ 新增箱子信息
- ✅ 分页查询箱子列表
- ✅ 修改箱子信息
- ✅ 删除箱子信息
- ✅ 根据ID查询箱子详情

### 2. 查询功能
- ✅ 支持按箱号模糊查询
- ✅ 支持按尺寸模糊查询
- ✅ 支持按箱型模糊查询
- ✅ 自有箱支持按制造商查询
- ✅ 租赁箱支持按起租地点查询

### 3. 数据验证
- ✅ 箱号唯一性验证
- ✅ 必填字段验证
- ✅ 数据格式验证

### 4. 前端特性
- ✅ 响应式表格设计
- ✅ 分页显示
- ✅ 搜索过滤
- ✅ 批量操作
- ✅ 表单验证
- ✅ 日期选择器
- ✅ 数字输入框

## 技术栈

### 后端
- Spring Boot 3.3.5
- MyBatis-Flex 1.10.4
- Oracle数据库
- 雪花ID生成器
- 乐观锁版本控制

### 前端
- Vue 2.6.12
- Element UI 2.15.14
- Axios HTTP客户端

## 文件位置总览

```
后端文件：
ruoyi-system/src/main/java/com/ruoyi/basic/
├── domain/
│   ├── BasicCntrMainOwn.java
│   └── BasicCntrMainLease.java
├── mapper/
│   ├── BasicCntrMainOwnMapper.java
│   └── BasicCntrMainLeaseMapper.java
├── service/
│   ├── IBasicCntrMainOwnService.java
│   ├── BasicCntrMainOwnServiceImpl.java
│   ├── IBasicCntrMainLeaseService.java
│   └── BasicCntrMainLeaseServiceImpl.java
└── controller/
    ├── BasicCntrMainOwnController.java
    └── BasicCntrMainLeaseController.java

前端文件：
src/
├── api/basic/
│   ├── cntrMainOwn.js
│   └── cntrMainLease.js
└── views/basic/
    ├── cntrMainOwn/
    │   └── index.vue
    └── cntrMainLease/
        └── index.vue
```

## API接口说明

### 自有箱接口
- `POST /system/cntrMainOwn/add` - 新增自有箱
- `GET /system/cntrMainOwn/list` - 分页查询自有箱列表
- `POST /system/cntrMainOwn/label` - 标签查询（下拉选择）
- `GET /system/cntrMainOwn/getById/{id}` - 根据ID查询
- `PUT /system/cntrMainOwn/edit` - 修改自有箱
- `DELETE /system/cntrMainOwn/deleteByIds` - 批量删除

### 租赁箱接口
- `POST /system/cntrMainLease/add` - 新增租赁箱
- `GET /system/cntrMainLease/list` - 分页查询租赁箱列表
- `POST /system/cntrMainLease/label` - 标签查询（下拉选择）
- `GET /system/cntrMainLease/getById/{id}` - 根据ID查询
- `PUT /system/cntrMainLease/edit` - 修改租赁箱
- `DELETE /system/cntrMainLease/deleteByIds` - 批量删除

## 注意事项

1. **数据库表需要先创建**：请先执行提供的Oracle建表语句
2. **权限配置**：需要在系统菜单中配置相应的权限
3. **路由配置**：需要在前端路由中添加页面路由
4. **TableDef生成**：如果使用MyBatis-Flex的APT功能，TableDef会自动生成
5. **样式调整**：可根据实际需求调整前端页面样式

## 扩展建议

1. **导入导出功能**：可添加Excel导入导出功能
2. **附件管理**：完善附件上传和管理功能
3. **状态管理**：添加箱子状态字段（在用、空闲、维修等）
4. **历史记录**：添加箱子使用历史记录
5. **统计报表**：添加箱子使用情况统计报表
