package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicDangerUnNumber;
import com.ruoyi.basic.mapper.BasicDangerUnNumberMapper;
import com.ruoyi.basic.service.IBasicDangerUnNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicDangerUnNumberServiceImpl extends ServiceImpl<BasicDangerUnNumberMapper, BasicDangerUnNumber> implements IBasicDangerUnNumberService {
    
    @Autowired
    private BasicDangerUnNumberMapper basicDangerUnNumberMapper;

    @Override
    public BasicDangerUnNumberMapper getMapper() {
        return basicDangerUnNumberMapper;
    }

    @Override
    public int deleteByDangerId(String dangerId) {
        return basicDangerUnNumberMapper.deleteByQuery(QueryWrapper.create().eq(BasicDangerUnNumber::getDangerId,dangerId));
    }

    @Override
    public int insertBatch(List<BasicDangerUnNumber> list) {
        return basicDangerUnNumberMapper.insertBatch(list);
    }
}
