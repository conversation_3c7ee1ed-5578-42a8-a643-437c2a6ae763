package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCntrMainLease;
import com.ruoyi.basic.service.IBasicCntrMainLeaseService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;



/**
 * 租赁箱基础信息控制器
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/system/cntrMainLease")
public class BasicCntrMainLeaseController extends BaseController {

    @Autowired
    private IBasicCntrMainLeaseService basicCntrMainLeaseService;

    /**
     * 新增租赁箱信息
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BasicCntrMainLease basicCntrMainLease) {
        return basicCntrMainLeaseService.addBasicCntrMainLease(basicCntrMainLease);
    }

    /**
     * 分页查询租赁箱列表
     */
    @GetMapping("/list")
    public Page<BasicCntrMainLease> list(BasicCntrMainLease basicCntrMainLease) {
        QueryWrapper queryWrapper = QueryWrapper.create();

        if (StringUtils.isNotEmpty(basicCntrMainLease.getCntrNo())) {
            queryWrapper.like("cntr_no", basicCntrMainLease.getCntrNo());
        }
        if (StringUtils.isNotEmpty(basicCntrMainLease.getCntrSize())) {
            queryWrapper.and("cntr_size like ?", "%" + basicCntrMainLease.getCntrSize() + "%");
        }
        if (StringUtils.isNotEmpty(basicCntrMainLease.getCntrType())) {
            queryWrapper.and("cntr_type like ?", "%" + basicCntrMainLease.getCntrType() + "%");
        }
        if (StringUtils.isNotEmpty(basicCntrMainLease.getLeaseStartLocation())) {
            queryWrapper.and("lease_start_location like ?", "%" + basicCntrMainLease.getLeaseStartLocation() + "%");
        }
        queryWrapper.orderBy("create_time desc");

        var pageDomain = TableSupport.buildPageRequest();
        return basicCntrMainLeaseService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 标签查询（用于下拉选择等）
     */
    @PostMapping("/label")
    public List<BasicCntrMainLease> label(@RequestBody BasicCntrMainLease basicCntrMainLease) {
        QueryWrapper queryWrapper = QueryWrapper.create();

        if (StringUtils.isNotEmpty(basicCntrMainLease.getCntrNo())) {
            queryWrapper.like("cntr_no", basicCntrMainLease.getCntrNo());
        }
        if (StringUtils.isNotEmpty(basicCntrMainLease.getCntrSize())) {
            queryWrapper.and("cntr_size like ?", "%" + basicCntrMainLease.getCntrSize() + "%");
        }
        if (StringUtils.isNotEmpty(basicCntrMainLease.getCntrType())) {
            queryWrapper.and("cntr_type like ?", "%" + basicCntrMainLease.getCntrType() + "%");
        }
        queryWrapper.orderBy("create_time desc");
        return basicCntrMainLeaseService.list(queryWrapper);
    }

    /**
     * 根据ID查询租赁箱信息
     */
    @GetMapping("/getById/{id}")
    public AjaxResult getById(@PathVariable("id") String id) {
        BasicCntrMainLease basicCntrMainLease = basicCntrMainLeaseService.getById(id);
        return AjaxResult.success(basicCntrMainLease);
    }

    /**
     * 修改租赁箱信息
     */
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody BasicCntrMainLease basicCntrMainLease) {
        return basicCntrMainLeaseService.editBasicCntrMainLease(basicCntrMainLease);
    }

    /**
     * 删除租赁箱信息
     */
    @DeleteMapping("/deleteByIds")
    public AjaxResult remove(@RequestBody List<String> ids) {
        return AjaxResult.success(basicCntrMainLeaseService.removeByIds(ids));
    }
}
