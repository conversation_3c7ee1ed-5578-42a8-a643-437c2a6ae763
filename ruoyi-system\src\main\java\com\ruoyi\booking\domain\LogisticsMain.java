package com.ruoyi.booking.domain;

import com.mybatisflex.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.core.domain.BaseEntity;
import java.util.Date;
import com.mybatisflex.core.keygen.KeyGenerators;

/**
 * 物流主表（三层架构第三层）
 * 物流过程的公共信息，严格按项目经理运输环节表字段设计
 */
@Data
@Table("LOGISTICS_MAIN")
@EqualsAndHashCode(callSuper = false)
public class LogisticsMain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键，雪花ID全局唯一标识符 */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /** 关联订舱主表ID，建立订舱与物流的关系 */
    @Column("BOOKING_ID")
    private String bookingId;

    /** 运输模式: WATERWAY(水路)/ROADWAY(陆路)/AIRWAY(航空)，决定了关联哪个子表 */
    @Column("BUSINESS_TYPE")
    private String businessType;

    /** 供应商ID，关联船务供应商基础资料表 */
    @Column("SUPPLIER_ID")
    private String supplierId;

    /** 供应商名称（冗余存储便于查询） */
    @Column("SUPPLIER_NAME")
    private String supplierName;

    /** 启运地ID（使用terminal Options，支持用户自创option时为空） */
    @Column("ORIGIN_LOCATION_ID")
    private String originLocationId;

    /** 启运地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询） */
    @Column("ORIGIN_LOCATION_NAME")
    private String originLocationName;

    /** 目的地ID（使用terminal Options，支持用户自创option时为空） */
    @Column("DESTINATION_LOCATION_ID")
    private String destinationLocationId;

    /** 目的地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询） */
    @Column("DESTINATION_LOCATION_NAME")
    private String destinationLocationName;


    /** 条款，运输合同条款或特殊约定 */
    @Column("TERMS_CONDITIONS")
    private String termsConditions;

    /** 要求启运日期，客户要求的发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column("REQUIRED_DEPARTURE_DATE")
    private Date requiredDepartureDate;

    /** 要求到达日期，客户要求的到货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column("REQUIRED_ARRIVAL_DATE")
    private Date requiredArrivalDate;

    /** 逻辑删除标志：0正常 2删除 */
    @Column(value = "DEL_FLAG", isLogicDelete = true)
    private String delFlag;

    /** 乐观锁版本号，防止并发修改 */
    @Column(value = "VERSION", version = true)
    private Integer version;
}