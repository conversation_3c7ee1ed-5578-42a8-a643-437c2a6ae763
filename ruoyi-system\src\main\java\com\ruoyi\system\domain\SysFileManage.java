package com.ruoyi.system.domain;

import com.mybatisflex.annotation.*;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("sys_file_manage")

public class SysFileManage extends BaseEntity {

    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    String fileId;

    String fileName;

    String filePath;

    String fileTypeId;

    String fileBusinessId;

//    @TableLogic
    String delFlag;

    @Column(ignore = true)
    String fileType;

    @Column(ignore = true)
    String fileBusinessType;

}
