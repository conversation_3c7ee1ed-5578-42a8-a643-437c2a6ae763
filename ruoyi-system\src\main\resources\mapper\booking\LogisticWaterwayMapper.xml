<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.booking.mapper.LogisticWaterwayMapper">

    <!-- 通用查询结果映射 -->
    <resultMap id="LogisticWaterwayResult" type="com.ruoyi.booking.domain.LogisticWaterway">
        <id property="id" column="ID"/>
        <result property="logisticsMainId" column="LOGISTICS_MAIN_ID"/>
        <result property="routeId" column="ROUTE_ID"/>
        <result property="routeName" column="ROUTE_NAME"/>
        <result property="vesselId" column="VESSEL_ID"/>
        <result property="vesselName" column="VESSEL_NAME"/>
        <result property="voyageNo" column="VOYAGE_NO"/>
        <result property="loadingTerminalId" column="LOADING_TERMINAL_ID"/>
        <result property="loadingTerminalName" column="LOADING_TERMINAL_NAME"/>
        <result property="unloadingTerminalId" column="UNLOADING_TERMINAL_ID"/>
        <result property="unloadingTerminalName" column="UNLOADING_TERMINAL_NAME"/>
        <result property="etd" column="ETD"/>
        <result property="eta" column="ETA"/>
        <result property="atd" column="ATD"/>
        <result property="ata" column="ATA"/>
        <result property="sequenceNo" column="SEQUENCE_NO"/>
        <result property="containerAllocation" column="CONTAINER_ALLOCATION"/>
        <result property="status" column="STATUS"/>
        <result property="splitBy" column="SPLIT_BY"/>
        <result property="splitTime" column="SPLIT_TIME"/>
        <result property="stowagePlanId" column="STOWAGE_PLAN_ID"/>
        <result property="remark" column="REMARK"/>
        <result property="createBy" column="CREATE_BY"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateBy" column="UPDATE_BY"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="version" column="VERSION"/>
    </resultMap>

    <!-- 查询所有字段的列名 -->
    <sql id="selectLogisticWaterwayVo">
        SELECT ID,
        LOGISTICS_MAIN_ID,
        ROUTE_ID,
        ROUTE_NAME,
        VESSEL_ID,
        VESSEL_NAME,
        VOYAGE_NO,
        LOADING_TERMINAL_ID,
        LOADING_TERMINAL_NAME,
        UNLOADING_TERMINAL_ID,
        UNLOADING_TERMINAL_NAME,
        ETD,
        ETA,
        ATD,
        ATA,
        SEQUENCE_NO,
        CONTAINER_ALLOCATION,
        STATUS,
        SPLIT_BY,
        SPLIT_TIME,
        STOWAGE_PLAN_ID,
        REMARK,
        CREATE_BY,
        CREATE_TIME,
        UPDATE_BY,
        UPDATE_TIME,
        DEL_FLAG,
        VERSION
        FROM LOGISTIC_WATERWAY
    </sql>

    <!-- 查询所有记录 -->
    <select id="selectLogisticWaterwayList" parameterType="com.ruoyi.booking.domain.LogisticWaterway"
            resultMap="LogisticWaterwayResult">
        <include refid="selectLogisticWaterwayVo"/>
        <where>
            <if test="logisticsMainId != null and logisticsMainId != ''">AND LOGISTICS_MAIN_ID = #{logisticsMainId}</if>
            <if test="status != null and status != ''">AND STATUS = #{status}</if>
            <if test="delFlag != null and delFlag != ''">AND DEL_FLAG = #{delFlag}</if>
        </where>
    </select>

    <!-- 根据主键查询记录 -->
    <select id="selectLogisticWaterwayById" parameterType="string"
            resultMap="LogisticWaterwayResult">
        <include refid="selectLogisticWaterwayVo"/>
        WHERE ID = #{id}
    </select>

    <!-- 插入记录 -->
    <insert id="insertLogisticWaterway" parameterType="com.ruoyi.booking.domain.LogisticWaterway">
        INSERT INTO LOGISTIC_WATERWAY (
        <if test="id != null and id != ''">ID,</if>
        <if test="logisticsMainId != null and logisticsMainId != ''">LOGISTICS_MAIN_ID,</if>
        <if test="routeId != null and routeId != ''">ROUTE_ID,</if>
        <if test="routeName != null and routeName != ''">ROUTE_NAME,</if>
        <if test="vesselId != null and vesselId != ''">VESSEL_ID,</if>
        <if test="vesselName != null and vesselName != ''">VESSEL_NAME,</if>
        <if test="voyageNo != null and voyageNo != ''">VOYAGE_NO,</if>
        <if test="loadingTerminalId != null and loadingTerminalId != ''">LOADING_TERMINAL_ID,</if>
        <if test="loadingTerminalName != null and loadingTerminalName != ''">LOADING_TERMINAL_NAME,</if>
        <if test="unloadingTerminalId != null and unloadingTerminalId != ''">UNLOADING_TERMINAL_ID,</if>
        <if test="unloadingTerminalName != null and unloadingTerminalName != ''">UNLOADING_TERMINAL_NAME,</if>
        <if test="etd != null">ETD,</if>
        <if test="eta != null">ETA,</if>
        <if test="atd != null">ATD,</if>
        <if test="ata != null">ATA,</if>
        <if test="sequenceNo != null">SEQUENCE_NO,</if>
        <if test="containerAllocation != null and containerAllocation != ''">CONTAINER_ALLOCATION,</if>
        <if test="status != null and status != ''">STATUS,</if>
        <if test="splitBy != null and splitBy != ''">SPLIT_BY,</if>
        <if test="splitTime != null">SPLIT_TIME,</if>
        <if test="stowagePlanId != null and stowagePlanId != ''">STOWAGE_PLAN_ID,</if>
        <if test="remark != null and remark != ''">REMARK,</if>
        <if test="createBy != null and createBy != ''">CREATE_BY,</if>
        <if test="updateBy != null and updateBy != ''">UPDATE_BY,</if>
        <if test="updateTime != null">UPDATE_TIME,</if>
        DEL_FLAG,
        VERSION
        ) VALUES (
        <if test="id != null and id != ''">#{id},</if>
        <if test="logisticsMainId != null and logisticsMainId != ''">#{logisticsMainId},</if>
        <if test="routeId != null and routeId != ''">#{routeId},</if>
        <if test="routeName != null and routeName != ''">#{routeName},</if>
        <if test="vesselId != null and vesselId != ''">#{vesselId},</if>
        <if test="vesselName != null and vesselName != ''">#{vesselName},</if>
        <if test="voyageNo != null and voyageNo != ''">#{voyageNo},</if>
        <if test="loadingTerminalId != null and loadingTerminalId != ''">#{loadingTerminalId},</if>
        <if test="loadingTerminalName != null and loadingTerminalName != ''">#{loadingTerminalName},</if>
        <if test="unloadingTerminalId != null and unloadingTerminalId != ''">#{unloadingTerminalId},</if>
        <if test="unloadingTerminalName != null and unloadingTerminalName != ''">#{unloadingTerminalName},</if>
        <if test="etd != null">#{etd, jdbcType=TIMESTAMP},</if>
        <if test="eta != null">#{eta, jdbcType=TIMESTAMP},</if>
        <if test="atd != null">#{atd, jdbcType=TIMESTAMP},</if>
        <if test="ata != null">#{ata, jdbcType=TIMESTAMP},</if>
        <if test="sequenceNo != null">#{sequenceNo},</if>
        <if test="containerAllocation != null and containerAllocation != ''">#{containerAllocation},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="splitBy != null and splitBy != ''">#{splitBy},</if>
        <if test="splitTime != null">#{splitTime, jdbcType=TIMESTAMP},</if>
        <if test="stowagePlanId != null and stowagePlanId != ''">#{stowagePlanId},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        <if test="updateTime != null">#{updateTime, jdbcType=TIMESTAMP},</if>
        '0',
        1
        )
    </insert>

    <!-- 查询指定条件的SEQUENCE_NO最大值 -->
    <select id="selectMaxSequenceNo" parameterType="com.ruoyi.booking.domain.LogisticWaterway" resultType="int">
        SELECT MAX(SEQUENCE_NO) AS MAX_SEQUENCE_NO
        FROM LOGISTIC_WATERWAY
        WHERE LOGISTICS_MAIN_ID = #{logisticsMainId}
    </select>

    <!-- 查询指定条件的数据数量 -->
    <select id="selectCount" parameterType="com.ruoyi.booking.domain.LogisticWaterway" resultType="int">
        SELECT MAX(SEQUENCE_NO) AS MAX_SEQUENCE_NO
        FROM LOGISTIC_WATERWAY
        WHERE LOGISTICS_MAIN_ID = #{logisticsMainId}
    </select>

    <!-- 更新记录 -->
    <update id="updateLogisticWaterway" parameterType="com.ruoyi.booking.domain.LogisticWaterway">
        UPDATE LOGISTIC_WATERWAY
        <set>
            <if test="logisticsMainId != null">LOGISTICS_MAIN_ID = #{logisticsMainId},</if>
            <if test="routeId != null">ROUTE_ID = #{routeId},</if>
            <if test="routeName != null">ROUTE_NAME = #{routeName},</if>
            <if test="vesselId != null">VESSEL_ID = #{vesselId},</if>
            <if test="vesselName != null">VESSEL_NAME = #{vesselName},</if>
            <if test="voyageNo != null">VOYAGE_NO = #{voyageNo},</if>
            <if test="loadingTerminalId != null">LOADING_TERMINAL_ID = #{loadingTerminalId},</if>
            <if test="loadingTerminalName != null">LOADING_TERMINAL_NAME = #{loadingTerminalName},</if>
            <if test="unloadingTerminalId != null">UNLOADING_TERMINAL_ID = #{unloadingTerminalId},</if>
            <if test="unloadingTerminalName != null">UNLOADING_TERMINAL_NAME = #{unloadingTerminalName},</if>
            <if test="etd != null">ETD = #{etd},</if>
            <if test="eta != null">ETA = #{eta},</if>
            <if test="atd != null">ATD = #{atd},</if>
            <if test="ata != null">ATA = #{ata},</if>
            <if test="sequenceNo != null">SEQUENCE_NO = #{sequenceNo},</if>
            <if test="containerAllocation != null">CONTAINER_ALLOCATION = #{containerAllocation},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="splitBy != null">SPLIT_BY = #{splitBy},</if>
            <if test="splitTime != null">SPLIT_TIME = #{splitTime},</if>
            <if test="stowagePlanId != null">STOWAGE_PLAN_ID = #{stowagePlanId},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="version != null">VERSION = #{version},</if>
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 删除记录(逻辑删除) -->
    <update id="deleteLogisticWaterwayById" parameterType="string">
        UPDATE LOGISTIC_WATERWAY
        SET DEL_FLAG = '1'
        WHERE ID = #{id}
    </update>

    <!-- 批量删除记录(逻辑删除) -->
    <update id="deleteLogisticWaterwayByIds" parameterType="string">
        UPDATE LOGISTIC_WATERWAY
        SET DEL_FLAG = '1'
        WHERE ID IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>