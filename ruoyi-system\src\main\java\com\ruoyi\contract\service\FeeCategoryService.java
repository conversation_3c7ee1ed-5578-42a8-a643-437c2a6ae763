package com.ruoyi.contract.service;


import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.contract.domain.FeeCategory;

/**
 * 费目分类 服务层。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
public interface FeeCategoryService extends IService<FeeCategory> {

    /**
     * 分页查询费目分类
     *
     * @param feeCategory 查询条件
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页对象
     */
    Page<FeeCategory> selectPage(FeeCategory feeCategory, Integer pageNum, Integer pageSize);
}