package com.ruoyi.web.controller.system;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysFileType;
import com.ruoyi.system.service.ISysFileTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sysFileType")
public class SysFileTypeController extends BaseController {

    @Autowired
    ISysFileTypeService sysFileTypeService;

    @GetMapping("/page")
    public Page<SysFileType> page(SysFileType sysFileType, Integer pageNum, Integer pageSize){

        return sysFileTypeService.page(new Page<>(pageNum,pageSize), QueryWrapper.create()
                .like("file_type", sysFileType.getFileType(), StringUtils.isNotEmpty(sysFileType.getFileType()))
                .like("file_business_type", sysFileType.getFileBusinessType(), StringUtils.isNotEmpty(sysFileType.getFileBusinessType()))
                .orderBy("file_type", true)
                .orderBy("file_sort", true)
                .orderBy("file_type_id", false)
        );

    }

    @GetMapping("/getOne/{fileTypeId}")
    public AjaxResult getOne(@PathVariable Long fileTypeId){
        return AjaxResult.success(sysFileTypeService.getById(fileTypeId));
    }

    @PostMapping
    @PreAuthorize("@ss.hasPermi('system:fileType:add')")
    public AjaxResult save(@RequestBody @Validated SysFileType sysFileType){

        try {
            return toAjax(sysFileTypeService.insert(sysFileType));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

    }

    @PutMapping
    @PreAuthorize("@ss.hasPermi('system:fileType:edit')")
    public AjaxResult update(@RequestBody @Validated SysFileType sysFileType){
        try {
            return toAjax(sysFileTypeService.update(sysFileType));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @DeleteMapping
    @PreAuthorize("@ss.hasPermi('system:fileType:remove')")
    public AjaxResult delete(@RequestBody List<Long> ids){

        try {
            return  toAjax(sysFileTypeService.remove(ids));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

    }

}
