package com.ruoyi.booking.domain;

import com.mybatisflex.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.core.domain.BaseEntity;
import java.util.Date;
import com.mybatisflex.core.keygen.KeyGenerators;

/**
 * 订舱主表（三层架构第二层）
 * 原为穿巴订舱(BOOKING_MAIN_CHUANBA)，现作为统一订舱模型，支持策划员拆单操作
 */
@Data
@Table("BOOKING_MAIN")
@EqualsAndHashCode(callSuper = false)
public class BookingMain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键，雪花ID全局唯一标识符 */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /** 关联委托主表ID，建立委托与订舱的关系 */
    @Column("ORDER_ID")
    private String orderId;

    /** 订舱号，格式：BK+日期+6位序号，如BK202507220000001 */
    @Column("BOOKING_NUMBER")
    private String bookingNumber;

    /** 订舱状态：DRAFT（草稿）/SUBMITTED（已提交）/CONFIRMED（已确认）/REJECTED（已拒绝）/CANCELLED（已取消） */
    @Column("STATUS")
    private String status;

    /** 托运单位ID，关联客户基础资料表 */
    @Column("SHIPPER_ID")
    private String shipperId;

    /** 托运单位名称（冗余存储便于查询） */
    @Column("SHIPPER_NAME")
    private String shipperName;

    /** 起运地ID（使用terminal Options，支持用户自创option时为空） */
    @Column("ORIGIN_LOCATION_ID")
    private String originLocationId;

    /** 起运地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询） */
    @Column("ORIGIN_LOCATION_NAME")
    private String originLocationName;

    /** 目的地ID（使用terminal Options，支持用户自创option时为空） */
    @Column("DESTINATION_LOCATION_ID")
    private String destinationLocationId;

    /** 目的地名称（使用terminal Options或用户自定义地点名称，冗余存储便于查询） */
    @Column("DESTINATION_LOCATION_NAME")
    private String destinationLocationName;

    /** 订舱日期，业务申请日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column("BOOKING_DATE")
    private Date bookingDate;

    /** 启运日期，实际发货时间（会议要求去除要求二字） */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column("DEPARTURE_DATE")
    private Date departureDate;

    /** 交货日期，实际到货时间（会议要求去除要求二字） */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column("DELIVERY_DATE")
    private Date deliveryDate;

    /** 装货码头ID，关联码头基础资料表 */
    @Column("LOADING_TERMINAL_ID")
    private String loadingTerminalId;

    /** 装货码头名称（支持自动填充逻辑） */
    @Column("LOADING_TERMINAL_NAME")
    private String loadingTerminalName;

    /** 卸货码头ID，关联码头基础资料表 */
    @Column("UNLOADING_TERMINAL_ID")
    private String unloadingTerminalId;

    /** 卸货码头名称（支持自动填充逻辑） */
    @Column("UNLOADING_TERMINAL_NAME")
    private String unloadingTerminalName;

    /** 装货代理ID，关联代理公司基础资料表 */
    @Column("LOADING_AGENT_ID")
    private String loadingAgentId;

    /** 装货代理名称（冗余存储便于查询） */
    @Column("LOADING_AGENT_NAME")
    private String loadingAgentName;

    /** 卸货代理ID，关联代理公司基础资料表 */
    @Column("UNLOADING_AGENT_ID")
    private String unloadingAgentId;

    /** 卸货代理名称（冗余存储便于查询） */
    @Column("UNLOADING_AGENT_NAME")
    private String unloadingAgentName;

    /** 贸易类型：DOMESTIC（内贸）/FOREIGN（外贸） */
    @Column("TRADE_TYPE")
    private String tradeType;

    /** 运输模式：FCL（整箱）/LCL（拼箱） */
    @Column("TRANSPORT_MODE")
    private String transportMode;

    /** 报关类型：GENERAL（一般贸易）/PROCESSING（加工贸易） */
    @Column("CUSTOMS_TYPE")
    private String customsType;

    /** 结算方式：MONTHLY（月结）/PREPAID（预付）/COD（货到付款） */
    @Column("SETTLEMENT_METHOD")
    private String settlementMethod;

    /** 所属共同体，穿巴业务固定为穿巴 */
    @Column("CONSORTIUM")
    private String consortium;

    /** 客户协议编号，运输合同编号 */
    @Column("CUSTOMER_AGREEMENT")
    private String customerAgreement;

    /** 委托来源：ONLINE（网上申请）/PHONE（电话委托）/ONSITE（现场委托）/EMAIL（邮件委托）/STAFF_ENTRY（单证员代替客户录入） */
    @Column("CONSIGNMENT_SOURCE")
    private String consignmentSource;

    /** 备注信息，特殊要求或注意事项 */
    @Column("REMARK")
    private String remark;

    /** 逻辑删除标志：0正常 2删除 */
    @Column(value = "DEL_FLAG", isLogicDelete = true)
    private String delFlag;

    /** 乐观锁版本号，防止并发修改 */
    @Column(value = "VERSION", version = true)
    private Integer version;
}