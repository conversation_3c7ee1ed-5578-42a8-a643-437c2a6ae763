-- ============================================
-- 创建订单号和订舱号生成序列
-- 解决唯一约束冲突问题
-- 创建时间: 2025-07-31
-- ============================================

-- 先删除可能存在的序列，避免ORA-00955错误
BEGIN EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_ORDER_NO'; EXCEPTION WHEN OTHERS THEN NULL; END;
/
BEGIN EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_BOOKING_NO'; EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- 创建订单号全局序列
-- 用于生成 ORDER_MAIN 表的 ORDER_NO 字段
CREATE SEQUENCE SEQ_ORDER_NO
    START WITH 1
    INCREMENT BY 1
    CACHE 50 
    NOCYCLE 
    NOORDER;

-- 创建订舱号全局序列  
-- 用于生成 BOOKING_MAIN 表的 BOOKING_NUMBER 字段
CREATE SEQUENCE SEQ_BOOKING_NO
    START WITH 1
    INCREMENT BY 1
    CACHE 50 
    NOCYCLE 
    NOORDER;

-- 验证序列创建成功
SELECT SEQUENCE_NAME, MIN_VALUE, MAX_VALUE, INCREMENT_BY, CACHE_SIZE
FROM USER_SEQUENCES
WHERE SEQUENCE_NAME IN ('SEQ_ORDER_NO', 'SEQ_BOOKING_NO');

-- 测试序列功能
SELECT 'WT' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(SEQ_ORDER_NO.NEXTVAL, 6, '0') AS ORDER_NO_SAMPLE FROM DUAL;
SELECT 'BK' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(SEQ_BOOKING_NO.NEXTVAL, 6, '0') AS BOOKING_NO_SAMPLE FROM DUAL;

COMMIT;