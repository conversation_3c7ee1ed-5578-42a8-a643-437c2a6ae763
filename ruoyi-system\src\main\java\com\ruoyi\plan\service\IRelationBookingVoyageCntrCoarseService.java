package com.ruoyi.plan.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.plan.domain.RelationBookingVoyageCntrCoarse;

public interface IRelationBookingVoyageCntrCoarseService extends IService<RelationBookingVoyageCntrCoarse> {
    RelationBookingVoyageCntrCoarse selectById(String id);
    int insert(RelationBookingVoyageCntrCoarse entity);
    int update(RelationBookingVoyageCntrCoarse entity);
    int deleteByIds(String[] ids);
    int deleteById(String id);
    
    /**
     * 根据bookingCntrCoarseId查询已配数量
     * 
     * @param bookingCntrCoarseId 粗略箱信息ID
     * @return 已配数量
     */
    Integer getSumQuantityByBookingCntrCoarseId(String bookingCntrCoarseId);
    
    /**
     * 检查订舱委托是否已完全配载
     * 
     * @param bookingId 订舱委托ID
     * @return true-已完全配载，false-未完全配载
     */
    Boolean isBookingFullyAllocated(String bookingId);
} 