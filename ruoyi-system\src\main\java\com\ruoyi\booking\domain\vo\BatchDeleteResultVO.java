package com.ruoyi.booking.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量删除订舱委托结果VO
 * 
 * 用于记录批量删除订舱委托操作的详细统计信息，包括：
 * 1. 主表（ORDER_MAIN、BOOKING_MAIN）的删除统计
 * 2. 子表（LOGISTICS_MAIN、LOGISTIC_WATERWAY、BOOKING_CNTR_NUM、BOOKING_TRANSIT_PORT）的删除统计
 * 3. 详细的删除清单和保留清单
 * 4. 可读性强的操作摘要信息
 * 
 * 表关系说明：
 * ORDER_MAIN (1) → (N) BOOKING_MAIN → (N) LOGISTICS_MAIN → (N) LOGISTIC_WATERWAY
 *                                   → (N) BOOKING_CNTR_NUM
 *                                   → (N) BOOKING_TRANSIT_PORT
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchDeleteResultVO {

    /** 删除的订舱记录总数 */
    private int totalBookingCount;

    /** 删除的订单记录总数 */
    private int deletedOrderCount;

    /** 保留的订单记录总数（仍有其他订舱关联） */
    private int remainingOrderCount;

    /** 删除的子表记录总数统计（包含4个相关子表的详细统计信息） */
    private ChildTableDeleteStatistics childTableStats;

    /** 删除的订舱号列表 */
    private List<String> deletedBookingNos;

    /** 删除的订单号列表 */
    private List<String> deletedOrderNos;

    /** 保留的订单号列表（仍有其他订舱关联） */
    private List<String> retainedOrderNos;

    /** 操作摘要信息 */
    private String operationSummary;

    /**
     * 子表删除统计信息内部类
     * 
     * 统计与订舱委托关联的所有子表的删除情况，包括：
     * 1. LOGISTICS_MAIN - 物流主表：存储物流基本信息
     * 2. LOGISTIC_WATERWAY - 水路运输表：存储具体的运输段信息（船舶、航线、港口等）
     * 3. BOOKING_CNTR_NUM - 箱量信息表：存储集装箱数量和类型信息
     * 4. BOOKING_TRANSIT_PORT - 中转港信息表：存储多段运输的中转港信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChildTableDeleteStatistics {
        /** 删除的物流主表(LOGISTICS_MAIN)记录数 - 存储订舱对应的物流业务基本信息 */
        private int logisticsMainCount;
        
        /** 删除的水路运输(LOGISTIC_WATERWAY)记录数 - 存储具体的船舶运输信息（航线、船舶、港口、时间等） */
        private int logisticWaterwayCount;
        
        /** 删除的箱量拆分明细(BOOKING_CNTR_NUM_SPLIT)记录数 - 存储策划员拆分后的箱量明细信息 */
        private int bookingCntrNumSplitCount;
        
        /** 删除的箱量信息(BOOKING_CNTR_NUM)记录数 - 存储集装箱的尺寸、类型、数量等信息 */
        private int bookingCntrNumCount;
        
        /** 删除的中转港信息(BOOKING_TRANSIT_PORT)记录数 - 存储多段运输中的中转港详细信息 */
        private int bookingTransitPortCount;
        
        /** 子表总删除记录数 - 上述五个子表删除记录的总和 */
        private int totalChildRecords;
    }

    /**
     * 构建操作摘要信息
     */
    public void buildOperationSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("成功删除 ").append(totalBookingCount).append(" 条订舱记录");
        
        if (deletedOrderCount > 0) {
            summary.append("，").append(deletedOrderCount).append(" 条无关联订单记录");
        }
        
        if (remainingOrderCount > 0) {
            summary.append("，保留 ").append(remainingOrderCount).append(" 条仍有关联的订单记录");
        }
        
        if (childTableStats != null && childTableStats.getTotalChildRecords() > 0) {
            summary.append("，同时清理相关子表记录 ").append(childTableStats.getTotalChildRecords()).append(" 条");
        }
        
        this.operationSummary = summary.toString();
    }

    /**
     * 创建成功的删除结果
     */
    public static BatchDeleteResultVO success(int bookingCount, int deletedOrderCount, int remainingOrderCount,
                                            ChildTableDeleteStatistics childStats,
                                            List<String> deletedBookingNos, List<String> deletedOrderNos, 
                                            List<String> retainedOrderNos) {
        BatchDeleteResultVO result = new BatchDeleteResultVO();
        result.setTotalBookingCount(bookingCount);
        result.setDeletedOrderCount(deletedOrderCount);
        result.setRemainingOrderCount(remainingOrderCount);
        result.setChildTableStats(childStats);
        result.setDeletedBookingNos(deletedBookingNos);
        result.setDeletedOrderNos(deletedOrderNos);
        result.setRetainedOrderNos(retainedOrderNos);
        result.buildOperationSummary();
        return result;
    }

    /**
     * 创建空的删除结果（没有记录被删除）
     */
    public static BatchDeleteResultVO empty() {
        BatchDeleteResultVO result = new BatchDeleteResultVO();
        result.setOperationSummary("没有符合条件的记录被删除");
        return result;
    }
}