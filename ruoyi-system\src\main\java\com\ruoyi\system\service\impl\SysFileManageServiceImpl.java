package com.ruoyi.system.service.impl;


import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.MinioConfig;
import com.ruoyi.common.utils.file.MinioUtil;
import com.ruoyi.system.domain.SysFileManage;
import com.ruoyi.system.mapper.SysFileManageMapper;
import com.ruoyi.system.service.ISysFileManageService;
import io.minio.errors.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class SysFileManageServiceImpl extends ServiceImpl<SysFileManageMapper, SysFileManage> implements ISysFileManageService {

    @Autowired
    private SysFileManageMapper sysFileManageMapper;

    @Override
    public AjaxResult delete(SysFileManage fileManage) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {

        if(StringUtils.isEmpty(fileManage.getFileId()) || "null".equals(fileManage.getFileId())){
            return AjaxResult.error("请选择文件");
        }

        SysFileManage origin = super.getById(fileManage.getFileId());

        String filePath = origin.getFilePath();

        filePath = filePath.replace("/" + MinioConfig.getBucketName(), "");

        MinioUtil.deleteFile(MinioConfig.getBucketName(),filePath);

        if(super.removeById(fileManage.getFileId())){

            return AjaxResult.success();

        }else {

            return AjaxResult.error("删除数据失败");

        }
    }

    @Override
    public List<SysFileManage> selectMyList(Long fileTypeId, String fileBusinessId) {
        return sysFileManageMapper.selectMyList(fileTypeId,fileBusinessId);
    }
}
