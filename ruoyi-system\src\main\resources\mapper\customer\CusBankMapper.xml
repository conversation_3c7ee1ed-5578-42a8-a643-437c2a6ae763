<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.customer.mapper.CusBankMapper">
    <delete id="deleteCusBankByCusId">
        UPDATE cus_bank
        SET del_flag = '2'
        WHERE cus_main_id = #{cusMainId}
    </delete>
    <select id="selectCusBankListByCusId" resultType="com.ruoyi.customer.domain.CusBank">
        SELECT * FROM cus_bank
        WHERE del_flag = '0'
        AND cus_main_id = #{cusMainId}
        order by sort
    </select>

</mapper>