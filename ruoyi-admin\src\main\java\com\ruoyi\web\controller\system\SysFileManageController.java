package com.ruoyi.web.controller.system;


import com.google.common.io.ByteStreams;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileDownloadUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.MinioConfig;
import com.ruoyi.system.domain.SysFileManage;
import com.ruoyi.system.domain.SysFileType;
import com.ruoyi.system.service.ISysFileManageService;
import com.ruoyi.system.service.ISysFileTypeService;
import io.minio.errors.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.ruoyi.system.domain.table.SysFileManageTableDef.SYS_FILE_MANAGE;
import static com.ruoyi.system.domain.table.SysFileTypeTableDef.SYS_FILE_TYPE;

@RestController
@RequestMapping("/fileManage")
public class SysFileManageController extends BaseController {

    @Autowired
    ISysFileManageService sysFileManageService;

    @Autowired
    ISysFileTypeService sysFileTypeService;

    @GetMapping("list")
    public AjaxResult list(SysFileManage sysFileManage){

        List<SysFileType> fileTypes = sysFileTypeService.list(QueryWrapper.create()
                .select(
                        SysFileType::getFileTypeId,
                        SysFileType::getFileType,
                        SysFileType::getFileBusinessType,
                        SysFileType::getFileSort
                )
                .eq(SysFileType::getFileBusinessType,sysFileManage.getFileBusinessType())
                .eq(SysFileType::getFileType,sysFileManage.getFileType())
                .orderBy(SysFileType::getFileType,false)
                .orderBy(SysFileType::getFileSort,true)
                .orderBy(SysFileType::getFileTypeId,false)
        );



        List<SysFileManage> fileManages = new ArrayList<>();

        for(SysFileType fileType:fileTypes){

            List<SysFileManage> list = sysFileManageService.list(
                    QueryWrapper.create()
                            .select(
                                    SysFileManage::getFileId,
                                    SysFileManage::getFileName,
                                    SysFileManage::getFilePath,
                                    SysFileManage::getFileBusinessId
                            )
                            .select(
                                    SysFileType::getFileTypeId,
                                    SysFileType::getFileType,
                                    SysFileType::getFileBusinessType
                            )
                            .from(SYS_FILE_TYPE)
                            .leftJoin(SYS_FILE_MANAGE).on(SysFileManage::getFileTypeId,SysFileType::getFileTypeId)
                            .eq(SysFileType::getFileTypeId,fileType.getFileTypeId())
                            .eq(SysFileManage::getFileBusinessId,sysFileManage.getFileBusinessId())
                            .orderBy(SysFileManage::getFileId,false)
            );



            if(!list.isEmpty()){
                fileManages.addAll(list);
            }else {

                SysFileManage fileManage = new SysFileManage();

                BeanUtils.copyProperties(fileType,fileManage);

                fileManages.add(fileManage);

            }


        }

        return success(fileManages);

    }

    @GetMapping("getOne/{fileId}")
    public AjaxResult getOne(@PathVariable String fileId){

        if(StringUtils.isEmpty(fileId) || "null".equals(fileId)){
            return AjaxResult.error("请选择文件");
        }

        return AjaxResult.success(sysFileManageService.getById(fileId));

    }

    @PostMapping("updateFileName")
    public AjaxResult updateFileName(@RequestBody SysFileManage fileManage){
       return toAjax( UpdateChain.of(SysFileManage.class)
               .set(SysFileManage::getFileName,fileManage.getFileName())
               .set(SysFileManage::getUpdateBy,SecurityUtils.getUsername())
               .set(SysFileManage::getUpdateTime,new Date())
               .eq(SysFileManage::getFileId,fileManage.getFileId())
               .update());
    }

    @PostMapping("upload")
    public AjaxResult upload(MultipartFile file, String fileTypeId, String fileBusinessId) throws IOException {

        if(file.isEmpty()){
            return error("请上传文件");
        }

        SysFileType sysFileType = sysFileTypeService.getById(fileTypeId);

        if(StringUtils.isNull(sysFileType)){
            return error("文件类型不存在");
        }

        if(StringUtils.isEmpty(fileBusinessId)){
            return error("请传入业务ID");
        }

        if(sysFileType.getLimitNum() > 0){

            //校验是否存在超过数量的文件
            Integer limitNum = sysFileType.getLimitNum();

            long checkSize = sysFileManageService.count(QueryWrapper.create()
                    .eq(SysFileManage::getFileTypeId, fileTypeId)
                    .eq(SysFileManage::getFileBusinessId, fileBusinessId)
            );

            if(checkSize >= limitNum){
                return error("文件数量超过限制");
            }

        }

        String fileName = file.getOriginalFilename();

        String filePath = FileUploadUtils.uploadMinio(file);

        SysFileManage sysFileManage = new SysFileManage();

        sysFileManage.setFileTypeId(fileTypeId);
        sysFileManage.setFileBusinessId(fileBusinessId);
        sysFileManage.setFileName(fileName);
        sysFileManage.setFilePath(filePath);

        return toAjax(sysFileManageService.save(sysFileManage));

    }

    @RequestMapping("download")
    @Anonymous
    public void download(HttpServletResponse response,String fileId) throws Exception {

        if(StringUtils.isEmpty(fileId)){
            throw new Exception("文件ID为空");
        }

        SysFileManage fileManage = sysFileManageService.getById(fileId);

        if(StringUtils.isNull(fileManage)){
            throw new Exception("文件不存在");
        }

        String fileName = fileManage.getFileName();

        String filePath = fileManage.getFilePath();

        filePath = filePath.replace("/" + MinioConfig.getBucketName(), "");

        InputStream inputStream = FileDownloadUtils.getMinio(filePath);
        StringBuilder contentDispositionValue = new StringBuilder();
        fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
        contentDispositionValue.append("attachment; filename=")
                .append(fileName)
                .append(";")
                .append("filename*=")
                .append("utf-8''")
                .append(fileName);
        response.setHeader("Content-disposition", contentDispositionValue.toString());
        OutputStream outputStream = response.getOutputStream();
        //将bytes 写入到输出流中
        ByteStreams.copy(inputStream, outputStream);
        inputStream.close();
        outputStream.close();

    }

    @DeleteMapping
    public AjaxResult delete(@RequestBody SysFileManage fileManage) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {

        return sysFileManageService.delete(fileManage);

    }

}
