## 订单物流系统数据库设计QA总结

本次总结旨在梳理我们围绕订单物流系统V3.0数据库DDL脚本进行的多次深入讨论，特别是关于箱量拆分和物流环节设计的关键决策与演进过程。

### 一、 `BOOKING_CNTR_NUM` 与 `BOOKING_CNTR_NUM_SPLIT` 的设计与关系

- **初始问题：** 如何将 `BOOKING_CNTR_NUM` 中的一条箱量数据拆分成多条，并确保总和一致？
- **讨论过程与决策：**
  - **引入子表：** 决定新增 `BOOKING_CNTR_NUM_SPLIT` 表作为 `BOOKING_CNTR_NUM` 的子表，实现一对多关系。
  - **命名优化：** 采纳建议，将子表命名为 `BOOKING_CNTR_NUM_SPLIT`，更符合命名规范且简洁。
  - **`BOOKING_CNTR_NUM` 的定位：** 明确 `BOOKING_CNTR_NUM` 中的 `QUANTITY` 和 `TOTAL_WEIGHT` 为“计划量”，一旦单证员录入后即“固定不变”。
  - **`BOOKING_CNTR_NUM_SPLIT` 的初始化：** 明确在单证员录入 `BOOKING_CNTR_NUM` 数据时，应**自动初始化一条**与父记录完全相同的 `BOOKING_CNTR_NUM_SPLIT` 数据。所有后续的拆分、合并、分配操作都只针对 `BOOKING_CNTR_NUM_SPLIT` 表进行。
  - **字段继承：** `BOOKING_CNTR_NUM_SPLIT` 继承 `BOOKING_CNTR_NUM` 中所有箱型、危险品、货类等属性字段，确保拆分后属性一致。
  - **拆分维度：** 策划员根据“船的情况”凭感觉拆分，无强依赖的固定维度。
  - **总和校验：** `BOOKING_CNTR_NUM_SPLIT` 中所有 `SPLIT_QUANTITY` 的总和**必须等于**其父 `BOOKING_CNTR_NUM`的 `QUANTITY`，此校验在 **Java 业务代码层**实现。 `SPLIT_TOTAL_WEIGHT` 允许有小误差。
  - **父表状态标识：** `BOOKING_CNTR_NUM` 新增 `IS_SPLIT_FLAG` 字段（0否/1是），标识其是否已被拆分。
  - **修改与锁定：**
    - 一旦 `BOOKING_CNTR_NUM` 被拆分或其关联的 `BOOKING_CNTR_NUM_SPLIT` 被分配，原始 `BOOKING_CNTR_NUM` 不可修改。
    - 若所有关联的 `BOOKING_CNTR_NUM_SPLIT` 被删除（或合并），`BOOKING_CNTR_NUM` 可恢复到未拆分状态并允许修改。
  - **合并操作：** “合回”操作指 `BOOKING_CNTR_NUM_SPLIT` 内部的多条记录合并为一条或几条新记录，不影响 `BOOKING_CNTR_NUM`。
  - **删除策略：** `BOOKING_CNTR_NUM_SPLIT` 外键 `BOOKING_CNTR_NUM_ID` 设置 `ON DELETE CASCADE`。应用程序层面需在删除 `BOOKING_CNTR_NUM` 前检查是否存在关联的 `BOOKING_CNTR_NUM_SPLIT` 记录，若存在则阻止删除。

### 二、 `LOGISTICS_MAIN` 和运输环节子表的设计

- **初始问题：** `LOGISTICS_MAIN` 的 `BUSINESS_TYPE` 字段定义不准确，且 `CONTAINER_DETAILS CLOB` 字段存在冗余。
- **讨论过程与决策：**
  - **`LOGISTICS_MAIN.BUSINESS_TYPE` 调整：** 将 `LOGISTICS_MAIN.BUSINESS_TYPE` 的业务类型调整为运输模式，包括 `WATERWAY`（水路）、`ROADWAY`（陆路）、`AIRWAY`（航空）。
  - **移除 `CLOB` 字段：** 确认并移除了 `LOGISTICS_MAIN.CONTAINER_DETAILS CLOB` 字段。
  - **引入 `LOGISTIC_WATERWAY` 子表：** 新增 `LOGISTIC_WATERWAY` 表作为 `LOGISTICS_MAIN` 的水路运输子表，记录具体的船运信息（航线、船舶、航次、港口、时间等）。
  - **移除 `LOGISTIC_WATERWAY.CONTAINER_ALLOCATION CLOB`：** 确认并移除了 `LOGISTIC_WATERWAY.CONTAINER_ALLOCATION CLOB` 字段。
  - **`LOGISTIC_WATERWAY` 的拆分：** 明确 `LOGISTIC_WATERWAY` 自身也可以被拆分成多条记录，表示同一环节的多次运输。`SEQUENCE_NO` 字段用于表示运输段顺序。

### 三、 `BOOKING_CNTR_NUM_SPLIT` 与运输环节的关联

- **初始问题：** `BOOKING_CNTR_NUM_SPLIT` 与 `LOGISTIC_WATERWAY` 之间是多对多关系，还是直接关联？
- **讨论过程与决策：**
  - **放弃中间表：** 决定不使用 `booking_cntr_split_waterway_link` 中间表。
  - **直接关联：** 在 `BOOKING_CNTR_NUM_SPLIT` 表中直接添加 `LOGISTIC_WATERWAY_ID` 字段，关联到 `LOGISTIC_WATERWAY.ID`。
  - **业务含义调整：** 这意味着一个 `BOOKING_CNTR_NUM_SPLIT` 记录在任何给定时间点，**只能分配给一个**`LOGISTIC_WATERWAY` 记录。如果一个箱量明细需要经历多个水路运输环节（A到B，再B到C），则 `BOOKING_CNTR_NUM_SPLIT.LOGISTIC_WATERWAY_ID` 会随着运输段的推进而**更新**。历史运输环节的追溯需要通过其他机制（例如，在 `LOGISTIC_WATERWAY` 记录中通过 `SEQUENCE_NO` 串联，或通过业务日志）。
  - **分配状态：** `BOOKING_CNTR_NUM_SPLIT` 新增 `STATUS` 字段（`DRAFT`/`ALLOCATED`/`CANCELLED`），当其被分配给 `LOGISTIC_WATERWAY` 后，状态变为 `ALLOCATED`，此时不可再拆分、合并或修改。

### 四、 其他重要决策

- **乐观锁：** 全表均包含 `VERSION` 字段，用于乐观锁机制，处理并发操作。
- **历史追溯与审计：** 暂时不考虑箱量拆分和合并的历史操作记录。
- **查询重点：** 后续业务操作和查询将主要集中在 `BOOKING_CNTR_NUM_SPLIT` 表。
- **未来扩展性：** 陆路和航空运输子表（`LOGISTICS_ROADWAY`, `LOGISTICS_AIRWAY`）将在未来根据需求创建。

### 总结

通过这几轮深入的问答和讨论，我们已经将订单物流系统的核心数据库结构进行了显著的优化和细化。特别是对箱量拆分逻辑的明确，以及物流环节的范式化处理，使得系统能够更好地支持策划员的配船业务，并为未来的功能扩展奠定了坚实基础。

接下来的优化点将集中在：

1. **实际箱号/铅封号等信息的存储：** 既然 `CLOB` 字段已移除，需要明确这些关键业务信息将如何存储和管理。
2. **`LOGISTIC_WATERWAY` 自身拆分后的父子关系：** 考虑是否需要为 `LOGISTIC_WATERWAY` 引入 `PARENT_WATERWAY_ID` 字段以明确其拆分后的层次结构。
3. **多段运输的箱量追溯：** 重新审视 `BOOKING_CNTR_NUM_SPLIT` 随运输段更新 `LOGISTIC_WATERWAY_ID` 的模式，并思考如何高效地追溯一个箱量明细所经历的所有运输环节。
4. **跨运输类型关联：** 思考未来 `BOOKING_CNTR_NUM_SPLIT` 如何与陆路/航空运输环节关联，以及是否需要一个更通用的关联模型。

这些持续的深入探讨将确保我们的数据库设计既能满足当前需求，又具备良好的可扩展性和健壮性。
