package com.ruoyi.contract.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 合同 收付关系 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Table(value = "contract_payment_relation")
@Data
public class ContractPaymentRelation {

    /**
     * 合同ID
     */
    @Column(value = "contract_id")
    private String contractId;

    /**
     * 收付关系 关联字典 payment_relations
     */
    @Column(value = "relation_code")
    private String relationCode;
}
