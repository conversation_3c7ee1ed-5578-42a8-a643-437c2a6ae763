package com.ruoyi.customer.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "CUS_BANK")
public class CusBank extends BaseEntity {

    /**
     * 银行账户id
     */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String cusBankId;

    /**
     * 客户id
     */
    @Column(value = "CUS_MAIN_ID")
    private String cusMainId;

    /**
     * 开户行
     */
    @Column(value = "BANK_OF_DEPOSIT")
    private String bankOfDeposit;

    /**
     * 银行账户
     */
    @Column(value = "BANK_ACCOUNT")
    private String bankAccount;

    /**
     * 开户名
     */
    @Column(value = "BANK_ACCOUNT_NAME")
    private String bankAccountName;

    @Column(value = "DEL_FLAG", isLogicDelete = true)
    private String delFlag;

    @Column(value = "REMARK")
    private String remark;

    // 排序
    @Column(value = "SORT")
    private String sort;

}
