package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicPortArea;

import java.util.List;

public interface IBasicPortAreaService extends IService<BasicPortArea> {

    /**
     * 查询港区管理
     *
     * @param id 港区管理主键
     * @return 港区管理
     */
    public BasicPortArea selectBasicPortAreaById(String id);

    /**
     * 查询港区管理列表
     *
     * @param basicPortArea 港区管理
     * @return 港区管理集合
     */
    public List<BasicPortArea> selectBasicPortAreaList(BasicPortArea basicPortArea);

    /**
     * 新增港区管理
     *
     * @param basicPortArea 港区管理
     * @return 结果
     */
    public int insertBasicPortArea(BasicPortArea basicPortArea) throws Exception;

    /**
     * 修改港区管理
     *
     * @param basicPortArea 港区管理
     * @return 结果
     */
    public int updateBasicPortArea(BasicPortArea basicPortArea) throws Exception;

    /**
     * 批量删除港区管理
     *
     * @param ids 需要删除的港区管理主键集合
     * @return 结果
     */
    public int deleteBasicPortAreaByIds(List<String> ids);

    /**
     * 删除港区管理信息
     *
     * @param id 港区管理主键
     * @return 结果
     */
    public int deleteBasicPortAreaById(String id);

}
