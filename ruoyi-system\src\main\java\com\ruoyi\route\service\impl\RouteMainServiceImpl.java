package com.ruoyi.route.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.route.domain.RouteMain;
import com.ruoyi.route.mapper.RouteMainMapper;
import com.ruoyi.route.service.IRouteMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.ruoyi.route.domain.table.RouteMainTableDef.ROUTE_MAIN;

/**
 * 航线主线Service业务层处理
 */
@Slf4j
@Service
public class RouteMainServiceImpl extends ServiceImpl<RouteMainMapper, RouteMain> implements IRouteMainService {
    
    /**
     * 分页查询航线主线
     */
    @Override
    public Page<RouteMain> selectRouteMainPage(int pageNumber, int pageSize, RouteMain routeMain) {
        log.debug("分页查询航线主线 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, routeMain);
        
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(ROUTE_MAIN)
                .where(ROUTE_MAIN.MAIN_NAME.like(routeMain.getMainName(), StringUtils.isNotBlank(routeMain.getMainName())))
                .orderBy(ROUTE_MAIN.CREATE_TIME.desc());
        
        Page<RouteMain> result = mapper.paginate(pageNumber, pageSize, queryWrapper);
        
        log.debug("分页查询航线主线完成 - 总记录数: {}", result.getTotalRow());
        
        return result;
    }

    /**
     * 判断主线名称是否已存在（全局唯一，排除自身）
     */
    public boolean existsMainName(String mainName, String excludeId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select()
            .from(ROUTE_MAIN)
            .where(ROUTE_MAIN.MAIN_NAME.eq(mainName));
        if (excludeId != null) {
            queryWrapper.and(ROUTE_MAIN.MAIN_ID.ne(excludeId));
        }
        return mapper.selectCountByQuery(queryWrapper) > 0;
    }
} 