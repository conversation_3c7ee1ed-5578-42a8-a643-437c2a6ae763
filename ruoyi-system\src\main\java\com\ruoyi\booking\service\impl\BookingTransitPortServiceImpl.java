package com.ruoyi.booking.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.BookingTransitPort;
import com.ruoyi.booking.mapper.BookingTransitPortMapper;
import com.ruoyi.booking.service.IBookingTransitPortService;
import org.springframework.stereotype.Service;

/**
 * 中转港表服务实现类
 * 继承ServiceImpl获得基础CRUD方法实现
 */
@Service
public class BookingTransitPortServiceImpl extends ServiceImpl<BookingTransitPortMapper, BookingTransitPort> implements IBookingTransitPortService {
    // 继承ServiceImpl后自动获得基础CRUD方法的实现
}