package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 自有箱基础信息实体类
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("BASIC_CNTR_MAIN_OWN")
public class BasicCntrMainOwn extends BaseEntity {

    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /**
     * 箱号
     */
    @Column("cntr_no")
    private String cntrNo;

    /**
     * 尺寸(如20GP, 40GP, 40HQ等)
     */
    @Column("cntr_size")
    private String cntrSize;

    /**
     * 箱型(如干货箱、冷藏箱等)
     */
    @Column("cntr_type")
    private String cntrType;

    /**
     * 空箱重量(公斤)
     */
    @Column("tare_weight")
    private BigDecimal tareWeight;

    /**
     * 最大总重量(公斤)
     */
    @Column("max_gross_weight")
    private BigDecimal maxGrossWeight;

    /**
     * 箱属(自有、租赁等)
     */
    @Column("cntr_ownership")
    private String cntrOwnership;

    /**
     * 采购日期
     */
    @Column("purchase_date")
    private Date purchaseDate;

    /**
     * 采购金额(元)
     */
    @Column("purchase_amount")
    private BigDecimal purchaseAmount;

    /**
     * 生产方/制造商
     */
    @Column("manufacturer")
    private String manufacturer;

    /**
     * 生产日期
     */
    @Column("manufacture_date")
    private Date manufactureDate;

    /**
     * 默认货类
     */
    @Column("default_cargo_type")
    private String defaultCargoType;

    /**
     * 附件路径
     */
    @Column("attachment_path")
    private String attachmentPath;

    /**
     * 版本号(乐观锁)
     */
    @Column("version")
    private Long version;

    /**
     * 删除标志(0:正常 1:删除)
     */
    @Column(isLogicDelete = true)
    private String delFlag;
}
