package com.ruoyi.web.controller.system;

import java.util.*;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.model.DifyLoginBody;
import com.ruoyi.common.utils.RsaUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysMenuService;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) throws Exception {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), RsaUtils.decryptByPrivateKey(loginBody.getPassword()), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    @PostMapping("/loginByDify")
    @Anonymous
    public AjaxResult loginByDify(@RequestBody DifyLoginBody difyLoginBody){

        AjaxResult ajaxResult = AjaxResult.success();

        String userNo = difyLoginBody.getUserNo();//userId_userName 的形式

        //原始传入userId
        String oldUserId = difyLoginBody.getUserId();

        String userId = null;
        try {
            userId = RsaUtils.decryptByPrivateKey(oldUserId);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

        //原始传入userName
        String oldUserName = difyLoginBody.getUserName();

        String userName = null;
        try {
            userName = RsaUtils.decryptByPrivateKey(oldUserName);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

        //校验userNo的前半部分是否等于userId
        if(!userNo.split("_")[0].equals(userId)){
            return AjaxResult.error("用户编号不匹配");
        }

        //校验userNo的后半部分是否等于userName
        if(!userNo.split("_")[1].equals(userName)){
            return AjaxResult.error("用户名不匹配");
        }

        //加密信息
        String encMsg = difyLoginBody.getEncMsg();

        Map<String, Object> map = new LinkedHashMap<>();

        map.put("userNo", userNo);
        map.put("userId",userId);
        map.put("userName",userName);

        String json = JSONObject.toJSONString(map);

        String md5 = Md5Utils.hash(json);

        if(StringUtils.isNotEmpty(encMsg) && encMsg.equals(md5)){
            String token = null;
            try {
                token = loginService.loginByDify(Long.valueOf(userId), userName);
            } catch (Exception e) {
                return AjaxResult.error(e.getMessage());
            }
            ajaxResult.put(Constants.TOKEN, token);
        }else {
            return AjaxResult.error("加密信息校验失败");
        }

        return ajaxResult;

    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions))
        {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
