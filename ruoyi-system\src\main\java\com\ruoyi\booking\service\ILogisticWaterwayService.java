package com.ruoyi.booking.service;

import com.mybatisflex.core.service.IService;
import com.alibaba.fastjson2.JSONArray;
import com.mybatisflex.core.paginate.Page;
import com.ruoyi.booking.domain.LogisticWaterway;

import java.util.List;

public interface ILogisticWaterwayService extends IService<LogisticWaterway> {

    Page<LogisticWaterway> selectLogisticWaterwayPage(int pageNumber, int pageSize, LogisticWaterway logisticWaterway,
                                                      String searchValue);

    boolean splitLogisticWaterway(String id, JSONArray splitRecords);

    boolean mergeLogisticWaterway(List<String> ids);
}