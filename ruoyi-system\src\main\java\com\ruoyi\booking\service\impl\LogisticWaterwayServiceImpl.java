package com.ruoyi.booking.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.BookingCntrNumSplit;
import com.ruoyi.booking.domain.BookingMain;
import com.ruoyi.booking.domain.LogisticWaterway;
import com.ruoyi.booking.domain.LogisticsMain;
import com.ruoyi.booking.mapper.LogisticWaterwayMapper;
import com.ruoyi.booking.service.IBookingCntrNumSplitService;
import com.ruoyi.booking.service.ILogisticWaterwayService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LogisticWaterwayServiceImpl extends ServiceImpl<LogisticWaterwayMapper, LogisticWaterway>
        implements ILogisticWaterwayService {

    @Autowired
    IBookingCntrNumSplitService bookingCntrNumSplitService;

    @Override
    public Page<LogisticWaterway> selectLogisticWaterwayPage(int pageNumber, int pageSize,
            LogisticWaterway logisticWaterway, String searchValue) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        // 三层关联查询: LogisticWaterway -> LogisticsMain -> BookingMain
        queryWrapper.select("lw.*", "bm.SHIPPER_NAME")
                .from(LogisticWaterway.class).as("lw")
                .leftJoin(LogisticsMain.class).on(LogisticWaterway::getLogisticsMainId, LogisticsMain::getId)
                .leftJoin(BookingMain.class).as("bm").on(LogisticsMain::getBookingId, BookingMain::getId);
        // 状态条件过滤
        if (StringUtils.isNotBlank(logisticWaterway.getStatus())) {
            queryWrapper.and(LogisticWaterway::getStatus).eq(logisticWaterway.getStatus());
        }

        // 搜索条件（支持模糊匹配）
        if (StringUtils.isNotBlank(searchValue)) {
            queryWrapper.and(qw -> {
                qw.or(LogisticWaterway::getVesselName).like(searchValue)
                        .or(LogisticWaterway::getLoadingTerminalName).like(searchValue)
                        .or(LogisticWaterway::getUnloadingTerminalName).like(searchValue)
                        .or(BookingMain::getShipperName).like(searchValue);
            });
        }

        // 默认排序
        queryWrapper.orderBy(LogisticWaterway::getEtd, true)
                .orderBy(LogisticWaterway::getLogisticsMainId, true)
                .orderBy(LogisticWaterway::getSequenceNo, true);

        // 先查询主表数据
        Page<LogisticWaterway> page = mapper.paginate(pageNumber, pageSize, queryWrapper);

        // 获取主表数据的ID列表
        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            List<String> waterwayIds = page.getRecords().stream()
                    .map(LogisticWaterway::getId)
                    .toList();

            // 批量查询关联的 BookingCntrNumSplit 数据
            QueryWrapper bcnsQuery = QueryWrapper.create()
                    .select()
                    .from(BookingCntrNumSplit.class)
                    .where(BookingCntrNumSplit::getRelatedWaterwayId).in(waterwayIds);

            List<BookingCntrNumSplit> bcnsList = bookingCntrNumSplitService.getMapper().selectListByQuery(bcnsQuery);

            // 按 waterwayId 分组
            Map<String, List<BookingCntrNumSplit>> bcnsMap = bcnsList.stream()
                    .collect(Collectors.groupingBy(BookingCntrNumSplit::getRelatedWaterwayId));

            // 将分组后的数据设置到对应的 LogisticWaterway 对象中
            for (LogisticWaterway waterway : page.getRecords()) {
                List<BookingCntrNumSplit> splits = bcnsMap.getOrDefault(waterway.getId(), new ArrayList<>());
                JSONObject cntrJson = getCntrJson();
                // 统计并设置箱量信息
                for (BookingCntrNumSplit split : splits) {
                    String key;
                    if (StringUtils.contains(split.getContainerTypeName(), "普通柜")) {
                        // 普通箱
                        key = split.getContainerSizeCode()
                                + (StringUtils.equals(split.getIsEmpty(), "0") ? "Full" : "Empty");
                        cntrJson.put(key, cntrJson.getIntValue(key) + split.getSplitQuantity());
                    } else {
                        // 特殊箱
                        cntrJson.put("other", cntrJson.getIntValue("other") + split.getSplitQuantity());
                    }
                }
                waterway.setContainerAllocation(JSONObject.toJSONString(cntrJson));
                waterway.setBookingCntrNumSplitList(splits);
            }
        }
        return page;
    }

    @Override
    @Transactional
    public boolean splitLogisticWaterway(String id, JSONArray splitRecords) {
        LogisticWaterway originalWaterway = getById(id);

        if (originalWaterway == null) {
            log.warn("拆单失败：找不到ID为 {} 的物流水运记录", id);
            return false;
        }

        int maxSeq = mapper.selectMaxSequenceNo(originalWaterway);

        // 保存拆分后的记录
        for (int i = 0; i < splitRecords.size(); i++) {
            // 创建新的水路运输记录
            LogisticWaterway newWaterway = new LogisticWaterway();
            BeanUtils.copyProperties(originalWaterway, newWaterway); // 复制基础属性
            newWaterway.setId(null); // mybatis-flex自动生成雪花id
            if (i > 0) {
                newWaterway.setSequenceNo(++maxSeq); // 第二条起使用最大序号
            }
            save(newWaterway);

            // 处理箱量拆分记录
            JSONArray recordSplits = splitRecords.getJSONArray(i);
            List<BookingCntrNumSplit> splitList = recordSplits.toJavaList(BookingCntrNumSplit.class);

            for (BookingCntrNumSplit split : splitList) {
                split.setId(null); // mybatis-flex自动生成雪花id
                split.setRelatedWaterwayId(newWaterway.getId()); // 关联新的水路运输记录
                bookingCntrNumSplitService.save(split);
            }
        }

        // 删除原始记录
        QueryWrapper bcnsQuery = QueryWrapper.create()
                .select()
                .from(BookingCntrNumSplit.class)
                .where(BookingCntrNumSplit::getRelatedWaterwayId).eq(id);
        bookingCntrNumSplitService.remove(bcnsQuery);
        removeById(id);

        return true;
    }

    @Override
    @Transactional
    public boolean mergeLogisticWaterway(List<String> ids) {
        // 1.批量查询所有需要合并的记录
        QueryWrapper bcnsQuery = QueryWrapper.create()
                .select()
                .from(BookingCntrNumSplit.class)
                .where(BookingCntrNumSplit::getRelatedWaterwayId).in(ids);

        List<LogisticWaterway> records = listByIds(ids);
        List<BookingCntrNumSplit> bcnsList = bookingCntrNumSplitService.list(bcnsQuery);
        if (records.isEmpty() || bcnsList.isEmpty()) {
            log.error("合并失败：未找到任何水运或箱量记录");
            return false;
        }
        if (records.size() < 2 || bcnsList.size() < 2) {
            log.error("合并失败：至少需要两个记录才能进行合并");
            return false;
        }

        // 获取最小的 sequenceNo
        int seq = records.stream()
                .mapToInt(LogisticWaterway::getSequenceNo)
                .min()
                .orElse(1000);

        // 2. 创建新waterway记录
        LogisticWaterway newRecord = new LogisticWaterway();
        LogisticWaterway firstRecord = records.get(0);
        BeanUtils.copyProperties(firstRecord, newRecord);
        newRecord.setId(null);
        newRecord.setSequenceNo(seq);
        save(newRecord);
        // 3.合并箱量信息
        Map<String, BookingCntrNumSplit> mergedMap = new HashMap<>();

        for (BookingCntrNumSplit split : bcnsList) {
            // 构造用于比较的键，包含所有需要比较的字段
            String key = String.join("|",
                    Objects.toString(split.getContainerSizeCode(), ""),
                    Objects.toString(split.getContainerTypeCode(), ""),
                    Objects.toString(split.getIsEmpty(), ""),
                    Objects.toString(split.getIsDangerous(), ""),
                    Objects.toString(split.getDangerousLevelId(), ""),
                    Objects.toString(split.getDangerousLevelCode(), ""),
                    Objects.toString(split.getDangerousLevelName(), ""),
                    Objects.toString(split.getIsRefrigerated(), ""),
                    Objects.toString(split.getCargoTypeCode(), ""),
                    Objects.toString(split.getIsOversize(), ""),
                    Objects.toString(split.getDelFlag(), ""));

            if (mergedMap.containsKey(key)) {
                // 如果已存在相同键的记录，则累加数量和重量
                BookingCntrNumSplit existing = mergedMap.get(key);
                existing.setSplitQuantity(existing.getSplitQuantity() + split.getSplitQuantity());
                existing.setSplitTotalWeight(
                        existing.getSplitTotalWeight().add(split.getSplitTotalWeight()));
            } else {
                // 否则，放入map中（需要创建新的实例避免引用问题）
                BookingCntrNumSplit newSplit = new BookingCntrNumSplit();
                BeanUtils.copyProperties(split, newSplit);
                newSplit.setId(null); // 设置新ID
                newSplit.setRelatedWaterwayId(newRecord.getId()); // 关联到新的水运记录
                mergedMap.put(key, newSplit);
            }
        }

        List<BookingCntrNumSplit> mergedBcnsList = new ArrayList<>(mergedMap.values());

        // 4. 插入新记录和箱量记录
        bookingCntrNumSplitService.saveBatch(mergedBcnsList); // 批量保存合并后的箱量记录

        // 5. 删除原始记录
        bookingCntrNumSplitService.remove(bcnsQuery);
        removeByIds(ids);

        return true;
    }

    private JSONObject getCntrJson() {
        JSONObject cntrObj = new JSONObject();
        cntrObj.put("20Empty", 0);
        cntrObj.put("20Full", 0);
        cntrObj.put("40Empty", 0);
        cntrObj.put("40Full", 0);
        cntrObj.put("other", 0);
        return cntrObj;
    }
}