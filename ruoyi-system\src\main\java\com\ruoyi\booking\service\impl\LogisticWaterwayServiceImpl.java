package com.ruoyi.booking.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.BookingCntrNumSplit;
import com.ruoyi.booking.domain.BookingMain;
import com.ruoyi.booking.domain.LogisticWaterway;
import com.ruoyi.booking.domain.LogisticsMain;
import com.ruoyi.booking.mapper.LogisticWaterwayMapper;
import com.ruoyi.booking.service.ILogisticWaterwayService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class LogisticWaterwayServiceImpl extends ServiceImpl<LogisticWaterwayMapper, LogisticWaterway>
        implements ILogisticWaterwayService {

    @Override
    public Page<LogisticWaterway> selectLogisticWaterwayPage(int pageNumber, int pageSize,
                                                             LogisticWaterway logisticWaterway, String searchValue) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        // 三层关联查询: LogisticWaterway -> LogisticsMain -> BookingMain
        queryWrapper.select("lw.*", "bm.SHIPPER_NAME")
                .from(LogisticWaterway.class).as("lw")
                .leftJoin(LogisticsMain.class).on(LogisticWaterway::getLogisticsMainId, LogisticsMain::getId)
                .leftJoin(BookingMain.class).as("bm").on(LogisticsMain::getBookingId, BookingMain::getId);
//                .leftJoin(BookingCntrNumSplit.class).as("bcns").on(LogisticWaterway::getId,BookingCntrNumSplit::getRelatedWaterwayId);
        // 状态条件过滤
        if (StringUtils.isNotBlank(logisticWaterway.getStatus())) {
            queryWrapper.and(LogisticWaterway::getStatus).eq(logisticWaterway.getStatus());
        }

        // 搜索条件（支持模糊匹配）
        if (StringUtils.isNotBlank(searchValue)) {
            queryWrapper.and(qw -> {
                qw.or(LogisticWaterway::getVesselName).like(searchValue)
                        .or(LogisticWaterway::getLoadingTerminalName).like(searchValue)
                        .or(LogisticWaterway::getUnloadingTerminalName).like(searchValue)
                        .or(BookingMain::getShipperName).like(searchValue);
            });
        }

        // 默认排序
        queryWrapper.orderBy(LogisticWaterway::getEtd, true)
                .orderBy(LogisticWaterway::getLogisticsMainId, true)
                .orderBy(LogisticWaterway::getSequenceNo, true);

        return mapper.paginate(pageNumber, pageSize, queryWrapper);
    }

    @Override
    public boolean splitLogisticWaterway(String id, JSONArray splitRecords) {
        LogisticWaterway original = mapper.selectLogisticWaterwayById(id);

        if (original == null) {
            log.warn("拆单失败：找不到ID为 {} 的物流水运记录", id);
            return false;
        }

        int maxSeq = mapper.selectMaxSequenceNo(original);

//        // 保存拆分后的记录
//        for (int i = 0; i < splitRecords.size(); i++) {
//            JSONObject record = splitRecords.getJSONObject(i);
//            LogisticWaterway newRecord = new LogisticWaterway();
//            BeanUtils.copyProperties(original, newRecord); // 复制基础属性
//            newRecord.setId(IdUtils.simpleUUID()); // 设置新ID
//            newRecord.setContainerAllocation(JSONObject.toJSONString(record)); // 存储拆分信息
//            if (i > 0) {
//                newRecord.setSequenceNo(++maxSeq); // 第二条起使用最大序号
//            }
//            mapper.insertLogisticWaterway(newRecord);
//        }

        // 删除原始记录
        removeById(id);

        return true;
    }

    @Override
    public boolean mergeLogisticWaterway(List<String> ids) {
        int seq = 1000;
        List<LogisticWaterway> records = new ArrayList<>();

        // 1. 查询所有需要合并的记录
        for (String id : ids) {
            LogisticWaterway record = mapper.selectLogisticWaterwayById(id);
            if (record != null) {
                records.add(record);
                seq = Math.min(seq, record.getSequenceNo());
            }
        }

        if (records.isEmpty()) {
            log.warn("合并失败：未找到任何物流水运记录");
            return false;
        }

//        // 2. 合并 containerAllocation 字段（JSON 格式，按字段累加）
//        JSONObject mergedAllocation = new JSONObject();
//        for (LogisticWaterway record : records) {
//            JSONObject container = JSONObject.parseObject(record.getContainerAllocation());
//            for (String key : container.keySet()) {
//                Object value = container.get(key);
//                // 跳过非整数类型的字段
//                if (!(value instanceof Integer || value instanceof Long || value instanceof Short
//                        || value instanceof Byte)) {
//                    continue;
//                }
//                int existingValue = mergedAllocation.getIntValue(key);
//                int newValue = container.getIntValue(key);
//                mergedAllocation.put(key, existingValue + newValue);
//            }
//        }

        // 3. 创建新记录
        LogisticWaterway newRecord = new LogisticWaterway();
        LogisticWaterway firstRecord = records.get(0);
        BeanUtils.copyProperties(firstRecord, newRecord);
        newRecord.setId(IdUtils.simpleUUID());
        newRecord.setSequenceNo(seq);

        // 4. 插入新记录
        mapper.insertLogisticWaterway(newRecord);

        // 5. 删除原始记录
        for (LogisticWaterway record : records) {
            removeById(record.getId());
        }

        return true;
    }
}