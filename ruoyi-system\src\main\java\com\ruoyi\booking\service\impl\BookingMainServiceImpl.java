package com.ruoyi.booking.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.BookingMain;
import com.ruoyi.booking.mapper.BookingMainMapper;
import com.ruoyi.booking.service.IBookingMainService;
import org.springframework.stereotype.Service;

/**
 * 订舱主表服务实现类
 * 继承ServiceImpl获得基础CRUD方法实现
 */
@Service
public class BookingMainServiceImpl extends ServiceImpl<BookingMainMapper, BookingMain> implements IBookingMainService {
    // 继承ServiceImpl后自动获得基础CRUD方法的实现
    // 可以在这里添加自定义业务方法
}