package com.ruoyi.booking.mapper;

import com.mybatisflex.core.BaseMapper;
import com.ruoyi.booking.domain.ViewBookingEntrust;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订舱委托统一视图Mapper接口
 * 对应数据库视图: v_booking_entrust
 * 用途: 为单证员工作台提供高性能的列表查询支持
 * 
 * 注意: 视图只支持查询操作，不支持增删改操作
 */
@Mapper
public interface ViewBookingEntrustMapper extends BaseMapper<ViewBookingEntrust> {
    // 继承 BaseMapper 后，无需编写基础查询方法
    // MyBatis-Flex 会自动提供:
    // - selectListByQuery(QueryWrapper)
    // - selectOneByQuery(QueryWrapper) 
    // - paginate(Page, QueryWrapper)
    // - selectCountByQuery(QueryWrapper)
    
    // 如需自定义复杂查询方法，可在此处添加
    // 例如: 统计分析、复杂报表查询等
}