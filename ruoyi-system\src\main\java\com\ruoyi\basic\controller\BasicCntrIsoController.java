package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCntrIso;
import com.ruoyi.basic.service.IBasicCntrIsoService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 国际箱型Controller
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
@RestController
@RequestMapping("/system/cntrIso")
public class BasicCntrIsoController extends BaseController {

    @Autowired
    private IBasicCntrIsoService basicCntrIsoService;

    /**
     * 查询箱ISO列表
     */
    @PreAuthorize("@ss.hasPermi('system:cntrIso:list')")
    @GetMapping("/list")
    public Page<BasicCntrIso> list(BasicCntrIso basicCntrIso)
    {
        // startPage();
        // List<BasicCntrIso> list = basicCntrIsoService.selectBasicCntrIsoList(basicCntrIso);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create()
        .like(BasicCntrIso::getIsoCode, basicCntrIso.getIsoCode())
//        .like(BasicCntrIso::getIsoName, basicCntrIso.getIsoName())
        .orderBy(BasicCntrIso::getCreateTime, true);
        return basicCntrIsoService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出箱ISO列表
     */
    @PreAuthorize("@ss.hasPermi('system:cntrIso:export')")
    @Log(title = "箱ISO", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicCntrIso basicCntrIso)
    {
        List<BasicCntrIso> list = basicCntrIsoService.selectBasicCntrIsoList(basicCntrIso);
        ExcelUtil<BasicCntrIso> util = new ExcelUtil<BasicCntrIso>(BasicCntrIso.class);
        util.exportExcel(response, list, "箱ISO数据");
    }

    /**
     * 获取箱ISO详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:cntrIso:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicCntrIsoService.selectBasicCntrIsoById(id));
    }

    /**
     * 新增箱ISO
     */
    @PreAuthorize("@ss.hasPermi('system:cntrIso:add')")
    @Log(title = "箱ISO", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicCntrIso basicCntrIso)
    {
        return toAjax(basicCntrIsoService.insertBasicCntrIso(basicCntrIso));
    }

    /**
     * 修改箱ISO
     */
    @PreAuthorize("@ss.hasPermi('system:cntrIso:edit')")
    @Log(title = "箱ISO", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicCntrIso basicCntrIso)
    {
        return toAjax(basicCntrIsoService.updateBasicCntrIso(basicCntrIso));
    }

    /**
     * 删除箱ISO
     */
    @PreAuthorize("@ss.hasPermi('system:cntrIso:remove')")
    @Log(title = "箱ISO", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicCntrIsoService.deleteBasicCntrIsoByIds(ids));
    }

}
