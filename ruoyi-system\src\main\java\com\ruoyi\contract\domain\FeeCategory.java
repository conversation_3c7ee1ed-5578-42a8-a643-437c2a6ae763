package com.ruoyi.contract.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 费目分类 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "fee_category")
public class FeeCategory extends BaseEntity {

    /**
     * 费目分类唯一标识
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String categoryId;

    /**
     * 费目分类名称（如"运输费"）
     */
    @Column(value = "category_name")
    @NotEmpty(message = "费目分类名称不能为空")
    private String categoryName;

    /**
     * 费目名称（如"代理费"）
     */
    @Column(value = "fee_type")
    @NotEmpty(message = "费目名称不能为空")
    private String feeType;

    /**
     * 税率
     */
    @Column(value = "tax_rate")
    private java.math.BigDecimal taxRate;

    /**
     * 财务科目编码
     */
    @Column(value = "account_code")
    private String accountCode;

    /**
     * 乐观锁
     */
    @Column(value = "version")
    private Long version;

    /**
     * 逻辑删除
     */
    @Column(value = "del_flag")
    private String delFlag;
}
