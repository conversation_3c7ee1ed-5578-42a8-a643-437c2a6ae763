package com.ruoyi.plan.controller;

import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.plan.domain.RelationBookingVoyageMain;
import com.ruoyi.plan.domain.RelationBookingVoyageCntrCoarse;
import com.ruoyi.plan.domain.VoyageMain;
import com.ruoyi.plan.domain.VoyageSeq;
import com.ruoyi.basic.domain.BasicTerminal;
import com.ruoyi.basic.domain.BasicShipMain;
import com.ruoyi.plan.service.IRelationBookingVoyageMainService;
import com.ruoyi.plan.service.IRelationBookingVoyageCntrCoarseService;
import com.ruoyi.plan.service.IVoyageMainService;
import com.ruoyi.plan.service.IVoyageSeqService;
import com.ruoyi.basic.service.IBasicTerminalService;
import com.ruoyi.basic.service.IBasicShipMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/plan/RelationBookingVoyageMain")
public class RelationBookingVoyageMainController extends BaseController {

    @Autowired
    private IRelationBookingVoyageMainService relationBookingVoyageMainService;

    @Autowired
    private IRelationBookingVoyageCntrCoarseService relationBookingVoyageCntrCoarseService;

    @Autowired
    private IVoyageMainService voyageMainService;

    @Autowired
    private IVoyageSeqService voyageSeqService;

    @Autowired
    private IBasicTerminalService basicTerminalService;

    @Autowired
    private IBasicShipMainService basicShipMainService;

    @GetMapping("/list")
    public AjaxResult list(RelationBookingVoyageMain query) {
        List<RelationBookingVoyageMain> list = relationBookingVoyageMainService.list();
        return AjaxResult.success(list);
    }

    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        return AjaxResult.success(relationBookingVoyageMainService.selectById(id));
    }

    @PostMapping
    public AjaxResult add(@RequestBody RelationBookingVoyageMain entity) {
        return toAjax(relationBookingVoyageMainService.insert(entity));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody RelationBookingVoyageMain entity) {
        return toAjax(relationBookingVoyageMainService.update(entity));
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(relationBookingVoyageMainService.deleteByIds(ids));
    }

    @GetMapping("/trackInfo")
    public AjaxResult getTrackInfo(@RequestParam String bookingId) {
        // 1. 查询所有RELATION_BOOKING_VOYAGE_MAIN
        List<RelationBookingVoyageMain> mainList = relationBookingVoyageMainService
            .list(new QueryWrapper().eq("booking_id", bookingId));

        List<Map<String, Object>> resultList = new ArrayList<>();

        for (RelationBookingVoyageMain main : mainList) {
            // 查询航次号和船名
            String voyageNo = "";
            String shipChineseName = "";
            String shipId = null;
            VoyageMain voyage = voyageMainService.getById(main.getVoyageId());
            if (voyage != null) {
                voyageNo = voyage.getVoyageNo();
                shipId = voyage.getShipId();
                if (shipId != null) {
                    BasicShipMain ship = basicShipMainService.getById(shipId);
                    if (ship != null) {
                        shipChineseName = ship.getShipChineseName();
                    }
                }
            }
            // 查询靠序-起始
            String startTerminalName = "";
            VoyageSeq startSeq = voyageSeqService.getById(main.getStartSeqId());
            if (startSeq != null) {
                BasicTerminal terminal = basicTerminalService.getById(startSeq.getTerminalId());
                if (terminal != null) startTerminalName = terminal.getTerminalName();
            }
            // 查询靠序-结束
            String endTerminalName = "";
            VoyageSeq endSeq = voyageSeqService.getById(main.getEndSeqId());
            if (endSeq != null) {
                BasicTerminal terminal = basicTerminalService.getById(endSeq.getTerminalId());
                if (terminal != null) endTerminalName = terminal.getTerminalName();
            }
            // 2. 用main.id查RELATION_BOOKING_VOYAGE_CNTR_COARSE
            List<RelationBookingVoyageCntrCoarse> cntrList = relationBookingVoyageCntrCoarseService
                .list(new QueryWrapper().eq("relation_booking_voyage_main_id", main.getId()));
            // 3. 组装每一行
            for (RelationBookingVoyageCntrCoarse cntr : cntrList) {
                Map<String, Object> row = new HashMap<>();
                // 主表字段
                row.put("mainId", main.getId());
                row.put("bookingId", main.getBookingId());
                row.put("voyageId", main.getVoyageId());
                row.put("voyageNo", voyageNo);
                row.put("shipId", shipId);
                row.put("shipChineseName", shipChineseName);
                row.put("startSeqId", main.getStartSeqId());
                row.put("startTerminalName", startTerminalName);
                row.put("endSeqId", main.getEndSeqId());
                row.put("endTerminalName", endTerminalName);
                // 明细字段
                row.put("cntrCoarseId", cntr.getId());
                row.put("relationBookingVoyageMainId", cntr.getRelationBookingVoyageMainId());
                row.put("cntrSize", cntr.getCntrSize());
                row.put("fullEmptyFlag", cntr.getFullEmptyFlag());
                row.put("quantity", cntr.getQuantity());
                row.put("dangerLevel", cntr.getDangerLevel());
                row.put("isReefer", cntr.getIsReefer());
                resultList.add(row);
            }
        }
        return AjaxResult.success(resultList);
    }
}
