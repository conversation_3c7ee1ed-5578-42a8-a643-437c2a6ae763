package com.ruoyi.plan.domain;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("RELATION_BOOKING_VOYAGE_MAIN")
public class RelationBookingVoyageMain extends BaseEntity {
//    ID
//    BOOKING_ID
//    VOYAGE_ID
//    START_SEQ_ID
//    END_SEQ_ID
//    REMARK
//    CREATE_BY
//    CREATE_TIME
//    UPDATE_BY
//    UPDATE_TIME
//    DEL_FLAG
//    VERSION

    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    private  String bookingId;

    private String voyageId;

    private String startSeqId;

    private String endSeqId;

    /**
     * 逻辑删除标志
     */
    @Column(isLogicDelete = true)
    private String delFlag;

    /**
     * 乐观锁
     */
    @Column("version")
    private Long version;



}
