package com.ruoyi.route.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.route.domain.RouteRelation;
import com.ruoyi.route.mapper.RouteRelationMapper;
import com.ruoyi.route.service.IRouteRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.ruoyi.route.domain.table.RouteMainTableDef.ROUTE_MAIN;
import static com.ruoyi.route.domain.table.RouteRelationTableDef.ROUTE_RELATION;
import static com.ruoyi.route.domain.table.RouteBranchTableDef.ROUTE_BRANCH;
import static com.ruoyi.basic.domain.table.BasicTerminalTableDef.BASIC_TERMINAL;

/**
 * 码头连接关系Service业务层处理
 */
@Slf4j
@Service
public class RouteRelationServiceImpl implements IRouteRelationService {
    @Autowired
    private RouteRelationMapper routeRelationMapper;

    @Override
    public RouteRelationMapper getMapper() {
        return routeRelationMapper;
    }

    /**
     * 分页查询码头连接关系
     */
    @Override
    public Page<RouteRelation> selectRouteRelationPage(int pageNumber, int pageSize, RouteRelation routeRelation) {
        log.debug("分页查询码头连接关系 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, routeRelation);

        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        ROUTE_RELATION.ALL_COLUMNS,
                        ROUTE_BRANCH.BRANCH_NAME.as("branchName"),
                        ROUTE_MAIN.MAIN_NAME.as("mainName"),
                        BASIC_TERMINAL.as("start_terminal").TERMINAL_NAME.as("startPointName"),
                        BASIC_TERMINAL.as("end_terminal").TERMINAL_NAME.as("endPointName")
                )
                .from(ROUTE_RELATION)
                .leftJoin(ROUTE_BRANCH).on(ROUTE_RELATION.BRANCH_ID.eq(ROUTE_BRANCH.BRANCH_ID))
                .leftJoin(ROUTE_MAIN).on(ROUTE_BRANCH.MAIN_ID.eq(ROUTE_MAIN.MAIN_ID))
                .leftJoin(BASIC_TERMINAL.as("start_terminal")).on(ROUTE_RELATION.START_POINT_ID.eq(BASIC_TERMINAL.as("start_terminal").ID))
                .leftJoin(BASIC_TERMINAL.as("end_terminal")).on(ROUTE_RELATION.END_POINT_ID.eq(BASIC_TERMINAL.as("end_terminal").ID))
                .where(ROUTE_BRANCH.MAIN_ID.eq(routeRelation.getMainId(), StringUtils.isNotBlank(routeRelation.getMainId())))
                .and(ROUTE_RELATION.BRANCH_ID.eq(routeRelation.getBranchId(), StringUtils.isNotBlank(routeRelation.getBranchId())))
                .and(ROUTE_RELATION.START_POINT_ID.eq(routeRelation.getStartPointId(), StringUtils.isNotBlank(routeRelation.getStartPointId())))
                .and(ROUTE_RELATION.END_POINT_ID.eq(routeRelation.getEndPointId(), StringUtils.isNotBlank(routeRelation.getEndPointId())))
                .orderBy(ROUTE_RELATION.CREATE_TIME.desc());

        Page<RouteRelation> result = routeRelationMapper.paginate(pageNumber, pageSize, queryWrapper);

        log.debug("分页查询码头连接关系完成 - 总记录数: {}", result.getTotalRow());

        return result;
    }

    /**
     * 查询码头连接关系列表
     */
    @Override
    public List<RouteRelation> selectRouteRelationList(RouteRelation routeRelation) {
        log.debug("查询码头连接关系列表 - 查询条件: {}", routeRelation);
        
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        ROUTE_RELATION.ALL_COLUMNS,
                        ROUTE_BRANCH.BRANCH_NAME.as("branchName"),
                        ROUTE_MAIN.MAIN_NAME.as("mainName"),
                        BASIC_TERMINAL.as("start_terminal").TERMINAL_NAME.as("startPointName"),
                        BASIC_TERMINAL.as("end_terminal").TERMINAL_NAME.as("endPointName")
                )
                .from(ROUTE_RELATION)
                .leftJoin(ROUTE_BRANCH).on(ROUTE_RELATION.BRANCH_ID.eq(ROUTE_BRANCH.BRANCH_ID))
                .leftJoin(ROUTE_MAIN).on(ROUTE_BRANCH.MAIN_ID.eq(ROUTE_MAIN.MAIN_ID))
                .leftJoin(BASIC_TERMINAL.as("start_terminal")).on(ROUTE_RELATION.START_POINT_ID.eq(BASIC_TERMINAL.as("start_terminal").ID))
                .leftJoin(BASIC_TERMINAL.as("end_terminal")).on(ROUTE_RELATION.END_POINT_ID.eq(BASIC_TERMINAL.as("end_terminal").ID))
                .where(ROUTE_BRANCH.MAIN_ID.eq(routeRelation.getMainId(), StringUtils.isNotBlank(routeRelation.getMainId())))
                .and(ROUTE_RELATION.BRANCH_ID.eq(routeRelation.getBranchId(), StringUtils.isNotBlank(routeRelation.getBranchId())))
                .and(ROUTE_RELATION.START_POINT_ID.eq(routeRelation.getStartPointId(), StringUtils.isNotBlank(routeRelation.getStartPointId())))
                .and(ROUTE_RELATION.END_POINT_ID.eq(routeRelation.getEndPointId(), StringUtils.isNotBlank(routeRelation.getEndPointId())))
                .orderBy(ROUTE_RELATION.CREATE_TIME.desc());
        
        return routeRelationMapper.selectListByQuery(queryWrapper);
    }
} 