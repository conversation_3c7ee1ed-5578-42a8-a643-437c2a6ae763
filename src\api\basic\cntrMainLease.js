import request from '@/utils/request'

// 新增租赁箱信息
export function addBasicCntrMainLease(data) {
  return request({
    url: '/system/cntrMainLease/add',
    method: 'post',
    data: data
  })
}

// 分页查询租赁箱列表
export function list(query) {
  return request({
    url: '/system/cntrMainLease/list',
    method: 'get',
    params: query
  })
}

// 标签查询（用于下拉选择等）
export function labelCntrMainLease(data) {
  return request({
    url: '/system/cntrMainLease/label',
    method: 'post',
    data: data
  })
}

// 根据ID查询租赁箱信息
export function getById(id) {
  return request({
    url: '/system/cntrMainLease/getById/' + id,
    method: 'get'
  })
}

// 修改租赁箱信息
export function editBasicCntrMainLease(data) {
  return request({
    url: '/system/cntrMainLease/edit',
    method: 'put',
    data: data
  })
}

// 删除租赁箱信息
export function deleteBasicCntrMainLease(ids) {
  return request({
    url: '/system/cntrMainLease/deleteByIds',
    method: 'delete',
    data: ids
  })
}
