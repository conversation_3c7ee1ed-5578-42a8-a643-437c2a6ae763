package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicAncBerth;
import com.ruoyi.basic.mapper.BasicAncBerthMapper;
import com.ruoyi.basic.service.IBasicAncBerthService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicAncBerthTableDef.BASIC_ANC_BERTH;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicAncBerthServiceImpl extends ServiceImpl<BasicAncBerthMapper, BasicAncBerth> implements IBasicAncBerthService {
    /**
     * 查询锚位管理
     *
     * @param id 锚位管理主键
     * @return 锚位管理
     */
    @Override
    public BasicAncBerth selectBasicAncBerthById(String id)
    {
        return getMapper().selectOneById(id);
    }

    /**
     * 查询锚位管理列表
     *
     * @param basicAncBerth 锚位管理
     * @return 锚位管理
     */
    @Override
    public List<BasicAncBerth> selectBasicAncBerthList(BasicAncBerth basicAncBerth)
    {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_ANC_BERTH.TERMINAL_ID.in(basicAncBerth.getTerminalIds()))
                .and(BASIC_ANC_BERTH.ANB_CODE.like(basicAncBerth.getAnbCode()).when(StringUtils.isNotEmpty(basicAncBerth.getAnbCode())))
                .and(BASIC_ANC_BERTH.ANB_CNAME.like(basicAncBerth.getAnbCname()).when(StringUtils.isNotEmpty(basicAncBerth.getAnbCname())))
                .orderBy(BASIC_ANC_BERTH.CREATE_TIME.asc());
        return getMapper().selectListByQuery(queryWrapper);
    }

    /**
     * 新增锚位管理
     *
     * @param basicAncBerth 锚位管理
     * @return 结果
     */
    @Override
    public int insertBasicAncBerth(BasicAncBerth basicAncBerth)
    {
        return getMapper().insert(basicAncBerth);
    }

    /**
     * 修改锚位管理
     *
     * @param basicAncBerth 锚位管理
     * @return 结果
     */
    @Override
    public int updateBasicAncBerth(BasicAncBerth basicAncBerth)
    {
        return getMapper().update(basicAncBerth);
    }

    /**
     * 批量删除锚位管理
     *
     * @param ids 需要删除的锚位管理主键
     * @return 结果
     */
    @Override
    public int deleteBasicAncBerthByIds(List<String> ids)
    {
        return getMapper().deleteBatchByIds(ids);
    }

    /**
     * 删除锚位管理信息
     *
     * @param id 锚位管理主键
     * @return 结果
     */
    @Override
    public int deleteBasicAncBerthById(String id)
    {
        return getMapper().deleteById(id);
    }
}
