package com.ruoyi.common.utils.password;

import com.alibaba.fastjson2.JSONObject;
import com.dtflys.forest.Forest;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


public class GzgWeakPasswordCheckUtils {
    public static String getGzgWeakPasswordReasons(String password, String username ,String phone) throws IOException {
        List<String> reasons = new ArrayList();
        if (password.length() < 8 || password.length() > 20) {
            reasons.add("密码长度必须在8到20个字符之间");
        }

        boolean hasDigit = password.matches(".*\\d.*");
        boolean hasLowercase = password.matches(".*[a-z].*");
        boolean hasUppercase = password.matches(".*[A-Z].*");
        boolean hasSpecialChar = password.matches(".*[!@#$%^&*+=:;<>,.?/].*");
        int count = (hasDigit ? 1 : 0) + (hasLowercase ? 1 : 0) + (hasUppercase ? 1 : 0) + (hasSpecialChar ? 1 : 0);
        if (count < 3) {
            reasons.add("密码至少需要包含数字、小写字母、大写字母、特殊字符中的三项");
        }

        if(!"ok".equals(checkRepeat(password))){
            reasons.add("不能连续3字母或数字");
        }

        if(!"ok".equals(check3(password))){
            reasons.add("包含三个或者三个以上相同");
        }

        if(!"ok".equals(validateKey(password))) {
            reasons.add("不得包含键盘上任意连续三个或者三个以上字符");
        }

        String[] bannedWords = new String[]{
                "cloud", "CLoUD", "password", "PASSWORD", "p@ssword", "gzport" , "com" , "gzp",
                "!qaz2wsx", "1.11111E+11", "1.12233E+11", "1.23123E+11", "1.23321E+11", "1.23456E+11", "1.23457E+11", "1.23457E+17",
                "1.23457E+19", "18atcskD2W", "1Fr2rfq7xL", "1qaz!QAZ", "1qaz!qaz", "1qaz@wsx", "1v7Upjw3nT", "1zn6FpN01x",
                "3Odi15ngxB", "3d8Cubaj2E", "3rJs1la2qE", "3rJs1la7qE", "3rJs5la8qE", "5X1CJdsb9p", "5hsU75kpoT", "5plK4L5Uc7",
                "6V21wbgad", "7uGd5HIp2J", "8PHroWZ624", "8ix6S1fceH", "AKAX89Wn", "Aa123456", "Abcd1234", "Blink123",
                "CM6E7Aumn9", "Charlie1", "D1lakiss", "Eh1K9oh335", "Groupd2013", "H2vWDuBjX4", "J1V1fp2BXm", "Letmein1",
                "Linkedin1", "MaprCheM56458", "Megaparol12345", "Michael1", "OcPOOok325", "P3Rat54797", "P@ssw0rd", "PE#5GZ29PTZMSE",
                "Pa55word", "Parola12", "Passw0rd", "Password01", "Password1", "Password123", "PolniyPizdec0211", "PolniyPizdec1102",
                "PolniyPizdec110211", "Qwerty123", "SZ9kQcCTwY", "Sample123", "Sojdlg123aljg", "Tnk0Mk16VX", "W1aUbvOQ", "W5tXn36alfW",
                "W5tn36alfW", "Welcome1", "X3LUym2MMJ", "XBLhInTB9w", "YAgjecc826", "YfDbUfNjH10305070", "a838hfiD", "asdfghjkl;&#39;",
                "c43qpul5RZ", "chivas#1", "couponSC10", "d71lWz9zjS", "d9Zufqd92N", "dIWtgm8492", "dfg5Fhg5VGFh1", "fxzZ75yer",
                "g9l2d1fzPY", "hgrFQg4577", "iG4abOX4", "iloveyou<3", "iw14Fi9j", "iw14Fi9jwQa", "iw14Fi9jxL", "j38ifUbn",
                "jG3h4HFn", "john!20130605at1753", "ka_dJKHJsy6", "mV46VkMz10", "owt243yGbJ", "p@ssw0rd", "pk3x7w9W", "qdujvyG5sxa",
                "qti7Zxh18U", "rbOTmvZ954", "s8YLPe9jDPvYM", "scvMOFAS79", "uQA9Ebw445", "vRbGQnS997", "w66YRyBgRa", "wsbe279qSG",
                "x4ivygA51F","y6p67FtrqJ"
        };

        String[] keyboardPatterns = bannedWords;
        int var10 = bannedWords.length;

        int var11;
        for (var11 = 0; var11 < var10; ++var11) {
            String bannedWord = keyboardPatterns[var11];
            if (password.equalsIgnoreCase(bannedWord) || password.toLowerCase().contains(bannedWord.toLowerCase())) {
                reasons.add("密码包含禁止的单词：" + bannedWord);
            }
        }

        String content = new String(new ClassPathResource("words_dictionary.json")
                .getInputStream().readAllBytes());

        JSONObject jsonObject = JSONObject.parseObject(content);

        Set<String> set = jsonObject.keySet();

        for(String str:set){

            if(str.length() >=5 && str.length() <= 10){

                if (password.equalsIgnoreCase(str) || password.toLowerCase().contains(str.toLowerCase())) {
                    reasons.add("密码包含禁止的单词：" + str);
                    break;
                }

            }

        }

        keyboardPatterns = new String[]{
                "abc", "cba", "qaz", "qwe", "abc123", "a1b2c3",
                "147", "369", "258", "741", "852", "963", "123", "456", "789",
                "321", "654", "987", "wer", "ert", "rty", "tyu", "yui", "uio", "iop", "op[", "p[]", "[]\\",
                "asd", "sdf", "dfg", "fgh", "ghj", "hjk", "jkl", "kl;", "l;'",
                "zxc", "xcv", "cvb", "vbn", "bnm", "nm,", "m,.", ",./",
                "qaz", "wsx", "edc", "rfv", "tgb", "yhn", "ujm", "ik,", "ol.", "p;/",
                "ewq", "rew", "tre", "ytr", "uyt", "iuy", "oiu", "poi", "[po", "][p", "\\][",
                "';l", ";lk", "lkj", "kjh", "jhg", "hgf", "gfd", "fds", "dsa",
                "/.,", ".,m", ",mn", "mnb", "nbv", "bvc", "vcx", "cxz",
                "`12", "234", "345", "567", "678", "890", "90-", "0-=",
                "=-0", "-09", "098", "876", "765", "543", "432", "21`",
                "~!@", "!@#", "@#$", "#$%", "$%^", "%^&", "^&*", "&*(", "*()", "()_", ")_+",
                "+_)", "_)(", ")(*", "(*&", "*&^", "&^%", "^%$", "%$#", "#$@", "@!~"
        };
        String[] var14 = keyboardPatterns;
        var11 = keyboardPatterns.length;

        for (int var15 = 0; var15 < var11; ++var15) {
            String pattern = var14[var15];
            if (password.equalsIgnoreCase(pattern) || password.matches(".*" + Pattern.quote(pattern) + ".*")) {
                reasons.add("密码包含连续键位字母序列：" + pattern);
            }
        }


        if (password.contains(username) || password.matches(".*" + Pattern.quote(username) + ".*") || containsLongConsecutiveSubstring(username,password,3)) {
            reasons.add("密码包含用户名或用户名中连续的部分内容");
        }


        if(StringUtils.isNotEmpty(phone)){
            if(password.contains(phone) || password.matches(".*" + Pattern.quote(phone) + ".*") || containsLongConsecutiveSubstring(phone,password,4)){
                reasons.add("密码包含手机号或手机号中连续的部分内容");
            }
        }

        //现在需要一个list,生成从1900年到2099年的所有日期
        //然后检查密码是否包含这些日期
        //如果包含，则添加到reasons中
        List<String> allDates = new ArrayList<>();

        for (int year = 1900; year <= 2099; year++) {
            for (int month = 1; month <= 12; month++) {
                int daysInMonth = getDaysInMonth(year, month);

                for (int day = 1; day <= daysInMonth; day++) {
                    // 格式化为YYYY-MM-DD并添加到列表
                    String dateStr = String.format("%04d-%02d-%02d", year, month, day);

                    Date date = DateUtils.parseDate(dateStr);

                    allDates.add(DateUtils.parseDateToStr("yyyyMMdd",date));
                }
            }
        }

        for (String dateStr: allDates
        ) {
            if(password.contains(dateStr) || password.matches(".*" + Pattern.quote(dateStr) + ".*") || containsLongConsecutiveSubstring(dateStr,password,4)){
                reasons.add("密码包含年份/日期中连续的部分内容");
                break;
            }
        }

        if (reasons.isEmpty()) {
            return "密码强度足够";
        } else {
            return String.join("; ", reasons);
        }

    }

    /**
     * 转码
     *
     * @param c 字符
     * @return 编码
     */
    private static int getUnicode(char c) {
        String returnUniCode = null;
        returnUniCode = String.valueOf((int) c);
        return Integer.parseInt(returnUniCode);
    }

    /***
     * 描述: 三个或者三个以上相同
     *
     * @param pwd 密码
     * @return String
     */
    public static String check3(String pwd) {

        String regx = "^.*(.)\\1{2}.*$";
        Matcher m = null;
        Pattern p = null;
        p = Pattern.compile(regx);
        m = p.matcher(pwd);
        if (m.matches()) {
            return "包含三个或者三个以上相同";
        } else {
            return "ok";

        }

    }

    /***
     * 描述: 密码不得包含键盘上任意连续的三个字符或shift转换字符
     *
     * @param str 字符串
     * @return String
     */
    public static String validateKey(String str) {

        //定义横向穷举
        String[][] keyCode = {
                {"`~·", "1=", "2@@", "3#", "4$￥", "5%", "6^……", "7&", "8*", "9(（", "0）)", "-_", "=+"},
                {" ", "qQ", "wW", "eE", "rR", "tT", "yY", "uU", "iI", "oO", "pP", "[{【", "]}】", "\\|、"},
                {" ", "aA", "sS", "dD", "fF", "gG", "hH", "jJ", "kK", "lL", ";:", "\'\"’“"},
                {" ", "zZ", "xX", "cC", "vV", "bB", "nN", "mM", ",《<", ".>》", "/?？"}
        };

        //找出给出的字符串，每个字符，在坐标系中的位置。
        char[] c = str.toCharArray();
        List<Integer> x = new ArrayList<Integer>();
        List<Integer> y = new ArrayList<Integer>();
        for (int i = 0; i < c.length; i++) {
            char temp = c[i];
            toHere:
            for (int j = 0; j < keyCode.length; j++) {
                for (int k = 0; k < keyCode[j].length; k++) {
                    String jk = keyCode[j][k];
                    if (jk.contains(String.valueOf(temp))) {
                        x.add(j);
                        y.add(k);
                        break toHere;
                    }
                }
            }
        }
        boolean flag = false;
        for (int i = 0; i < x.size() - 2; i++) {
            // 如果X一致，那么就是在一排
            if (x.get(i) == x.get(i + 1) && x.get(i + 1) == x.get(i + 2)) {//四者在同一行上
                if (y.get(i) > y.get(i + 2)) {
                    if (y.get(i) - 1 == y.get(i + 1) && y.get(i) - 2 == y.get(i + 2)) {
                        flag = true;
                        break;
                    }
                } else {
                    if (y.get(i) + 1 == y.get(i + 1) && y.get(i) + 2 == y.get(i + 2)) {
                        flag = true;
                        break;
                    }
                }

            } else if (x.get(i) != x.get(i + 1)
                    && x.get(i + 1) != x.get(i + 2)
                    && x.get(i) != x.get(i + 2)
            ) {//四者均不在同一行上,但是如果y相同，说明是一列
                if (y.get(i) == y.get(i + 1) && y.get(i + 1) == y.get(i + 2)) {
                    flag = true;
                    break;
                }
            }

        }
        if (flag) {
            return "不得包含键盘上任意连续三个或者三个以上字符";

        } else {
            return "ok";
        }
    }

    /***
     * 描述: 不能连续字符（如123、abc）连续3位或3位以上
     *
     * @param str 字符串
     * @return String
     */
    public static String checkRepeat(String str) {
        String[] arr = str.split("");
        boolean flag = false;
        for (int i = 1; i < arr.length - 1; i++) {
            int firstIndex = getUnicode(arr[i - 1].charAt(0));
            int secondIndex = getUnicode(arr[i].charAt(0));
            int thirdIndex = getUnicode(arr[i + 1].charAt(0));
            if ((thirdIndex - secondIndex == 1) && (secondIndex - firstIndex == 1)) {
                flag = true;
            }
        }
        if (flag) {
            return "不能连续3字母或数字";

        } else {
            return "ok";
        }
    }

    private static int getDaysInMonth(int year, int month) {
        return switch (month) {
            case 4, 6, 9, 11 -> 30;  // 4月、6月、9月、11月
            case 2 -> isLeapYear(year) ? 29 : 28;  // 2月（闰年检查）
            default -> 31;  // 其他月份（1,3,5,7,8,10,12）
        };
    }

    private static boolean isLeapYear(int year) {
        // 闰年规则：能被4整除但不能被100整除，或者能被400整除
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    public static boolean containsLongConsecutiveSubstring(String A, String B, Integer length) {
        int lengthA = A.length();

        // 遍历A的所有可能的连续子字符串（长度至少为3）
        for (int i = 0; i <= lengthA - length; i++) {
            String substring = A.substring(i, i + 3); // 获取长度为3的子字符串
            // 如果子字符串长度小于A的剩余部分，则继续增加长度并检查
            for (int j = length; j <= lengthA - i; j++) {
                substring = A.substring(i, i + j); // 更新子字符串为更长的部分
                if (B.contains(substring)) {
                    return true; // 如果B包含这个子字符串，则返回true
                }
            }
            // 注意：这里的内层循环其实是不必要的，因为一旦找到长度为3的匹配，
            // 后面的更长匹配就已经被包含了。但为了保持逻辑的完整性，我还是保留了它。
            // 在实际应用中，你可以只检查长度为3的子字符串，或者根据需要调整检查的长度。
        }

        // 如果没有找到任何匹配，则返回false
        return false;
    }

    public static void main(String[] args) throws IOException {


        ForestRequest<?> request = Forest.post("http://10.201.16.152:6041/rest/sql");

        request.setContentType("text/plain");
        request.addHeader("Authorization", "Basic cm9vdDpHenRvc0AyMDI0");

        request.addBody("select pdw from weekpdw.weekpdwlist order by pdw asc");

        ForestResponse<String> response = request.executeAsResponse();

        String json = response.getResult();

        PasswordRes passwordRes = JSONObject.parseObject(json, PasswordRes.class);

        List<List<String>> password = passwordRes.getData();

        List<String> passwordList = new ArrayList<>();

        List<String> weakPasswordList = new ArrayList<>();

        for (List<String> strings : password) {

            for (String str:
                    strings ) {
                if (str.length() >= 8 && str.length() <= 20) {
//                    reasons.add("密码长度必须在8到20个字符之间");

                    boolean hasDigit = str.matches(".*\\d.*");
                    boolean hasLowercase = str.matches(".*[a-z].*");
                    boolean hasUppercase = str.matches(".*[A-Z].*");
                    boolean hasSpecialChar = str.matches(".*[!@#$%^&*+=:;<>,.?/].*");
                    int count = (hasDigit ? 1 : 0) + (hasLowercase ? 1 : 0) + (hasUppercase ? 1 : 0) + (hasSpecialChar ? 1 : 0);
                    if (count >= 3) {
//                    reasons.add("密码至少需要包含数字、小写字母、大写字母、特殊字符中的三项");
                        passwordList.add(str);
                    }

                }

                if(str.length() >= 8 ){

                    boolean hasDigit = str.matches(".*\\d.*");
                    boolean hasLowercase = str.matches(".*[a-z].*");
                    boolean hasUppercase = str.matches(".*[A-Z].*");
                    boolean hasSpecialChar = str.matches(".*[!@#$%^&*+=:;<>,.?/].*");
                    int count = (hasDigit ? 1 : 0) + (hasLowercase ? 1 : 0) + (hasUppercase ? 1 : 0) + (hasSpecialChar ? 1 : 0);
                    if (count < 3) {
//                    reasons.add("密码至少需要包含数字、小写字母、大写字母、特殊字符中的三项");
                        weakPasswordList.add(str);
                    }

                }


            }

        }

        passwordList = passwordList.stream().distinct().collect(Collectors.toList());

        weakPasswordList = weakPasswordList.stream().distinct().collect(Collectors.toList());

        System.out.println(passwordList);
        System.out.println(passwordList.size());

        System.out.println(weakPasswordList);
        System.out.println(weakPasswordList.size());



    }

}
