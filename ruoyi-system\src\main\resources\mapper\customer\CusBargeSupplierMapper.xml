<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.customer.mapper.CusBargeSupplierMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.customer.domain.CusBargeSupplier">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CUS_MAIN_ID" jdbcType="VARCHAR" property="cusMainId"/>
        <result column="ROUTE_NAME" jdbcType="VARCHAR" property="routeName"/>
        <result column="BARGE_SUPPLIER_NAME" jdbcType="VARCHAR" property="bargeSupplierName"/>
    </resultMap>
    <sql id="Base_Column_List">
        `ID`, `CUS_MAIN_ID`, `ROUTE_NAME`, `BARGE_SUPPLIER_NAME`
    </sql>

</mapper>