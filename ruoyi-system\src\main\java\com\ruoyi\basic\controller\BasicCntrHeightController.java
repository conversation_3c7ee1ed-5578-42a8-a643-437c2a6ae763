package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCntrHeight;
import com.ruoyi.basic.service.IBasicCntrHeightService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 箱高Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/system/cntrHeight")
public class BasicCntrHeightController extends BaseController {

    @Autowired
    private IBasicCntrHeightService basicCntrHeightService;

    /**
     * 查询箱高列表
     */
    @PreAuthorize("@ss.hasPermi('system:height:list')")
    @GetMapping("/list")
    public Page<BasicCntrHeight> list(BasicCntrHeight basicCntrHeight)
    {
        // startPage();
        // List<BasicCntrHeight> list = basicCntrHeightService.selectBasicCntrHeightList(basicCntrHeight);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create()
        .like(BasicCntrHeight::getHeightCode, basicCntrHeight.getHeightCode())
        .like(BasicCntrHeight::getHeightName, basicCntrHeight.getHeightName())
        .orderBy(BasicCntrHeight::getCreateTime, true);
        return basicCntrHeightService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出箱高列表
     */
    @PreAuthorize("@ss.hasPermi('system:height:export')")
    @Log(title = "箱高", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicCntrHeight basicCntrHeight)
    {
        List<BasicCntrHeight> list = basicCntrHeightService.selectBasicCntrHeightList(basicCntrHeight);
        ExcelUtil<BasicCntrHeight> util = new ExcelUtil<BasicCntrHeight>(BasicCntrHeight.class);
        util.exportExcel(response, list, "箱高数据");
    }

    /**
     * 获取箱高详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:height:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicCntrHeightService.selectBasicCntrHeightById(id));
    }

    /**
     * 新增箱高
     */
    @PreAuthorize("@ss.hasPermi('system:height:add')")
    @Log(title = "箱高", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicCntrHeight basicCntrHeight)
    {
        return toAjax(basicCntrHeightService.insertBasicCntrHeight(basicCntrHeight));
    }

    /**
     * 修改箱高
     */
    @PreAuthorize("@ss.hasPermi('system:height:edit')")
    @Log(title = "箱高", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicCntrHeight basicCntrHeight)
    {
        return toAjax(basicCntrHeightService.updateBasicCntrHeight(basicCntrHeight));
    }

    /**
     * 删除箱高
     */
    @PreAuthorize("@ss.hasPermi('system:height:remove')")
    @Log(title = "箱高", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicCntrHeightService.deleteBasicCntrHeightByIds(ids));
    }

}
