package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicArea;
import com.ruoyi.basic.domain.BasicPort;
import com.ruoyi.basic.domain.BasicPortArea;
import com.ruoyi.basic.mapper.BasicPortMapper;
import com.ruoyi.basic.service.IBasicAreaService;
import com.ruoyi.basic.service.IBasicCountryService;
import com.ruoyi.basic.service.IBasicPortAreaService;
import com.ruoyi.basic.service.IBasicPortService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.ruoyi.basic.domain.table.BasicPortAreaTableDef.BASIC_PORT_AREA;
import static com.ruoyi.basic.domain.table.BasicPortTableDef.BASIC_PORT;

@Service
public class BasicPortServiceImpl extends ServiceImpl<BasicPortMapper, BasicPort> implements IBasicPortService {

    @Autowired
    IBasicAreaService areaService;

    @Autowired
    IBasicCountryService countryService;

    /**
     * 查询港口管理
     *
     * @param id 港口管理主键
     * @return 港口管理
     */
    @Override
    public BasicPort selectBasicPortById(String id)
    {
        return super.getById(id);
    }

    /**
     * 查询港口管理列表
     *
     * @param basicPort 港口管理
     * @return 港口管理
     */
    @Override
    public List<BasicPort> selectBasicPortList(BasicPort basicPort)
    {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_PORT.AREA_ID.eq(basicPort.getAreaId(), StringUtils.isNotEmpty(basicPort.getAreaId())))
                .and(BASIC_PORT.PORT_CODE.like(basicPort.getPortCode(), StringUtils.isNotEmpty(basicPort.getPortCode())))
                .and(BASIC_PORT.PORT_NAME.like(basicPort.getPortName(), StringUtils.isNotEmpty(basicPort.getPortName())))
                .orderBy(BASIC_PORT.CREATE_TIME.asc());

        return super.list(queryWrapper);
    }

    /**
     * 新增港口管理
     *
     * @param basicPort 港口管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBasicPort(BasicPort basicPort) throws Exception {
        checkValid(basicPort);
        checkUnique(basicPort);
        return super.save(basicPort) ? 1 : 0;
    }

    /**
     * 修改港口管理
     *
     * @param basicPort 港口管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBasicPort(BasicPort basicPort) throws Exception {
        checkValid(basicPort);
        checkUnique(basicPort);
        return super.updateById(basicPort) ? 1 : 0;
    }

    /**
     * 批量删除港口管理
     *
     * @param ids 需要删除的港口管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBasicPortByIds(List<String> ids) throws Exception {
        checkUse(ids);
        return super.removeByIds(ids) ? 1 : 0;
    }

    /**
     * 删除港口管理信息
     *
     * @param id 港口管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBasicPortById(String id)
    {
        return super.removeById(id) ? 1 : 0;
    }

    @Autowired
    IBasicPortAreaService portAreaService;

    public void checkUse(List<String> ids) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_PORT_AREA.PORT_ID.in(ids));

        List<BasicPortArea> checkUse = portAreaService.list(queryWrapper);

        if(!checkUse.isEmpty()){
            throw new Exception("选择港口正在被使用");
        }
    }

    public void checkUnique(BasicPort basicPort) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_PORT.ID.ne(basicPort.getId(), StringUtils.isNotEmpty(basicPort.getId())))
//                .and(BASIC_PORT.AREA_ID.eq(basicPort.getAreaId()))
                .and(BASIC_PORT.PORT_CODE.eq(basicPort.getPortCode()));

        List<BasicPort> checkCode = super.list(queryWrapper);

        if(!checkCode.isEmpty()){
            throw new Exception("港口代码 唯一校验失败");
        }

        queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_PORT.ID.ne(basicPort.getId(), StringUtils.isNotEmpty(basicPort.getId())))
//                .and(BASIC_PORT.AREA_ID.eq(basicPort.getAreaId()))
                .and(BASIC_PORT.PORT_NAME.eq(basicPort.getPortName()));

        List<BasicPort> checkName = super.list(queryWrapper);

        if(!checkName.isEmpty()){
            throw new Exception("港口名称 唯一校验失败");
        }
    }

    public void checkValid(BasicPort basicPort) throws Exception {
//        if(StringUtils.isEmpty(basicPort.getAreaId())){
//            throw new Exception("请选择区域");
//        }

        if(StringUtils.isEmpty(basicPort.getPortCode())){
            throw new Exception("港口代码不能为空");
        }

        if(StringUtils.isEmpty(basicPort.getPortName())){
            throw new Exception("港口名称不能为空");
        }
    }
}
