package com.ruoyi.customer.domain;

import lombok.Data;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

/**
 * 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@Table(value = "CUS_TYPE")
public class CusType {

    /**
     * 客户表主键
     */
    @Id
    private String cusMainId;

    /**
     * 客户类型
     */
    @Id
    private String cusType;


}
