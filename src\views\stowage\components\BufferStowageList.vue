<template>
  <div class="buffer-stowage-list">
    <!-- 缓冲区列表 -->
    <div class="buffer-zones">
      <div
        v-for="zone in stowageStore.bufferZones"
        :key="zone.id"
        class="buffer-zone"
        :class="{ 'drag-over': dragOverZone === zone.id }"
        :style="{
          backgroundColor: zone.color,
          borderColor: zone.borderColor,
        }"
        @dragover.prevent="handleDragOver(zone.id)"
        @dragleave="handleDragLeave"
        @drop="handleDrop(zone.id, $event)"
      >
        <!-- 缓冲区标题 -->
        <div class="buffer-header">
          <h4>{{ zone.name }}</h4>
          <div class="buffer-stats">
            <el-tag size="small" :color="zone.borderColor" style="color: white">
              {{ zone.items.length }} 项
            </el-tag>
            <el-tag size="small" type="info">
              {{ calculateZoneTEU(zone) }} TEU
            </el-tag>
          </div>
          <div class="buffer-actions">
            <el-button
              size="small"
              type="primary"
              @click="optimizeBuffer(zone.id)"
              :disabled="zone.items.length === 0"
            >
              <el-icon><MagicStick /></el-icon>
              优化
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="clearBuffer(zone.id)"
              :disabled="zone.items.length === 0"
            >
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>

        <!-- 缓冲区统计信息 -->
        <div class="buffer-statistics" v-if="zone.items.length > 0">
          <div class="stat-row">
            <span class="stat-label">箱量统计：</span>
            <div class="stat-values">
              <span class="stat-item"
                >20空: {{ zone.statistics.containers["20Empty"] }}</span
              >
              <span class="stat-item"
                >20重: {{ zone.statistics.containers["20Full"] }}</span
              >
              <span class="stat-item"
                >40空: {{ zone.statistics.containers["40Empty"] }}</span
              >
              <span class="stat-item"
                >40重: {{ zone.statistics.containers["40Full"] }}</span
              >
              <span class="stat-item"
                >其他: {{ zone.statistics.containers["other"] }}</span
              >
            </div>
          </div>
        </div>

        <!-- 缓冲区内容 -->
        <div class="buffer-content">
          <div v-if="zone.items.length === 0" class="empty-zone drop-zone">
            <el-empty description="缓冲区为空" :image-size="80">
              <template #description>
                <p>将货物拖拽到此处</p>
                <p class="drop-hint">支持拖拽操作</p>
              </template>
            </el-empty>
          </div>
          <div v-else class="zone-items">
            <div
              v-for="item in zone.items"
              :key="item.id"
              class="buffer-item"
              :style="{ backgroundColor: lightenColor(zone.color, 0.3) }"
            >
              <div class="item-header">
                <div class="item-title">
                  <span
                    class="booking-no"
                    draggable="true"
                    @dragstart="handleItemDragStart(item, $event)"
                    @dragend="handleItemDragEnd"
                  >
                    <el-icon class="drag-handle"><Rank /></el-icon>
                    {{ item.logisticsMainId }} - {{ item.sequenceNo }}
                  </span>
                </div>
                <div class="item-actions">
                  <el-button
                    type="text"
                    size="small"
                    @click="viewItemDetail(item)"
                  >
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="removeFromBuffer(zone.id, item.id)"
                    style="color: #f56565"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="item-content">
                <div class="item-row">
                  <span class="item-label">客户：</span>
                  <span
                    class="item-value"
                    :title="item.shipperName"
                    style="
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    "
                    >{{ item.shipperName }}</span
                  >
                  <span class="item-label" style="margin-left: 16px"
                    >途经码头：</span
                  >
                  <span
                    class="item-value"
                    :title="`${item.loadingTerminalName} → ${item.unloadingTerminalName}`"
                    style="
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    "
                    >{{ item.loadingTerminalName }} →
                    {{ item.unloadingTerminalName }}</span
                  >
                </div>
                <div class="item-row">
                  <span class="item-label">箱量：</span>
                  <div class="container-summary">
                    <span v-if="item.containers['20Empty'] > 0"
                      >20空×{{ item.containers["20Empty"] }}</span
                    >
                    <span v-if="item.containers['20Full'] > 0"
                      >20重×{{ item.containers["20Full"] }}</span
                    >
                    <span v-if="item.containers['40Empty'] > 0"
                      >40空×{{ item.containers["40Empty"] }}</span
                    >
                    <span v-if="item.containers['40Full'] > 0"
                      >40重×{{ item.containers["40Full"] }}</span
                    >
                    <span v-if="item.containers['other'] > 0"
                      >其他×{{ item.containers["other"] }}</span
                    >
                  </div>
                  <span class="item-label" style="margin-left: 16px"
                    >TEU：</span
                  >
                  <el-tag type="info" size="small">{{
                    calculateTEU(item.containers)
                  }}</el-tag>
                </div>
                <div class="item-row" v-if="item.remark">
                  <span class="item-label">备注：</span>
                  <span class="item-value">{{ item.remark }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 缓冲区底部操作 -->
        <div class="buffer-footer" v-if="zone.items.length > 0">
          <div class="footer-stats">
            <span v-if="!stowageStore.selectedVessel" class="vessel-hint">
              <el-icon><InfoFilled /></el-icon>
              请先在船舶清单中选择一艘船舶
            </span>
          </div>
          <div class="footer-actions">
            <el-button
              size="small"
              type="success"
              @click="bindToVessel(zone.id)"
              :disabled="!stowageStore.selectedVessel"
              :title="
                !stowageStore.selectedVessel
                  ? '请先在船舶清单中选择一艘船舶'
                  : `配载到 ${stowageStore.selectedVessel.vesselName}`
              "
            >
              <el-icon><Ship /></el-icon>
              {{
                stowageStore.selectedVessel
                  ? `配载到 ${stowageStore.selectedVessel.vesselName}`
                  : "配载到船舶"
              }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 货物详情对话框 -->
    <el-dialog
      v-model="itemDetailVisible"
      :title="`货物详情: ${selectedItem?.logisticsMainId || ''}-${selectedItem?.sequenceNo || ''}`"
      width="300px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedItem" class="item-detail-content">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">运输环节号:</span>
            <span class="detail-value">{{ selectedItem.logisticsMainId }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">客户:</span>
            <span class="detail-value">{{ selectedItem.shipperName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">途经码头:</span>
            <span class="detail-value"
              >{{ selectedItem.loadingTerminalName }} →
              {{ selectedItem.unloadingTerminalName }}</span
            >
          </div>
          <div class="detail-item">
            <span class="detail-label">内外贸:</span>
            <span class="detail-value">{{ selectedItem.tradeType }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">项目:</span>
            <span class="detail-value">{{ selectedItem.project }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">航线:</span>
            <span class="detail-value">{{ selectedItem.routeName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">TEU:</span>
            <span class="detail-value">{{
              calculateTEU(selectedItem.containers)
            }}</span>
          </div>
        </div>

        <div class="container-details">
          <h4>集装箱详情</h4>
          <div class="container-grid">
            <div class="container-item">
              <span class="container-label">20尺空:</span>
              <span class="container-value">{{
                selectedItem.containers["20Empty"]
              }}</span>
            </div>
            <div class="container-item">
              <span class="container-label">20尺重:</span>
              <span class="container-value">{{
                selectedItem.containers["20Full"]
              }}</span>
            </div>
            <div class="container-item">
              <span class="container-label">40尺空:</span>
              <span class="container-value">{{
                selectedItem.containers["40Empty"]
              }}</span>
            </div>
            <div class="container-item">
              <span class="container-label">40尺重:</span>
              <span class="container-value">{{
                selectedItem.containers["40Full"]
              }}</span>
            </div>
            <div class="container-item">
              <span class="container-label">其他箱:</span>
              <span class="container-value">{{
                selectedItem.containers["other"]
              }}</span>
            </div>
          </div>
        </div>

        <div class="remark-section" v-if="selectedItem.remark">
          <h4>备注信息</h4>
          <p>{{ selectedItem.remark }}</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="itemDetailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  InfoFilled,
  Plus,
  MagicStick,
  Delete,
  View,
  Close,
  Ship,
  Rank,
} from "@element-plus/icons-vue";
import { useStowageStore } from "@/stores/stowage";
import { calculateTEU } from "../mockData.js";

const stowageStore = useStowageStore();

// 界面状态
const itemDetailVisible = ref(false);
const selectedItem = ref(null);
const dragOverZone = ref(null);
const draggingItem = ref(null);

// 计算缓冲区TEU总数
const calculateZoneTEU = (zone) => {
  return zone.items.reduce((total, item) => {
    return total + calculateTEU(item.containers);
  }, 0);
};

// 颜色处理函数
const lightenColor = (color, percent) => {
  // 简单的颜色淡化处理
  const hex = color.replace("#", "");
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const lightenedR = Math.round(r + (255 - r) * percent);
  const lightenedG = Math.round(g + (255 - g) * percent);
  const lightenedB = Math.round(b + (255 - b) * percent);

  return `#${lightenedR.toString(16).padStart(2, "0")}${lightenedG
    .toString(16)
    .padStart(2, "0")}${lightenedB.toString(16).padStart(2, "0")}`;
};

// 从缓冲区移除
const removeFromBuffer = (zoneId, itemId) => {
  if (stowageStore.removeFromBuffer(itemId, zoneId)) {
    ElMessage.success("已从缓冲区移除");
  } else {
    ElMessage.error("移除失败");
  }
};

// 清空单个缓冲区
const clearBuffer = (zoneId) => {
  const zone = stowageStore.bufferZones.find((z) => z.id === zoneId);
  if (!zone || zone.items.length === 0) return;

  ElMessageBox.confirm(
    `确定要清空${zone.name}吗？这将移除所有 ${zone.items.length} 项货物。`,
    "确认清空",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      if (stowageStore.clearBuffer(zoneId)) {
        ElMessage.success(`${zone.name}已清空`);
        // 添加以下代码来清除待运输货物列表中的选中状态
        stowageStore.clearSelection();
      } else {
        ElMessage.error("清空失败");
      }
    })
    .catch(() => {
      // 用户取消
    });
};

// 清空所有缓冲区
const clearAllBuffers = () => {
  if (stowageStore.bufferTotalItems === 0) {
    ElMessage.info("所有缓冲区均为空");
    return;
  }

  ElMessageBox.confirm(
    `确定要清空所有缓冲区吗？这将移除总共 ${stowageStore.bufferTotalItems} 项货物。`,
    "确认清空全部",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      let successCount = 0;
      stowageStore.bufferZones.forEach((zone) => {
        if (zone.items.length > 0 && stowageStore.clearBuffer(zone.id)) {
          successCount++;
        }
      });
      if (successCount > 0) {
        ElMessage.success(`已清空 ${successCount} 个缓冲区`);
        // 添加以下代码来清除待运输货物列表中的选中状态
        stowageStore.clearSelection();
      }
    })
    .catch(() => {
      // 用户取消
    });
};

// 优化缓冲区
const optimizeBuffer = (zoneId) => {
  ElMessage.info("优化功能开发中...");
  // TODO: 实现缓冲区优化逻辑
};

// 查看货物详情
const viewItemDetail = (item) => {
  selectedItem.value = item;
  itemDetailVisible.value = true;
};

// 绑定到船舶
const bindToVessel = (zoneId) => {
  if (!stowageStore.selectedVessel) {
    ElMessage.warning("请先选择一艘船舶");
    return;
  }

  const zone = stowageStore.bufferZones.find((z) => z.id === zoneId);
  if (!zone || zone.items.length === 0) {
    ElMessage.warning("缓冲区为空，无法配载");
    return;
  }

  const voyage = stowageStore.bindBufferToVessel(
    zoneId,
    stowageStore.selectedVessel.id
  );
  if (voyage) {
    ElMessage.success(
      `${zone.name}已成功配载到${stowageStore.selectedVessel.vesselName}，生成航次${voyage.voyageNo}`
    );
    // 添加以下代码来清除待运输货物列表中的选中状态
    stowageStore.clearSelection();
  } else {
    ElMessage.error("配载失败");
  }
};

// 处理拖拽进入
const handleDragOver = (zoneId) => {
  dragOverZone.value = zoneId;
};

// 处理拖拽离开
const handleDragLeave = () => {
  dragOverZone.value = null;
};

// 处理放下
const handleDrop = (zoneId, event) => {
  event.preventDefault();
  dragOverZone.value = null;

  try {
    const data = event.dataTransfer.getData("application/json");
    if (data) {
      const dropData = JSON.parse(data);
      const itemIdentifier = `${dropData.item.logisticsMainId}-${dropData.item.sequenceNo}`;
      if (dropData.type === "cargo") {
        // 从待运输列表拖拽到缓冲区
        if (stowageStore.addToBuffer([dropData.item], zoneId)) {
          ElMessage.success(`已将 ${itemIdentifier} 添加到缓冲区${zoneId}`);
        }
      } else if (dropData.type === "buffer-item") {
        // 在缓冲区之间移动
        const fromZoneId = dropData.fromZoneId;
        if (fromZoneId !== zoneId) {
          if (
            stowageStore.moveItemBetweenBuffers(
              dropData.item.id,
              fromZoneId,
              zoneId
            )
          ) {
            ElMessage.success(
              `已将 ${itemIdentifier} 从缓冲区${fromZoneId}移动到缓冲区${zoneId}`
            );
          }
        }
      }
    }
  } catch (error) {
    console.error("拖拽数据解析失败:", error);
    ElMessage.error("拖拽操作失败");
  }
};

// 处理缓冲区项拖拽开始
const handleItemDragStart = (item, event) => {
  draggingItem.value = item;
  const dragData = {
    type: "buffer-item",
    item: item,
    fromZoneId: item.bufferZoneId,
  };
  event.dataTransfer.setData("application/json", JSON.stringify(dragData));
  event.dataTransfer.effectAllowed = "move";
};

// 处理缓冲区项拖拽结束
const handleItemDragEnd = () => {
  draggingItem.value = null;
};

// 获取去重后的码头列表
const getUniqueTerminals = (zone) => {
  const allTerminals = new Set();

  // 添加所有起运港和目的港
  zone.statistics.loadingTerminals.forEach((port) => allTerminals.add(port));
  zone.statistics.unloadingTerminals.forEach((port) => allTerminals.add(port));

  // 返回排序后的去重码头列表
  return Array.from(allTerminals).sort();
};
</script>

<style lang="scss" scoped>
.buffer-stowage-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow: hidden;
}

.buffer-zones {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.buffer-zone {
  border: 2px solid;
  border-radius: 8px;
  padding: 10px;
  min-height: 240px;
  max-height: 1000px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;

  &.drag-over {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(64, 158, 255, 0.3);
    border-style: solid;

    .empty-zone.drop-zone::after {
      opacity: 1;
    }
  }
}

.buffer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  h4 {
    margin: 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
  }

  .buffer-stats {
    display: flex;
    gap: 8px;
  }

  .buffer-actions {
    display: flex;
    gap: 8px;
  }
}

.buffer-statistics {
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;

  .stat-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;

    .stat-label {
      min-width: 80px;
      font-weight: 500;
    }

    .stat-values {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .stat-item {
        background: rgba(255, 255, 255, 0.8);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
      }
    }

    .port-tags {
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
    }
  }
}

.buffer-content {
  flex: 1;
  margin-bottom: 2px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .zone-items {
    flex: 1;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
}

.empty-zone {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
  border: 2px dashed rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;

  .drop-hint {
    color: #999;
    font-size: 12px;
    margin-top: 4px;
  }

  &.drop-zone {
    position: relative;

    &::after {
      content: "拖拽货物到此处";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(64, 158, 255, 0.1);
      border: 1px solid #409eff;
      border-radius: 4px;
      padding: 8px 12px;
      font-size: 12px;
      color: #409eff;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s ease;
    }
  }
}

.zone-items {
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow-y: auto;
  flex: 1;
  max-height: 100%;
}

.buffer-item {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 2px;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0px;
    padding-bottom: 0px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .item-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .booking-no {
        font-weight: 600;
        color: #333;
        font-size: 12px;
        cursor: move;
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 0px 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        .drag-handle {
          opacity: 0.5;
          font-size: 12px;
        }

        &:hover {
          background-color: rgba(64, 158, 255, 0.1);

          .drag-handle {
            opacity: 1;
          }
        }
      }
    }

    .item-actions {
      display: flex;
      gap: 4px;
    }
  }

  .item-content {
    .item-row {
      display: flex;
      align-items: center;
      margin-bottom: 2px;
      font-size: 12px;

      .item-label {
        min-width: 40px;
        color: #666;
        font-weight: 500;
      }

      .item-value {
        color: #333;
        flex: 1;
      }

      .container-summary {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
        span {
          background: rgba(255, 255, 255, 0.8);
          padding: 2px 4px;
          border-radius: 3px;
          font-size: 11px;
        }
      }
    }
  }
}

.buffer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 12px;

  .footer-stats {
    color: #666;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .vessel-hint {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #e6a23c;
      font-size: 11px;

      .el-icon {
        font-size: 12px;
      }
    }
  }

  .footer-actions {
    display: flex;
    gap: 8px;
  }
}

.item-detail-content {
  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .detail-label {
        font-weight: 500;
        color: #666;
      }

      .detail-value {
        color: #333;
      }
    }
  }

  .container-details {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .container-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;

      .container-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e6e6e6;

        .container-label {
          font-size: 12px;
          color: #666;
        }

        .container-value {
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .remark-section {
    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #666;
      line-height: 1.6;
    }
  }
}

// 滚动条样式
.buffer-zones::-webkit-scrollbar,
.zone-items::-webkit-scrollbar {
  width: 6px;
}

.buffer-zones::-webkit-scrollbar-track,
.zone-items::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.buffer-zones::-webkit-scrollbar-thumb,
.zone-items::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.buffer-zones::-webkit-scrollbar-thumb:hover,
.zone-items::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

// 响应式设计
@media (max-width: 768px) {
  // 响应式样式
}
</style>