# MyBatis-Flex 四件套开发模板 2024版

本文档提供了一个基于MyBatis-Flex框架的四件套开发模板，包括Controller、Service接口、Service实现、Domain、Mapper以及链式操作示例、注意事项和最佳实践。开发人员可以根据此模板快速创建新表的四件套代码。

## 目录

- [MyBatis-Flex 四件套开发模板 2024版](#mybatis-flex-四件套开发模板-2024版)
  - [目录](#目录)
  - [1. Controller 层](#1-controller-层)
  - [2. Service 接口层](#2-service-接口层)
  - [3. Service 实现层](#3-service-实现层)
  - [4. Domain 层](#4-domain-层)
  - [5. Mapper 层](#5-mapper-层)
  - [6. 链式操作示例](#6-链式操作示例)
  - [7. 注意事项](#7-注意事项)
  - [8. 最佳实践](#8-最佳实践)

## 1. Controller 层

```java
@Slf4j
@RestController
@RequestMapping("/module/path")
public class EntityNameController extends BaseController {
    @Autowired
    private IEntityNameService entityNameService;

    /**
     * 分页查询实体
     */
    @PreAuthorize("@ss.hasPermi('module:path:list')")
    @GetMapping("/page")
    public AjaxResult page(@RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            EntityName entityName) {
        log.debug("分页查询实体 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, entityName);
        Page<EntityName> page = entityNameService.selectEntityNamePage(pageNumber, pageSize, entityName);
        log.debug("分页查询实体完成 - 总记录数: {}", page.getTotalRow());
        return AjaxResult.success(page);
    }

    /**
     * 导出实体列表
     */
    @PreAuthorize("@ss.hasPermi('module:path:export')")
    @Log(title = "实体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EntityName entityName) {
        log.debug("导出实体数据 - 查询条件: {}", entityName);
        List<EntityName> list = entityNameService.selectEntityNamePage(1, Integer.MAX_VALUE, entityName).getRecords();
        log.debug("导出实体数据 - 导出记录数: {}", list.size());
        ExcelUtil<EntityName> util = new ExcelUtil<EntityName>(EntityName.class);
        util.exportExcel(response, list, "实体数据");
        log.debug("导出实体数据完成");
    }

    /**
     * 获取实体详细信息
     */
    @PreAuthorize("@ss.hasPermi('module:path:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        log.debug("获取实体详细信息 - ID: {}", id);
        EntityName entityName = entityNameService.getById(id);
        log.debug("获取实体详细信息完成 - 结果: {}", entityName);
        return success(entityName);
    }

    /**
     * 新增实体
     */
    @PreAuthorize("@ss.hasPermi('module:path:add')")
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EntityName entityName) {
        log.debug("新增实体 - 数据: {}", entityName);
        boolean success = entityNameService.save(entityName);
        log.debug("新增实体完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 修改实体
     */
    @PreAuthorize("@ss.hasPermi('module:path:edit')")
    @Log(title = "实体", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EntityName entityName) {
        log.debug("修改实体 - 数据: {}", entityName);
        boolean success = entityNameService.updateById(entityName);
        log.debug("修改实体完成 - 结果: {}", success);
        return toAjax(success);
    }

    /**
     * 删除实体
     */
    @PreAuthorize("@ss.hasPermi('module:path:remove')")
    @Log(title = "实体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        log.debug("删除实体 - ID数组: {}", ids);
        boolean success = entityNameService.removeByIds(Arrays.asList(ids));
        log.debug("删除实体完成 - 结果: {}", success);
        return toAjax(success);
    }
}
```

## 2. Service 接口层

```java
public interface IEntityNameService extends IService<EntityName> {
    /**
     * 分页查询实体
     * 
     * @param pageNumber 页码
     * @param pageSize   每页大小
     * @param entityName 查询条件
     * @return 分页结果
     */
    public Page<EntityName> selectEntityNamePage(int pageNumber, int pageSize, EntityName entityName);
}
```

## 3. Service 实现层

```java
@Service
@Slf4j
public class EntityNameServiceImpl extends ServiceImpl<EntityNameMapper, EntityName> implements IEntityNameService {
    /**
     * 分页查询实体
     */
    @Override
    public Page<EntityName> selectEntityNamePage(int pageNumber, int pageSize, EntityName entityName) {
        log.debug("分页查询实体 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, entityName);
        
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(ENTITY_NAME)
                .where(ENTITY_NAME.FIELD1.like(entityName.getField1(), StringUtils.isNotBlank(entityName.getField1())))
                .and(ENTITY_NAME.FIELD2.eq(entityName.getField2(), entityName.getField2() != null))
                .orderBy(ENTITY_NAME.CREATE_TIME.desc());
        
        Page<EntityName> result = mapper.paginate(pageNumber, pageSize, queryWrapper);
        
        log.debug("分页查询实体完成 - 总记录数: {}", result.getTotalRow());
        
        return result;
    }
}
```

## 4. Domain 层

```java
@Data
@Table("table_name")
@EqualsAndHashCode(callSuper = true)
public class EntityName extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /** 字段1 */
    @Excel(name = "字段1")
    @Column("field1")
    private String field1;

    /** 字段2 */
    @Excel(name = "字段2")
    @Column("field2")
    private String field2;

    /** 字段3 */
    @Excel(name = "字段3")
    @Column("field3")
    private Integer field3;

    /** 字段4（日期类型） */
    @Excel(name = "字段4", width = 30, dateFormat = "yyyy-MM-dd")
    @Column("field4")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date field4;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /** 乐观锁版本号 */
    @Column("version")
    private Long version;

    /** 删除标志（0代表存在 2代表删除） */
    @Column("del_flag")
    private String delFlag;

    /** 部门ID */
    @Excel(name = "部门ID")
    @Column("dept_id")
    private Long deptId;
}
```

## 5. Mapper 层

```java
@Mapper
public interface EntityNameMapper extends BaseMapper<EntityName> {
    // 继承 BaseMapper 后，无需编写基础 CRUD 方法
    // 如需自定义方法，可在此处添加
}
```

## 6. 链式操作示例

```java
// 查询单个
EntityName entityName = entityNameMapper.selectOneById("1234567890123456789");

// 查询列表
List<EntityName> entityNameList = entityNameMapper.selectListByQuery(
    QueryWrapper.create()
        .where(EntityName::getField1).eq("value")
        .orderBy(EntityName::getCreateTime, false)
);

// 分页查询
Page<EntityName> page = entityNameMapper.paginate(
    Page.of(1, 10),
    QueryWrapper.create()
        .where(EntityName::getField1).eq("value")
);

// 更新单个
entityNameMapper.update(entityName);

// 批量更新
entityNameMapper.updateBatch(entityNameList);

// 条件更新
entityNameMapper.updateByQuery(
    UpdateWrapper.create()
        .set(EntityName::getField1, "newValue")
        .where(EntityName::getField1).eq("oldValue")
);

// 删除单个
entityNameMapper.deleteById("1234567890123456789");

// 批量删除
entityNameMapper.deleteBatchByIds(Arrays.asList("1234567890123456789", "9876543210987654321"));

// 条件删除
entityNameMapper.deleteByQuery(
    QueryWrapper.create()
        .where(EntityName::getField1).eq("value")
);
```

## 7. 注意事项

1. 实体类必须继承 BaseEntity，以获取通用字段和自动填充功能
2. 所有日期类型字段必须添加 `@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")` 注解，确保返回北京时间
3. 实体类只包含数据库表字段，不包含关联查询字段
4. 删除标志（del_flag）由框架自动处理，无需手动处理
5. 乐观锁（version）由框架自动处理，无需手动处理
6. 使用 MyBatis-Flex 提供的链式操作和 QueryWrapper
7. 合理使用缓存提高性能
8. 注意事务管理和异常处理
9. 做好日志记录
10. 在查询条件中，对于String类型的字段，应使用`StringUtils.isNotBlank()`进行判断，以同时检查null、空字符串和空白字符；对于非String类型的字段，只需使用`!= null`进行判断
11. 更新操作使用`update`方法而不是`updateById`
12. 批量更新使用`updateBatch`方法
13. 条件更新使用`updateByQuery`方法配合`UpdateWrapper`
14. Service 接口应继承 `IService` 接口，以使用框架提供的通用 CRUD 方法
15. Service 实现类应继承 `ServiceImpl`，并只实现必要的自定义查询方法
16. 充分利用框架提供的通用方法，避免重复实现基础 CRUD 操作

## 8. 最佳实践

1. 优先使用链式操作和 QueryWrapper
2. 使用批量操作方法提高性能
3. 注意事务管理和异常处理
4. 做好日志记录
5. 使用框架提供的自动填充功能
6. 合理使用缓存提高性能
7. 遵循单一职责原则
8. 代码要优雅、高效、规范
9. 所有时间字段统一使用 `@JsonFormat` 注解，确保返回标准格式的北京时间
10. 在Excel导出时，可以使用不同的日期格式，但JSON返回必须使用统一格式
11. 使用正确的MyBatis-Flex方法进行CRUD操作
12. 合理使用链式操作和Wrapper进行复杂查询
13. 注意事务的粒度控制
14. 做好异常处理和日志记录 