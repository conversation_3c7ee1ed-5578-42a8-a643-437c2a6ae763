package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicDangerUnNumber;
import com.ruoyi.basic.domain.table.BasicDangerUnNumberTableDef;
import com.ruoyi.basic.service.IBasicDangerUnNumberService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicDangerUnNumberTableDef.BASIC_DANGER_UN_NUMBER;

@RestController
@RequestMapping("/system/dangerUnNumber")
public class BasicDangerUnNumberController extends BaseController {
    @Autowired
    private IBasicDangerUnNumberService basicDangerUnNumberService;

    @PreAuthorize("@ss.hasPermi('system:dangerUnNumber:list')")
    @PostMapping("/list")
    public Page<BasicDangerUnNumber> list(@RequestBody List<String> dangerIds) {
        // startPage();
        // List<BasicDangerUnNumber> list = QueryChain.of(BasicDangerUnNumber.class)
        //         .eq("danger_id", basicDangerUnNumber.getDangerId())
        //         .orderBy("create_time asc")
        //         .list();
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.select()
        .where(BASIC_DANGER_UN_NUMBER.DANGER_ID.in(dangerIds))
        .orderBy(BASIC_DANGER_UN_NUMBER.CREATE_TIME.asc());
        return basicDangerUnNumberService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    @PreAuthorize("@ss.hasPermi('system:dangerUnNumber:export')")
    @Log(title = "危险品UN编号", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicDangerUnNumber basicDangerUnNumber) {
        List<BasicDangerUnNumber> list = QueryChain.of(BasicDangerUnNumber.class)
                .eq("danger_id", basicDangerUnNumber.getDangerId(), basicDangerUnNumber.getDangerId() != null)
                .orderBy("create_time asc")
                .list();
        ExcelUtil<BasicDangerUnNumber> util = new ExcelUtil<>(BasicDangerUnNumber.class);
        util.exportExcel(response, list, "危险品UN编号数据");
    }

    @PreAuthorize("@ss.hasPermi('system:dangerUnNumber:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(basicDangerUnNumberService.getById(id));
    }

    @PreAuthorize("@ss.hasPermi('system:dangerUnNumber:add')")
    @Log(title = "危险品UN编号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicDangerUnNumber basicDangerUnNumber) {
        try {
            return toAjax(basicDangerUnNumberService.save(basicDangerUnNumber));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('system:dangerUnNumber:edit')")
    @Log(title = "危险品UN编号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicDangerUnNumber basicDangerUnNumber) {
        try {
            return toAjax(basicDangerUnNumberService.updateById(basicDangerUnNumber));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('system:dangerUnNumber:remove')")
    @Log(title = "危险品UN编号", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<String> ids) {
        try {
            return toAjax(basicDangerUnNumberService.removeByIds(ids));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
