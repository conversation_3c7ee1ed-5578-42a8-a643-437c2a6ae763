import { ref, onMounted } from 'vue'
import request from '@/utils/request'

/**
 * 箱尺寸选项管理
 * 
 * 功能特性：
 * - 从BASIC_CNTR_SIZE表获取箱尺寸数据
 * - 支持标准化的label格式输出
 * - 优雅降级，API失败时返回空数组
 * 
 * 使用示例：
 * const { containerSizeOptions, getContainerSizeName } = useContainerSizeOptions()
 */
export function useContainerSizeOptions() {
  const containerSizeOptions = ref([])
  const loading = ref(false)

  // 加载箱尺寸选项
  const loadContainerSizeOptions = async (params = {}) => {
    loading.value = true
    try {
      const response = await request({
        url: '/system/cntrSize/label',
        method: 'get',
        params: {
          delFlag: '0', // 只获取有效数据
          ...params
        }
      })
      
      const data = response.data || response.rows || response || []
      
      // 转换数据格式为标准的Element UI Select格式
      const transformedData = Array.isArray(data) ? data.map(item => {
        // 修复label格式：避免重复显示相同的尺寸信息
        let label = item.sizeName || ''
        
        // 只有当sizeCode存在且与sizeName不同时才添加括号
        if (item.sizeCode && item.sizeCode !== item.sizeName) {
          label += ` (${item.sizeCode})`
        }
        
        return {
          label: label,
          value: item.id || '',
          sizeName: item.sizeName,
          sizeCode: item.sizeCode,
          sizeIso: item.sizeIso,
          sizeNetWeight: item.sizeNetWeight,
          sizeOverWeight: item.sizeOverWeight,
          sizeCategorized: item.sizeCategorized
        }
      }) : []
      
      containerSizeOptions.value = transformedData
      return containerSizeOptions.value
    } catch (error) {
      console.warn('加载箱尺寸选项失败，可能是后端接口未就绪:', error.message)
      containerSizeOptions.value = []
      return []
    } finally {
      loading.value = false
    }
  }

  // 根据ID获取箱尺寸名称
  const getContainerSizeName = (sizeId) => {
    const size = containerSizeOptions.value.find(item => item.value === sizeId)
    return size ? size.sizeName : ''
  }

  // 根据ID获取箱尺寸代码
  const getContainerSizeCode = (sizeId) => {
    const size = containerSizeOptions.value.find(item => item.value === sizeId)
    return size ? size.sizeCode : ''
  }

  // 组件挂载时自动加载选项
  onMounted(() => {
    loadContainerSizeOptions()
  })

  return {
    containerSizeOptions,
    loading,
    loadContainerSizeOptions,
    getContainerSizeName,
    getContainerSizeCode
  }
}