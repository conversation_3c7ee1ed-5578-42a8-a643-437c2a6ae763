# 航线支线模块数据库表结构设计

## 一、背景说明

- 本文档所描述的数据库表结构以实际业务需求为准。
- 所有字段名、类型、注释、命名规则及字段顺序均与业务逻辑保持一致。
- 主要涉及以下三张核心表：
  - `ROUTE_MAIN`：航线主表
  - `ROUTE_BRANCH`：支线主表
  - `ROUTE_RELATION`：码头连接关系表
- 已知有码头表BASIC_PORT_AREA(ID,PORT_AREA_CODE,PORT_AREA_NAME.......等字段)



## 二、ER关系图（mermaid）

```mermaid
erDiagram
    ROUTE_MAIN {
        VARCHAR2 MAIN_ID "主线ID（主键）,雪花ID"
        VARCHAR2 MAIN_NAME "主线名称"
        VARCHAR2 REMARK "备注"
        VARCHAR2 CREATE_BY "创建人"
        DATE CREATE_TIME "创建时间"
        VARCHAR2 UPDATE_BY "更新人"
        DATE UPDATE_TIME "更新时间"
        VARCHAR2 DEL_FLAG "逻辑删除标志"
        NUMBER VERSION "乐观锁"
    }
    ROUTE_BRANCH {
        VARCHAR2 BRANCH_ID "支线ID（主键）,雪花ID"
        VARCHAR2 BRANCH_NAME "支线名称"
        VARCHAR2 MAIN_ID "所属主线ID（外键）"
        VARCHAR2 REMARK "备注"
        VARCHAR2 CREATE_BY "创建人"
        DATE CREATE_TIME "创建时间"
        VARCHAR2 UPDATE_BY "更新人"
        DATE UPDATE_TIME "更新时间"
        VARCHAR2 DEL_FLAG "逻辑删除标志"
        NUMBER VERSION "乐观锁"
    }
    ROUTE_RELATION {
        VARCHAR2 RELATION_ID "连接ID（主键）,雪花ID"
        VARCHAR2 BRANCH_ID "关联ROUTE_BRANCH表ID（外键）"
        VARCHAR2 START_POINT_ID "起点码头ID（外键）"
        VARCHAR2 END_POINT_ID "终点码头ID（外键）"
        NUMBER DISTANCE_KM "距离（公里）"
        NUMBER DURATION_HOUR "时间（小时）"
        VARCHAR2 REMARK "备注"
        VARCHAR2 CREATE_BY "创建人"
        DATE CREATE_TIME "创建时间"
        VARCHAR2 UPDATE_BY "更新人"
        DATE UPDATE_TIME "更新时间"
        VARCHAR2 DEL_FLAG "逻辑删除标志"
        NUMBER VERSION "乐观锁"
    }
    BASIC_PORT_AREA {
        VARCHAR2 ID 
        VARCHAR2 PORT_AREA_CODE 
        VARCHAR2 PORT_AREA_NAME 
    }

    ROUTE_MAIN ||--o{ ROUTE_BRANCH : "1对多"
    ROUTE_BRANCH ||--o{ ROUTE_RELATION : "1对多"
    BASIC_PORT_AREA }|--o{ ROUTE_RELATION : "多对多"
```

**表名说明：**

- `ROUTE_MAIN`：航线主表，记录主要的航线区域；
- `ROUTE_BRANCH`：支线表，记录每条主线下的细分线路；
- `ROUTE_RELATION`：连接关系表，记录各支线之间的具体连接点；
- `BASIC_PORT_AREA`：码头表，记录所有可能作为起点或终点的码头信息（已有，无需改动）。




## 三、各表主要字段说明

### 1.ROUTE_MAIN(主线表)

| 字段名      | 类型          | 说明                   |
| ----------- | ------------- | ---------------------- |
| MAIN_ID     | VARCHAR2(32)  | 主线ID（主键），雪花ID |
| MAIN_NAME   | VARCHAR2(255) | 主线名称               |
| REMARK      | VARCHAR2(255) | 备注                   |
| CREATE_BY   | VARCHAR2(50)  | 创建人                 |
| CREATE_TIME | DATE          | 创建时间               |
| UPDATE_BY   | VARCHAR2(50)  | 更新人                 |
| UPDATE_TIME | DATE          | 更新时间               |
| DEL_FLAG    | VARCHAR2(1)   | 逻辑删除标志           |
| VERSION     | NUMBER(10)    | 乐观锁                 |

### 2.ROUTE_BRANCH(支线表)

| 字段名      | 类型          | 说明                            |
| ----------- | ------------- | ------------------------------- |
| BRANCH_ID   | VARCHAR2(32)  | 支线ID（主键），雪花ID          |
| BRANCH_NAME | VARCHAR2(255) | 支线名称                        |
| MAIN_ID     | VARCHAR2(32)  | 关联 `ROUTE_MAIN` 表 ID（外键） |
| REMARK      | VARCHAR2(255) | 备注                            |
| CREATE_BY   | VARCHAR2(50)  | 创建人                          |
| CREATE_TIME | DATE          | 创建时间                        |
| UPDATE_BY   | VARCHAR2(50)  | 更新人                          |
| UPDATE_TIME | DATE          | 更新时间                        |
| DEL_FLAG    | VARCHAR2(1)   | 逻辑删除标志                    |
| VERSION     | NUMBER(10)    | 乐观锁                          |

### 3.ROUTE_RELATION(码头连接关系表)

| 字段名         | 类型          | 说明                              |
| -------------- | ------------- | --------------------------------- |
| RELATION_ID    | VARCHAR2(32)  | 连接ID（主键），雪花ID            |
| BRANCH_ID      | VARCHAR2(32)  | 关联 `ROUTE_BRANCH` 表 ID（外键） |
| START_POINT_ID | VARCHAR2(50)  | 起点码头ID（外键）                |
| END_POINT_ID   | VARCHAR2(50)  | 终点码头ID（外键）                |
| DISTANCE_KM    | NUMBER(10,2)  | 距离（公里）                      |
| DURATION_HOUR  | NUMBER(10,2)  | 时间（小时）                      |
| REMARK         | VARCHAR2(255) | 备注                              |
| CREATE_BY      | VARCHAR2(50)  | 创建人                            |
| CREATE_TIME    | DATE          | 创建时间                          |
| UPDATE_BY      | VARCHAR2(50)  | 更新人                            |
| UPDATE_TIME    | DATE          | 更新时间                          |
| DEL_FLAG       | VARCHAR2(1)   | 逻辑删除标志                      |
| VERSION        | NUMBER(10)    | 乐观锁                            |

### 4.BASIC_PORT_AREA(码头表) （已有）

| 字段名         | 类型          | 说明 |
| -------------- | ------------- | ---- |
| ID             | VARCHAR2(50)  |      |
| PORT_AREA_NAME | VARCHAR2(255) |      |
| PORT_AREA_CODE | VARCHAR2(255) |      |
| ...            | ...           | ...  |