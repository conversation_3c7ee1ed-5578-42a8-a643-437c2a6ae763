package com.ruoyi.plan.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("VOYAGE_MAIN")
public class VoyageMain extends BaseEntity {
    /**
     * 船舶主键ID
     */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    @Column("VOYAGE_NO")
    private String voyageNo;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column("DISPATCH_SHIP_DATE")
    private Date dispatchShipDate;

    @Column("ORDER_STATUS")
    private String orderStatus;

    @Column("SHIP_ID")
    private String shipId;

    @Column("VOYAGE_TYPE")
    private String voyageType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column("REAL_START_TIME")
    private Date realStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column("REAL_END_TIME")
    private Date realEndTime;

    /**
     * 乐观锁
     */
    @Column("version")
    private Long version;

    /**
     * 逻辑删除标志
     */
    @Column(isLogicDelete = true)
    private String delFlag;
}
