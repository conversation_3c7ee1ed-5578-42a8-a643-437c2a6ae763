package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicEndurance;
import com.ruoyi.basic.service.IBasicEnduranceService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 航时信息Controller
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RestController
@RequestMapping("/system/endurance")
public class BasicEnduranceController extends BaseController {
    @Autowired
    private IBasicEnduranceService basicEnduranceService;

    /**
     * 查询航时信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:endurance:list')")
    @GetMapping("/list")
    public Page<BasicEndurance> list(BasicEndurance basicEndurance)
    {
        // startPage();
        // List<BasicEndurance> list = basicEnduranceService.selectBasicEnduranceList(basicEndurance);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.like(BasicEndurance::getStartTerminalId,basicEndurance.getStartTerminalId())
        .like(BasicEndurance::getEndTerminalId,basicEndurance.getEndTerminalId())
        .orderBy(BasicEndurance::getCreateTime, true);
        return basicEnduranceService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出航时信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:endurance:export')")
    @Log(title = "航时信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicEndurance basicEndurance)
    {
        List<BasicEndurance> list = basicEnduranceService.selectBasicEnduranceList(basicEndurance);
        ExcelUtil<BasicEndurance> util = new ExcelUtil<BasicEndurance>(BasicEndurance.class);
        util.exportExcel(response, list, "航时信息数据");
    }

    /**
     * 获取航时信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:endurance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicEnduranceService.selectBasicEnduranceById(id));
    }

    /**
     * 新增航时信息
     */
    @PreAuthorize("@ss.hasPermi('system:endurance:add')")
    @Log(title = "航时信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicEndurance basicEndurance)
    {
        try {
            return toAjax(basicEnduranceService.insertBasicEndurance(basicEndurance));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改航时信息
     */
    @PreAuthorize("@ss.hasPermi('system:endurance:edit')")
    @Log(title = "航时信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicEndurance basicEndurance)
    {
        try {
            return toAjax(basicEnduranceService.updateBasicEndurance(basicEndurance));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除航时信息
     */
    @PreAuthorize("@ss.hasPermi('system:endurance:remove')")
    @Log(title = "航时信息", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicEnduranceService.deleteBasicEnduranceByIds(ids));
    }
}
