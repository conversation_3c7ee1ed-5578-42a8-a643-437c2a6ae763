package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_cntr_iso")
public class BasicCntrIso extends BaseEntity {

    /** 国际箱型ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 国际箱型代码 */
    @Excel(name = "国际箱型代码")
    @Column("iso_code")
    private String isoCode;

    // /** 国际箱型名称 */
    // @Column("iso_name")
    // private String isoName;

    /** 关联箱尺寸ID */
    @Excel(name = "关联箱尺寸ID")
    private String cntrSizeId;

    /** 关联箱型ID */
    @Excel(name = "关联箱型ID")
    private String cntrTypeId;

    /** 乐观锁 */
    @Column(version = true)
    private Long version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("isoCode", getIsoCode())
                .append("version", getVersion())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
