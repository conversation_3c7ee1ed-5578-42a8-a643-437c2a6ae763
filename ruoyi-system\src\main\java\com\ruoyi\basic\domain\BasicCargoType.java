package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_cargo_type")
public class BasicCargoType extends BaseEntity {
    /** 货物基础信息编号 */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 货物代码 */
    @Excel(name = "货物代码")
    private String cargoCode;

    /** 货物中文名 */
    @Excel(name = "货物中文名")
    private String cargoName;

    /** 货物种类 */
    @Excel(name = "货物种类")
    private String cargoKind;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;
}
