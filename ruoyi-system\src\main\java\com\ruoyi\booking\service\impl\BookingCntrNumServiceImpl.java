package com.ruoyi.booking.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.BookingCntrNum;
import com.ruoyi.booking.mapper.BookingCntrNumMapper;
import com.ruoyi.booking.service.IBookingCntrNumService;
import org.springframework.stereotype.Service;

/**
 * 柜量信息表服务实现类
 * 继承ServiceImpl获得基础CRUD方法实现
 */
@Service
public class BookingCntrNumServiceImpl extends ServiceImpl<BookingCntrNumMapper, BookingCntrNum> implements IBookingCntrNumService {
    // 继承ServiceImpl后自动获得基础CRUD方法的实现
}