package com.ruoyi.booking.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.booking.domain.ViewBookingEntrust;
import com.ruoyi.booking.domain.vo.BookingEntrustQueryVO;
import com.ruoyi.booking.mapper.ViewBookingEntrustMapper;
import com.ruoyi.booking.service.IViewBookingEntrustService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ruoyi.booking.domain.table.ViewBookingEntrustTableDef.VIEW_BOOKING_ENTRUST;


/**
 * 订舱委托统一视图服务实现类
 * 对应数据库视图: v_booking_entrust
 * 用途: 为单证员工作台提供高性能的列表查询服务实现
 */
@Service
@Slf4j
public class ViewBookingEntrustServiceImpl extends ServiceImpl<ViewBookingEntrustMapper, ViewBookingEntrust> implements IViewBookingEntrustService {

    /**
     * 分页查询订舱委托视图数据
     * 支持多条件动态查询和排序
     */
    @Override
    public Page<ViewBookingEntrust> selectViewEntrustPage(int pageNumber, int pageSize, BookingEntrustQueryVO queryVO) {
        log.debug("分页查询订舱委托视图 - 页码: {}, 每页数量: {}, 查询条件: {}", pageNumber, pageSize, queryVO);
        
        // 构建动态查询条件 - 遵循MyBatis-Flex官方最佳实践
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(VIEW_BOOKING_ENTRUST.ALL_COLUMNS)
                .from(VIEW_BOOKING_ENTRUST)
                .where(VIEW_BOOKING_ENTRUST.DEL_FLAG.eq("0"))
                
                // 客户名称模糊查询
                .and(VIEW_BOOKING_ENTRUST.CUSTOMER_NAME.like(queryVO.getCustomerName(), If::hasText))
                
                // 委托号模糊查询
                .and(VIEW_BOOKING_ENTRUST.ENTRUST_NO.like(queryVO.getEntrustNo(), If::hasText))
                
                // 订舱号模糊查询（如果bookingNumber和entrustNo不同的话）
                .and(VIEW_BOOKING_ENTRUST.ENTRUST_NO.like(queryVO.getBookingNumber(), If::hasText))
                
                // 托运单位名称模糊查询（支持shipperName字段）
                .and(VIEW_BOOKING_ENTRUST.CUSTOMER_NAME.like(queryVO.getShipperName(), If::hasText))
                
                // 起运港精确匹配
                .and(VIEW_BOOKING_ENTRUST.POL.eq(queryVO.getPol(), If::hasText))
                
                // 目的港精确匹配  
                .and(VIEW_BOOKING_ENTRUST.POD.eq(queryVO.getPod(), If::hasText))
                
                // 委托状态精确匹配
                .and(VIEW_BOOKING_ENTRUST.OVERALL_STATUS.eq(queryVO.getEntrustStatus(), If::hasText))
                
                // 业务类型精确匹配
                .and(VIEW_BOOKING_ENTRUST.ORDER_BUSINESS_TYPE.eq(queryVO.getBusinessType(), If::hasText))
                
                // 贸易类型精确匹配
                .and(VIEW_BOOKING_ENTRUST.ORDER_TRADE_TYPE.eq(queryVO.getTradeType(), If::hasText))
                
                // 委托日期范围查询 - MyBatis-Flex自动忽略null值
                .and(VIEW_BOOKING_ENTRUST.ORDER_DATE.ge(queryVO.getEntrustDateStart()))
                .and(VIEW_BOOKING_ENTRUST.ORDER_DATE.le(queryVO.getEntrustDateEnd()))
                
                // 默认按创建时间倒序排列
                .orderBy(VIEW_BOOKING_ENTRUST.CREATE_TIME.desc());

        // 执行分页查询
        Page<ViewBookingEntrust> result = mapper.paginate(pageNumber, pageSize, queryWrapper);
        
        log.debug("分页查询订舱委托视图完成 - 总记录数: {}, 当前页记录数: {}", result.getTotalRow(), result.getRecords().size());
        
        return result;
    }
}