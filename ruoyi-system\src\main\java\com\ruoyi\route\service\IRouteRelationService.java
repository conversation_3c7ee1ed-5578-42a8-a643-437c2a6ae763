package com.ruoyi.route.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.ruoyi.route.domain.RouteRelation;
import com.ruoyi.route.mapper.RouteRelationMapper;

import java.util.List;

/**
 * 码头连接关系Service接口
 */
public interface IRouteRelationService extends IService<RouteRelation> {
    /**
     * 获取Mapper
     */
    RouteRelationMapper getMapper();

    /**
     * 分页查询码头连接关系
     */
     Page<RouteRelation> selectRouteRelationPage(int pageNumber, int pageSize, RouteRelation routeRelation);

    /**
     * 查询码头连接关系列表
     */
    List<RouteRelation> selectRouteRelationList(RouteRelation routeRelation);
} 