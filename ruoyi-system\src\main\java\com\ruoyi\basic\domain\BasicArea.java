package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_area")
public class BasicArea extends BaseEntity {
    /** 区域ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 国家ID */
    @Excel(name = "国家ID")
    private String couId;

    /** 区域代码 */
    @Excel(name = "区域代码")
    private String areaCode;

    /** 区域名称 */
    @Excel(name = "区域名称")
    private String areaName;

    /** 邮编 */
    @Excel(name = "邮编")
    private String email;

    /** 短名代码 */
    @Excel(name = "短名代码")
    private String shortCode;

    /** 短名名称 */
    @Excel(name = "短名名称")
    private String shortName;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 删除标志 */
    @Column(isLogicDelete = true)
    private String delFlag;
}
