package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_mileage")
public class BasicMileage extends BaseEntity {

    /** 码头里程ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 起始码头 */
    @Excel(name = "起始码头")
    private String startTerminalId;

    /** 目的码头 */
    @Excel(name = "目的码头")
    private String endTerminalId;

    /** 里程 */
    @Excel(name = "里程")
    private Long mileage;

    /** 里程单位 */
    @Excel(name = "里程单位")
    private String mileageUnit;

    /** 乐观锁 */
    @Excel(name = "乐观锁")
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

}
