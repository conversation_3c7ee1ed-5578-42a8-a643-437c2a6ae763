package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Table("basic_zone")
@Data
public class BasicZone extends BaseEntity {

    /** 关区ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 关区代码 */
    @Excel(name = "关区代码")
    private String zoneCode;

    /** 关区名称 */
    @Excel(name = "关区名称")
    private String zoneName;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

    /** 关区码头关联信息 */
    @Column(ignore = true)
    private List<BasicZoneTerminal> basicZoneTerminalList;
}
