package com.ruoyi.basic.domain;


import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_terminal")
public class BasicTerminal extends BaseEntity {
    /** 码头ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 码头名称 */
    @Excel(name = "码头名称")
    private String terminalName;

    /** 码头英文名称 */
    @Excel(name = "码头英文名称")
    private String englishName;

    /** 码头代码 */
    @Excel(name = "码头代码")
    private String terminalCode;

    /** 所属码头 */
    @Excel(name = "所属码头")
    private String portId;

    /** 排序 */
    @Excel(name = "排序")
    private BigDecimal sort;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    private String delFlag;

    /** 所属类别 字典terminal */
    @Excel(name = "所属类别 字典terminal")
    private String type;

    /** 码头别名 */
    @Excel(name = "码头别名")
    private String terminalOtherName;

    /** 海事编码 */
    @Excel(name = "海事编码")
    private String hgCode;

    /** 所属海事局 字典marine_board */
    @Excel(name = "所属海事局 字典marine_board")
    private String sshsj;

    /** 所属港区 */
    @Excel(name = "所属港区")
    private String portAreaId;

    /** 类型 字典anchorage_type */
    @Excel(name = "类型 字典anchorage_type")
    private String terType;

    /** 所属区域 */
    @Excel(name = "所属区域")
    private String areaId;

    /** 预计在港时长 */
    @Excel(name = "预计在港时长")
    private BigDecimal expectTimeInPort;
}
