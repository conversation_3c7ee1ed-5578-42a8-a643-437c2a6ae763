package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicArea;
import com.ruoyi.basic.service.IBasicAreaService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicAreaTableDef.BASIC_AREA;

/**
 * 区域管理Controller
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@RestController
@RequestMapping("/system/area")
public class BasicAreaController extends BaseController {
    @Autowired
    private IBasicAreaService basicAreaService;

    /**
     * 查询区域管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:list')")
    @GetMapping("/list")
    public Page<BasicArea> list(BasicArea basicArea,Integer pageNum,Integer pageSize)
    {
//        startPage();
//        List<BasicArea> list = basicAreaService.selectBasicAreaList(basicArea);
//        return getDataTable(list);
        Page<BasicArea> page = basicAreaService.page(new Page<>(pageNum,pageSize), QueryWrapper.create()
        .select()
        .where(BASIC_AREA.COU_ID.eq(basicArea.getCouId(), StringUtils.isNotEmpty(basicArea.getCouId())))
        .and(BASIC_AREA.AREA_CODE.like(basicArea.getAreaCode(), StringUtils.isNotEmpty(basicArea.getAreaCode())))
        .and(BASIC_AREA.AREA_NAME.like(basicArea.getAreaName(), StringUtils.isNotEmpty(basicArea.getAreaName())))
        .orderBy(BASIC_AREA.CREATE_TIME.asc()));
        return page;
    }



    @PreAuthorize("@ss.hasPermi('system:area:list')")
    @GetMapping("/label")
    public AjaxResult label(BasicArea basicArea){
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .where(BASIC_AREA.COU_ID.eq(basicArea.getCouId(), StringUtils.isNotEmpty(basicArea.getCouId())));
        List<BasicArea> list = basicAreaService.list(queryWrapper);
        return AjaxResult.success(list);
    }

    /**
     * 导出区域管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:area:export')")
    @Log(title = "区域管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicArea basicArea)
    {
        List<BasicArea> list = basicAreaService.selectBasicAreaList(basicArea);
        ExcelUtil<BasicArea> util = new ExcelUtil<BasicArea>(BasicArea.class);
        util.exportExcel(response, list, "区域管理数据");
    }

    /**
     * 获取区域管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:area:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicAreaService.selectBasicAreaById(id));
    }

    /**
     * 新增区域管理
     */
    @PreAuthorize("@ss.hasPermi('system:area:add')")
    @Log(title = "区域管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicArea basicArea)
    {
        try {
            return toAjax(basicAreaService.insertBasicArea(basicArea));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改区域管理
     */
    @PreAuthorize("@ss.hasPermi('system:area:edit')")
    @Log(title = "区域管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicArea basicArea)
    {
        try {
            return toAjax(basicAreaService.updateBasicArea(basicArea));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除区域管理
     */
    @PreAuthorize("@ss.hasPermi('system:area:remove')")
    @Log(title = "区域管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        try {
            return toAjax(basicAreaService.deleteBasicAreaByIds(ids));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
