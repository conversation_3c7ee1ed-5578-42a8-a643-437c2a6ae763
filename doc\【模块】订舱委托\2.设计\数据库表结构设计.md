# 订舱委托模块数据库表结构设计（最终版）

## 一、背景说明
- 本文档表结构以《订舱委托-数据库表结构.sql》为准，所有字段、类型、注释、命名、顺序均与SQL保持一致。
- 主要表：ORDER_MAIN（订单主表）、BOOKING_MAIN（订舱委托主表）、BOOKING_CNTR_COARSE（粗略箱信息表）、BOOKING_CNTR_DETAIL（明细箱信息表）。

---

## 二、ER关系图（Mermaid）

```mermaid
erDiagram
    ORDER_MAIN {
        VARCHAR2 id "主键，雪花ID"
        VARCHAR2 order_no "订单编号"
        VARCHAR2 customer_id "客户ID"
        DATE order_date "订单日期"
        VARCHAR2 remark "备注"
        VARCHAR2 create_by "创建人"
        DATE create_time "创建时间"
        VARCHAR2 update_by "更新人"
        DATE update_time "更新时间"
        VARCHAR2 del_flag "逻辑删除标志"
        NUMBER version "乐观锁"
    }
    BOOKING_MAIN {
        VARCHAR2 id "主键，雪花ID"
        VARCHAR2 order_id "关联ORDER_MAIN表ID"
        VARCHAR2 booking_no "委托编号"
        VARCHAR2 customer_id "订舱客户ID"
        DATE booking_date "订舱日期"
        VARCHAR2 contract_id "执行合同ID"
        VARCHAR2 trade_type "贸易类型"
        VARCHAR2 load_terminal_id "装货码头ID"
        VARCHAR2 unload_terminal_id "卸货码头ID"
        VARCHAR2 load_agent_id "装货代理ID"
        VARCHAR2 unload_agent_id "卸货代理ID"
        VARCHAR2 vessel_schedule "大船船期"
        VARCHAR2 remark "备注"
        VARCHAR2 create_by "创建人"
        DATE create_time "创建时间"
        VARCHAR2 update_by "更新人"
        DATE update_time "更新时间"
        VARCHAR2 del_flag "逻辑删除标志"
        NUMBER version "乐观锁"
    }
    BOOKING_CNTR_COARSE {
        VARCHAR2 id "主键，雪花ID"
        VARCHAR2 booking_id "关联BOOKING_MAIN表ID"
        VARCHAR2 cntr_size "尺寸"
        NUMBER quantity "箱量"
        NUMBER weight "总重"
        VARCHAR2 danger_level "危险品等级"
        VARCHAR2 remark "备注"
        VARCHAR2 create_by "创建人"
        DATE create_time "创建时间"
        VARCHAR2 update_by "更新人"
        DATE update_time "更新时间"
        VARCHAR2 del_flag "逻辑删除标志"
        NUMBER version "乐观锁"
    }
    BOOKING_CNTR_DETAIL {
        VARCHAR2 id "主键，雪花ID"
        VARCHAR2 booking_id "关联BOOKING_MAIN表ID"
        VARCHAR2 box_no "箱号"
        VARCHAR2 cntr_size "尺寸"
        VARCHAR2 box_model "箱型"
        NUMBER weight "重量"
        VARCHAR2 danger_level "危险品等级"
        VARCHAR2 remark "备注"
        VARCHAR2 create_by "创建人"
        DATE create_time "创建时间"
        VARCHAR2 update_by "更新人"
        DATE update_time "更新时间"
        VARCHAR2 del_flag "逻辑删除标志"
        NUMBER version "乐观锁"
    }

    ORDER_MAIN ||--o{ BOOKING_MAIN : "1对多"
    BOOKING_MAIN ||--o{ BOOKING_CNTR_COARSE : "1对多"
    BOOKING_MAIN ||--o{ BOOKING_CNTR_DETAIL : "1对多"
```

> **表名说明：**
> - ORDER_MAIN：订单主表
> - BOOKING_MAIN：订舱委托主表
> - BOOKING_CNTR_COARSE：粗略箱信息表
> - BOOKING_CNTR_DETAIL：明细箱信息表

---

## 三、各表主要字段

### 1. ORDER_MAIN（订单主表）
| 字段名      | 类型           | 说明         |
|-------------|----------------|--------------|
| id          | VARCHAR2(32)   | 主键，雪花ID |
| order_no    | VARCHAR2(64)   | 订单编号     |
| customer_id | VARCHAR2(64)   | 客户ID       |
| order_date  | DATE           | 订单日期     |
| remark      | VARCHAR2(255)  | 备注         |
| create_by   | VARCHAR2(64)   | 创建人       |
| create_time | DATE           | 创建时间     |
| update_by   | VARCHAR2(64)   | 更新人       |
| update_time | DATE           | 更新时间     |
| del_flag    | VARCHAR2(1)    | 逻辑删除标志 |
| version     | NUMBER(10)     | 乐观锁       |

### 2. BOOKING_MAIN（订舱委托主表）
| 字段名             | 类型           | 说明           |
|--------------------|----------------|----------------|
| id                 | VARCHAR2(32)   | 主键，雪花ID   |
| order_id           | VARCHAR2(32)   | 关联ORDER_MAIN表ID |
| booking_no         | VARCHAR2(64)   | 委托编号       |
| customer_id        | VARCHAR2(32)   | 订舱客户ID     |
| booking_date       | DATE           | 订舱日期       |
| contract_id        | VARCHAR2(32)   | 执行合同ID     |
| trade_type         | VARCHAR2(32)   | 贸易类型       |
| load_terminal_id   | VARCHAR2(32)   | 装货码头ID     |
| unload_terminal_id | VARCHAR2(32)   | 卸货码头ID     |
| load_agent_id      | VARCHAR2(32)   | 装货代理ID     |
| unload_agent_id    | VARCHAR2(32)   | 卸货代理ID     |
| vessel_schedule    | VARCHAR2(64)   | 大船船期       |
| remark             | VARCHAR2(255)  | 备注           |
| create_by          | VARCHAR2(64)   | 创建人         |
| create_time        | DATE           | 创建时间       |
| update_by          | VARCHAR2(64)   | 更新人         |
| update_time        | DATE           | 更新时间       |
| del_flag           | VARCHAR2(1)    | 逻辑删除标志   |
| version            | NUMBER(10)     | 乐观锁         |

### 3. BOOKING_CNTR_COARSE（粗略箱信息表）
| 字段名        | 类型           | 说明             |
|---------------|----------------|------------------|
| id            | VARCHAR2(32)   | 主键，雪花ID     |
| booking_id    | VARCHAR2(32)   | 关联BOOKING_MAIN表ID |
| cntr_size     | VARCHAR2(16)   | 尺寸             |
| quantity      | NUMBER(10)     | 箱量             |
| weight        | NUMBER(18,4)   | 总重             |
| danger_level  | VARCHAR2(32)   | 危险品等级       |
| remark        | VARCHAR2(255)  | 备注             |
| create_by     | VARCHAR2(64)   | 创建人           |
| create_time   | DATE           | 创建时间         |
| update_by     | VARCHAR2(64)   | 更新人           |
| update_time   | DATE           | 更新时间         |
| del_flag      | VARCHAR2(1)    | 逻辑删除标志     |
| version       | NUMBER(10)     | 乐观锁           |

### 4. BOOKING_CNTR_DETAIL（明细箱信息表）
| 字段名        | 类型           | 说明             |
|---------------|----------------|------------------|
| id            | VARCHAR2(32)   | 主键，雪花ID     |
| booking_id    | VARCHAR2(32)   | 关联BOOKING_MAIN表ID |
| box_no        | VARCHAR2(32)   | 箱号             |
| cntr_size     | VARCHAR2(16)   | 尺寸             |
| box_model     | VARCHAR2(32)   | 箱型             |
| weight        | NUMBER(18,4)   | 重量             |
| danger_level  | VARCHAR2(32)   | 危险品等级       |
| remark        | VARCHAR2(255)  | 备注             |
| create_by     | VARCHAR2(64)   | 创建人           |
| create_time   | DATE           | 创建时间         |
| update_by     | VARCHAR2(64)   | 更新人           |
| update_time   | DATE           | 更新时间         |
| del_flag      | VARCHAR2(1)    | 逻辑删除标志     |
| version       | NUMBER(10)     | 乐观锁           |