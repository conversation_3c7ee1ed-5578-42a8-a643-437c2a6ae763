INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10060', '11', '10016', '10039', NULL, '1');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10059', 'WWE', '10016', '10039', '测试', '1');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10061', '22P1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10062', '22P1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10063', '22G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10064', '22G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10065', '22G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10066', '22U1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10067', '22R1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10068', '22T0', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10069', '25G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10070', '25G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10071', '25R1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10072', '42P1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10073', '42G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10074', '42U1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10075', '42R1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10076', '42T0', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10077', '45G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10078', '45G1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10079', '45R1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10080', '45R1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10081', '42P1', NULL, NULL, NULL, '0');
INSERT INTO "BASIC_CNTR_ISO" ("ID", "ISO_CODE", "CNTR_SIZE_ID", "CNTR_TYPE_ID", "REMARK", "DEL_FLAG") VALUES ('10082', 'L5P0', NULL, NULL, NULL, '0');
UPDATE BASIC_CNTR_ISO SET DEL_FLAG = '2' WHERE  DEL_FLAG != '0'