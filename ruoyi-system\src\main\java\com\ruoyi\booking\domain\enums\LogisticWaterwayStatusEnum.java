package com.ruoyi.booking.domain.enums;

import lombok.Getter;

/**
 * 物流水运记录状态枚举
 */
@Getter
public enum LogisticWaterwayStatusEnum {
    /**
     * 待处理
     */
    PENDING("pending", "待处理"),

    /**
     * 已拆解
     */
    SPLIT("split", "已拆解"),

    /**
     * 已分配
     */
    ALLOCATED("allocated", "已分配");

    private final String code;
    private final String info;

    LogisticWaterwayStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

}
