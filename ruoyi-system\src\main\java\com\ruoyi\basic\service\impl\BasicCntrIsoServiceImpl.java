package com.ruoyi.basic.service.impl;

import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicCntrIso;
import com.ruoyi.basic.mapper.BasicCntrIsoMapper;
import com.ruoyi.basic.service.IBasicCntrIsoService;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.basic.domain.table.BasicCntrIsoTableDef.BASIC_CNTR_ISO;

@Service
@Transactional(rollbackFor = Exception.class)
public class BasicCntrIsoServiceImpl extends ServiceImpl<BasicCntrIsoMapper, BasicCntrIso> implements IBasicCntrIsoService {

    @Autowired
    private BasicCntrIsoMapper basicCntrIsoMapper;

    @Override
    public BasicCntrIsoMapper getMapper() {
        return basicCntrIsoMapper;
    }

    /**
     * 查询箱ISO
     *
     * @param id 箱ISO主键
     * @return 箱ISO
     */
    @Override
    public BasicCntrIso selectBasicCntrIsoById(String id) {
        return basicCntrIsoMapper.selectOneById(id);
    }

    /**
     * 查询箱ISO列表
     *
     * @param basicCntrIso 箱ISO
     * @return 箱ISO
     */
    @Override
    public List<BasicCntrIso> selectBasicCntrIsoList(BasicCntrIso basicCntrIso) {
        return QueryChain.of(BasicCntrIso.class)
                .where(BASIC_CNTR_ISO.ISO_CODE.like(basicCntrIso.getIsoCode(), StringUtils::isNotEmpty))
//                .and(BASIC_CNTR_ISO.ISO_NAME.like(basicCntrIso.getIsoName(), StringUtils::isNotEmpty))
                .list();
    }

    /**
     * 新增箱ISO
     *
     * @param basicCntrIso 箱ISO
     * @return 结果
     */
    @Override
    public int insertBasicCntrIso(BasicCntrIso basicCntrIso) {
        return basicCntrIsoMapper.insert(basicCntrIso);
    }

    /**
     * 修改箱ISO
     *
     * @param basicCntrIso 箱ISO
     * @return 结果
     */
    @Override
    public int updateBasicCntrIso(BasicCntrIso basicCntrIso) {
        return basicCntrIsoMapper.update(basicCntrIso);
    }

    /**
     * 批量删除箱ISO
     *
     * @param ids 需要删除的箱ISO主键集合
     * @return 结果
     */
    @Override
    public int deleteBasicCntrIsoByIds(List<String> ids) {
        return basicCntrIsoMapper.deleteBatchByIds(ids);
    }

    /**
     * 删除箱ISO信息
     *
     * @param id 箱ISO主键
     * @return 结果
     */
    @Override
    public int deleteBasicCntrIsoById(String id) {
        return basicCntrIsoMapper.deleteById(id);
    }

    public void check(BasicCntrIso basicCntrIso) throws Exception {
        QueryChain queryChain = QueryChain.of(BasicCntrIso.class)
                .and(BASIC_CNTR_ISO.ID.ne(basicCntrIso.getId()).when(StringUtils.isNotEmpty(basicCntrIso.getId())))
                .and(BASIC_CNTR_ISO.ISO_CODE.eq(basicCntrIso.getIsoCode()));

        long check = basicCntrIsoMapper.selectCountByQuery(queryChain);

        if(check > 0){
            throw new Exception("国家箱型代码不唯一");
        }
    }
}
