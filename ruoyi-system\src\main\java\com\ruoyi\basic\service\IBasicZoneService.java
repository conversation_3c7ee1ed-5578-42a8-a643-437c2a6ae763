package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicZone;

import java.util.List;

public interface IBasicZoneService extends IService<BasicZone> {

    /**
     * 查询关区管理
     *
     * @param id 关区管理主键
     * @return 关区管理
     */
    public BasicZone selectBasicZoneById(String id);

    /**
     * 查询关区管理列表
     *
     * @param basicZone 关区管理
     * @return 关区管理集合
     */
    public List<BasicZone> selectBasicZoneList(BasicZone basicZone);

    /**
     * 新增关区管理
     *
     * @param basicZone 关区管理
     * @return 结果
     */
    public int insertBasicZone(BasicZone basicZone) throws Exception;

    /**
     * 修改关区管理
     *
     * @param basicZone 关区管理
     * @return 结果
     */
    public int updateBasicZone(BasicZone basicZone) throws Exception;

    /**
     * 批量删除关区管理
     *
     * @param ids 需要删除的关区管理主键集合
     * @return 结果
     */
    public int deleteBasicZoneByIds(List<String> ids);

    /**
     * 删除关区管理信息
     *
     * @param id 关区管理主键
     * @return 结果
     */
    public int deleteBasicZoneById(String id);

}
