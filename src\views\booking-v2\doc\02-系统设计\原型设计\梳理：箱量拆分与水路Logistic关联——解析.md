# 订单物流系统V3.0 数据库与业务流程解析

本文档旨在对订单物流系统V3.0的核心数据库结构和业务流程进行全面梳理，帮助团队成员理解系统设计的核心思想，特别是“箱量拆分”和“三层解耦”架构。

## 1. 核心业务流程图

下图展示了从客户下单到策划员配船的完整业务流转过程，以及关键数据在不同角色和阶段的创建与流转。

```mermaid
graph TD
    subgraph "用户/单证员"
        A[客户发起委托] --> B(创建 委托主表 order_main);
        B --> C{一个委托含多个订舱};
        C --> D(创建 订舱主表 booking_main);
        D --> E(录入 计划箱量 booking_cntr_num);
        E --> F[计划箱量录入完成];
    end

    subgraph "策划员"
        F --> G{配船/运输计划};
        G --> H(1. 初始化拆分);
        H --> I(在 booking_cntr_num_split 中
生成一条与父记录相同的初始数据);
        I --> J{根据运输资源拆分};
        J --> K(2. 拆分/合并);
        K --> L(在 booking_cntr_num_split 表内
进行拆分或合并操作);
        L --> M(3. 分配运输);
        M --> N(创建 物流主表 logistics_main
及具体运输环节子表);
        N --> O(将 booking_cntr_num_split 记录
关联到具体运输环节ID);
        O --> P[完成配船];
    end

    style F fill:#f9f,stroke:#333,stroke-width:2px
    style P fill:#ccf,stroke:#333,stroke-width:2px
```

**流程解读:**
1.  **单证员阶段**：负责将客户的原始委托（`order_main`）转化为具体的订舱（`booking_main`），并录入客户要求的“计划箱量”（`booking_cntr_num`）。此处的箱量数据是固定的，作为后续操作的基准。
2.  **策划员阶段**：核心工作是“配船”。策划员会基于单证员录入的“计划箱量”，在“可拆分箱量表”（`booking_cntr_num_split`）中进行灵活的拆分、合并操作，然后将这些拆分后的具体箱量分配给实际的运输环节（`logistics_main`及其子表）。

---

## 2. 数据库核心ER图

下图展示了订单物流系统核心表的实体关系（ER图）。

```mermaid
erDiagram
    order_main {
        varchar(32) id PK "委托ID"
        varchar(64) order_no "委托编号"
        varchar(32) status "委托状态"
    }

    booking_main {
        varchar(32) id PK "订舱ID"
        varchar(32) order_id FK "委托ID"
        varchar(64) booking_number "订舱号"
        varchar(32) status "订舱状态"
    }

    logistics_main {
        varchar(32) id PK "物流ID"
        varchar(32) booking_id FK "订舱ID"
        varchar(32) business_type "运输模式(水路/陆路)"
    }

    logistic_waterway {
        varchar(32) id PK "水路运输ID"
        varchar(32) logistics_main_id FK "物流ID"
        varchar(128) vessel_name "船名"
        varchar(128) voyage_no "航次"
    }

    booking_cntr_num {
        varchar(32) id PK "计划箱量ID"
        varchar(32) booking_id FK "订舱ID"
        number quantity "计划数量 (固定)"
    }

    booking_cntr_num_split {
        varchar(32) id PK "拆分箱量ID"
        varchar(32) booking_cntr_num_id FK "计划箱量ID"
        number split_quantity "拆分后数量"
        varchar(32) related_waterway_id FK "水路运输ID"
    }

    order_main ||--o{ booking_main : "包含"
    booking_main ||--o{ logistics_main : "安排"
    booking_main ||--o{ booking_cntr_num : "定义"
    logistics_main ||--o{ logistic_waterway : "包含(未来规划)"
    booking_cntr_num ||--o{ booking_cntr_num_split : "被拆分为"
    logistic_waterway }o--|| booking_cntr_num_split : "分配给"
```
**关系解读:**
*   **三层核心链**：`order_main` → `booking_main` → `logistics_main` 构成了业务的主干。
*   **运输环节细化 (未来规划)**: `logistics_main` 定义了宏观运输模式，其子表 `logistic_waterway` (未来创建) 将记录具体的水路运输详情。
*   **箱量拆分核心**：`booking_cntr_num` (计划) 与 `booking_cntr_num_split` (实际操作) 是一对多关系，这是策划员配船功能实现的关键。
*   **核心分配关系**: `logistic_waterway` 与 `booking_cntr_num_split` 是 **一对多** 关系。一个水路运输环节（一条船的一个航次），可以分配多个拆分后的箱量。这是通过在 `booking_cntr_num_split` 中设置外键 `related_waterway_id` 实现的。

---

## 3. 核心表结构与业务逻辑解析

### 3.1. `booking_cntr_num` - 计划箱量表

*   **用途**: 记录单证员录入的、客户委托的 **“原始计划”** 箱量信息。
*   **核心逻辑**:
    *   **数据固定**: 此表中的 `quantity` 和 `total_weight` 等数据一旦录入后即 **固定不变**，作为所有后续操作的基准和总量控制依据。
    *   **拆分源头**: 它是箱量拆分的唯一源头。

### 3.2. `booking_cntr_num_split` - 箱量拆分明细表

*   **用途**: 策划员进行 **实际操作** 的核心表。所有箱量的拆分、合并、分配都在此表进行。
*   **核心逻辑**:
    *   **初始化**: 当 `booking_cntr_num` 创建时，系统会自动在此表生成一条完全相同的初始记录。
    *   **拆分/合并**: 策划员可将一条记录拆为多条，或将多条合并为一条。
        *   **总量约束**: 所有关联到同一个 `booking_cntr_num` 的 `split_quantity` 之和，必须恒等于父记录的 `quantity`。这个校验在 **业务代码层面** 实现。
    *   **分配给运输**: `related_waterway_id` 字段用于直接关联到一个具体的运输环节（如水路运输的ID）。这意味着一个拆分后的箱量在某一时刻只能属于一个运输段。
    *   **级联删除**: 与父表 `booking_cntr_num` 建立了 `ON DELETE CASCADE` 关系，父记录被删除时，所有拆分明细将自动删除。

### 3.3. `logistics_main` - 物流主表

*   **用途**: 定义一个高级别的物流环节，作为三层架构的第三层。
*   **核心逻辑**:
    *   **运输模式**: `business_type` 字段定义了该环节的运输模式，如 `WATERWAY`（水路）、`ROADWAY`（陆路）等。
    *   **具体信息载体**: 它本身不包含详细的运输信息（如船名、航次），这些信息将存储在未来的子表中（如 `logistic_waterway`）。

---

## 4. 总结与待办

### 设计亮点
1.  **三层解耦**: `Order` -> `Booking` -> `Logistics` 的架构清晰地分离了客户意向、订舱处理和物流执行三个层面。
2.  **计划与执行分离**: 通过 `booking_cntr_num` (计划) 和 `booking_cntr_num_split` (执行) 的设计，完美解决了策划员需要灵活拆分箱量，同时又要保证原始订单数据不被破坏的矛盾，权责清晰。
3.  **关联简化**: 放弃中间表，在 `booking_cntr_num_split` 中直接关联运输环节ID，简化了模型，满足当前核心需求。

### 后续优化点 (摘自设计纪要)
1.  **实际业务信息存储**: 需要明确实际的箱号、铅封号等信息在哪个表中存储。
2.  **多段运输追溯**: 当前设计下，一个箱量在多段运输中会更新其关联的运输ID。需要思考如何高效地追溯一个箱量所经历的 **所有历史运输环节**。
3.  **运输子表设计**: 需尽快明确 `LOGISTICS_MAIN` 的子表（如 `LOGISTIC_WATERWAY`, `LOGISTICS_ROADWAY`）的具体设计。
4.  **跨运输类型关联**: 思考未来 `booking_cntr_num_split` 如何与陆路、航空等不同类型的运输环节进行关联。

希望这份文档能帮助您的团队更好地理解系统，并顺利推进后续的开发工作。