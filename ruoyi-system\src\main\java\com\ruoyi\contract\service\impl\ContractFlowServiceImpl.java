package com.ruoyi.contract.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.contract.domain.ContractFlow;
import com.ruoyi.contract.domain.ContractFlowTerminal;
import com.ruoyi.contract.mapper.ContractFlowMapper;
import com.ruoyi.contract.mapper.ContractFlowTerminalMapper;
import com.ruoyi.contract.service.IContractFlowService;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 合同流向服务实现类
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContractFlowServiceImpl implements IContractFlowService {

    @Autowired
    private ContractFlowMapper contractFlowMapper;
    
    @Autowired
    private ContractFlowTerminalMapper contractFlowTerminalMapper;

    @Override
    public AjaxResult saveFlows(List<ContractFlow> flows) {
        if (flows == null || flows.isEmpty()) {
            return AjaxResult.error("流向数据不能为空");
        }

        System.out.println(flows);

        try {
            // 获取第一个流向的合同ID
            String contractId = flows.get(0).getContractId();
            
            // 保存新的流向数据
            for (ContractFlow flow : flows) {
                // 设置合同ID
                flow.setContractId(contractId);

                // 保存流向
//                contractFlowMapper.insertOrUpdate(flow);

                if(StringUtils.isNotEmpty(flow.getFlowId())){
                    contractFlowMapper.update(flow);
                }else {
                    contractFlowMapper.insert(flow);
                }


                // 保存流向码头数据
                if (flow.getTerminals() != null && !flow.getTerminals().isEmpty()) {

                    //删除旧的
                    QueryWrapper terminalQuery = QueryWrapper.create()
                            .eq(ContractFlowTerminal::getFlowId, flow.getFlowId());

                    contractFlowTerminalMapper.deleteByQuery(terminalQuery);

                    for (ContractFlowTerminal terminal : flow.getTerminals()) {
                        terminal.setFlowTerminalId(null);
                        terminal.setFlowId(flow.getFlowId());
                        contractFlowTerminalMapper.insert(terminal);
                    }
                }
            }
            
            return AjaxResult.success("保存成功");
        } catch (Exception e) {
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult getFlowsByContractId(String contractId) {
        try {
            // 查询流向列表
            QueryWrapper flowQuery = QueryWrapper.create()
                    .eq(ContractFlow::getContractId, contractId)
                    .orderBy(ContractFlow::getCreateTime,true);
            
            List<ContractFlow> flows = contractFlowMapper.selectListByQuery(flowQuery);
            
            // 查询每个流向的码头数据
            for (ContractFlow flow : flows) {
                QueryWrapper terminalQuery = QueryWrapper.create()
                        .eq(ContractFlowTerminal::getFlowId, flow.getFlowId())
                        .orderBy(ContractFlowTerminal::getOrderNum,true);
                
                List<ContractFlowTerminal> terminals = contractFlowTerminalMapper.selectListByQuery(terminalQuery);
                flow.setTerminals(terminals);
            }
            
            return AjaxResult.success(flows);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult deleteFlow(String flowId) {
        try {
            // 删除流向码头数据
            QueryWrapper terminalQuery = QueryWrapper.create()
                    .eq(ContractFlowTerminal::getFlowId, flowId);
            contractFlowTerminalMapper.deleteByQuery(terminalQuery);
            
            // 删除流向数据
            QueryWrapper flowQuery = QueryWrapper.create()
                    .eq(ContractFlow::getFlowId, flowId);
            contractFlowMapper.deleteByQuery(flowQuery);
            
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }
} 