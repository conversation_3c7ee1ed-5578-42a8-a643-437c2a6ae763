package com.ruoyi.system.service.impl;

import com.mybatisflex.core.query.QueryWrapper;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysFileType;

import com.ruoyi.system.mapper.SysFileManageMapper;
import com.ruoyi.system.mapper.SysFileTypeMapper;
import com.ruoyi.system.service.ISysFileManageService;
import com.ruoyi.system.service.ISysFileTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.domain.SysFileManage;

import java.util.List;

import static com.ruoyi.system.domain.table.SysFileTypeTableDef.SYS_FILE_TYPE;
import static com.ruoyi.system.domain.table.SysFileManageTableDef.SYS_FILE_MANAGE;
import static javax.management.Query.eq;

@Service
@Transactional(rollbackFor = Exception.class)
public class SysFileTypeServiceImpl extends ServiceImpl<SysFileTypeMapper, SysFileType> implements ISysFileTypeService {

    @Autowired
    ISysFileManageService sysFileManageService;

    @Override
    public boolean insert(SysFileType sysFileType) throws Exception {
        checkUnique(sysFileType);
        return super.save(sysFileType);
    }

    @Override
    public boolean update(SysFileType sysFileType) throws Exception {
        checkUnique(sysFileType);
        return super.updateById(sysFileType);
    }

    @Override
    public boolean remove(List<Long> ids) throws Exception {
        checkUse(ids);
        return super.removeByIds(ids);
    }

    public void checkUnique(SysFileType sysFileType) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(SYS_FILE_TYPE)
                .eq(SysFileType::getFileType,sysFileType.getFileType())
                .eq(SysFileType::getFileBusinessType,sysFileType.getFileBusinessType());
        
        if (StringUtils.isNotNull(sysFileType.getFileTypeId())) {
            queryWrapper.ne(SysFileType::getFileTypeId,sysFileType.getFileTypeId());
        }

        List<SysFileType> checkList = super.list(queryWrapper);

        if(!checkList.isEmpty()){
            throw new Exception("文件类型(一级) 和 文件类型(二级) 不唯一");
        }
    }

    public void checkUse(List<Long> ids) throws Exception {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select()
                .from(SYS_FILE_MANAGE)
                .where(SYS_FILE_MANAGE.FILE_TYPE_ID.in(ids));

        List<SysFileManage> sysFileManages = sysFileManageService.list(queryWrapper);

        if(!sysFileManages.isEmpty()){
            throw new Exception("该文件类型正在被使用");
        }
    }

}
