package com.ruoyi.booking.domain.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 批量删除请求DTO
 * 用于接收前端批量删除操作的请求参数
 * 
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
public class BatchDeleteRequestDTO {

    /**
     * 订舱ID列表
     * 对应 booking_main 表的主键ID数组
     */
    @NotEmpty(message = "请选择要删除的订舱记录")
    @Size(min = 1, max = 50, message = "单次删除数量必须在1-50条之间")
    private List<String> bookingIds;
}