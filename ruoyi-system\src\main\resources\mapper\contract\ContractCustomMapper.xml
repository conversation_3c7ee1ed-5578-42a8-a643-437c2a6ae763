<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractCustomMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.contract.domain.ContractCustom">
        <result column="contract_id" jdbcType="VARCHAR" property="contractId"/>
        <result column="cus_id" jdbcType="VARCHAR" property="cusId"/>
        <result column="contract_role" jdbcType="VARCHAR" property="contractRole"/>
    </resultMap>
    <sql id="Base_Column_List">
        `contract_id`, `cus_id`, `contract_role`
    </sql>

</mapper>