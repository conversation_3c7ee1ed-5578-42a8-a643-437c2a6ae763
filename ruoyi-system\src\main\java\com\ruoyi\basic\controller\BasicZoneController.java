package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicZone;
import com.ruoyi.basic.domain.BasicZoneTerminal;
import com.ruoyi.basic.mapper.BasicZoneTerminalMapper;
import com.ruoyi.basic.service.IBasicZoneService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/system/zone")
public class BasicZoneController extends BaseController {

    @Autowired
    private IBasicZoneService basicZoneService;

    @Autowired
    private BasicZoneTerminalMapper basicZoneTerminalMapper;

    /**
     * 查询关区管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:zone:list')")
    @GetMapping("/list")
    public Page<BasicZone> list(BasicZone basicZone)
    {
        // startPage();
        // List<BasicZone> list = basicZoneService.selectBasicZoneList(basicZone);
        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (StringUtils.isNotEmpty(basicZone.getZoneCode())) {
            queryWrapper.like("zone_code", basicZone.getZoneCode());
        }
        if (StringUtils.isNotEmpty(basicZone.getZoneName())) {
            queryWrapper.like("zone_name", basicZone.getZoneName());
        }
        queryWrapper.orderBy("create_time", true);
        return basicZoneService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    @PreAuthorize("@ss.hasPermi('system:zone:list')")
    @PostMapping("/listDetail")
    public Page<BasicZoneTerminal> listDetail(@RequestBody List<String> zoneIds){

        // startPage();
        // List<BasicZoneTerminal> list = basicZoneTerminalMapper.selectListByQuery(QueryWrapper.create()
        //         .in("zone_id", zoneIds)
        //         .orderBy("create_time",true)
        // );

        // return getDataTable(list);
        var pageDomain = TableSupport.buildPageRequest();
        QueryWrapper queryWrapper = QueryWrapper.create()
        .in("zone_id", zoneIds)
        .orderBy("create_time",true);
        return basicZoneTerminalMapper.paginate(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 导出关区管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:zone:export')")
    @Log(title = "关区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicZone basicZone)
    {
        List<BasicZone> list = basicZoneService.selectBasicZoneList(basicZone);
        ExcelUtil<BasicZone> util = new ExcelUtil<BasicZone>(BasicZone.class);
        util.exportExcel(response, list, "关区管理数据");
    }

    /**
     * 获取关区管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:zone:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(basicZoneService.selectBasicZoneById(id));
    }

    /**
     * 新增关区管理
     */
    @PreAuthorize("@ss.hasPermi('system:zone:add')")
    @Log(title = "关区管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BasicZone basicZone)
    {
        System.out.println(basicZone);

        try {
            return toAjax(basicZoneService.insertBasicZone(basicZone));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改关区管理
     */
    @PreAuthorize("@ss.hasPermi('system:zone:edit')")
    @Log(title = "关区管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicZone basicZone)
    {
        try {
            return toAjax(basicZoneService.updateBasicZone(basicZone));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除关区管理
     */
    @PreAuthorize("@ss.hasPermi('system:zone:remove')")
    @Log(title = "关区管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@RequestBody List<String> ids)
    {
        return toAjax(basicZoneService.deleteBasicZoneByIds(ids));
    }

}
