package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_endurance")
public class BasicEndurance extends BaseEntity {

    /** 航时信息ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 起始码头 */
    @Excel(name = "起始码头")
    private String startTerminalId;

    /** 目的码头 */
    @Excel(name = "目的码头")
    private String endTerminalId;

    /** 航行小时 */
    @Excel(name = "航行小时")
    private BigDecimal voyageTime;

    /** 是否往返一样 Y是 N否 */
    @Excel(name = "是否往返一样 Y是 N否")
    private String isSame;

    /** 乐观锁 */
    @Excel(name = "乐观锁")
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

}
