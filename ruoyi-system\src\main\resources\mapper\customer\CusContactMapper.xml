<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.customer.mapper.CusContactMapper">
    <delete id="deleteCusContactByCusId">
        UPDATE cus_contact
        SET del_flag = '2'
        WHERE cus_main_id = #{cusMainId}
    </delete>
    <select id="selectCusContactListByCusId" resultType="com.ruoyi.customer.domain.CusContact">
        SELECT * FROM cus_contact
        WHERE del_flag = '0'
        AND cus_main_id = #{cusMainId}
    </select>
</mapper>