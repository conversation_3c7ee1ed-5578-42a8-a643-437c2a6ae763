package com.ruoyi.booking.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.mybatisflex.annotation.*;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Table("LOGISTIC_WATERWAY")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class LogisticWaterway extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    @Excel(name = "物流主表ID")
    @Column("LOGISTICS_MAIN_ID")
    private String logisticsMainId;

    // 航线信息
    @Excel(name = "航线ID")
    @Column("ROUTE_ID")
    private String routeId;

    @Excel(name = "航线名称")
    @Column("ROUTE_NAME")
    private String routeName;

    @Excel(name = "船舶ID")
    @Column("VESSEL_ID")
    private String vesselId;

    @Excel(name = "船舶名称")
    @Column("VESSEL_NAME")
    private String vesselName;

    @Excel(name = "航次号")
    @Column("VOYAGE_NO")
    private String voyageNo;

    // 港口信息
    @Excel(name = "装货港ID")
    @Column("LOADING_TERMINAL_ID")
    private String loadingTerminalId;

    @Excel(name = "装货港名称")
    @Column("LOADING_TERMINAL_NAME")
    private String loadingTerminalName;

    @Excel(name = "卸货港ID")
    @Column("UNLOADING_TERMINAL_ID")
    private String unloadingTerminalId;

    @Excel(name = "卸货港名称")
    @Column("UNLOADING_TERMINAL_NAME")
    private String unloadingTerminalName;

    // 时间信息
    @Excel(name = "预计离港时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column("ETD")
    private Date etd;

    @Excel(name = "预计到港时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column("ETA")
    private Date eta;

    @Excel(name = "实际离港时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column("ATD")
    private Date atd;

    @Excel(name = "实际到港时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column("ATA")
    private Date ata;

    // 运输段顺序号，用于多段运输
    @Excel(name = "运输段顺序号")
    @Column("SEQUENCE_NO")
    private Integer sequenceNo;



    @Excel(name = "状态", readConverterExp = "PENDING=待处理,SPLIT=已拆解,ALLOCATED=已分配,CANCEL=已取消")
    @Column("STATUS")
    private String status;

    @Excel(name = "拆单操作员")
    @Column("SPLIT_BY")
    private String splitBy;

    @Excel(name = "拆单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column("SPLIT_TIME")
    private Date splitTime;

    // 配载信息
    @Excel(name = "配载计划ID")
    @Column("STOWAGE_PLAN_ID")
    private String stowagePlanId;

    // 系统字段
    @Excel(name = "备注")
    @Column("REMARK")
    private String remark;

    @Excel(name = "删除标记")
    @Column("DEL_FLAG")
    private String delFlag;

    @Column("VERSION")
    private Long version;

    @Excel(name = "客户名称")
    @Column(ignore = true)
    private String shipperName;

    @Excel(name = "箱量信息")
    @Column(ignore = true)
    private String containerAllocation;

    @Excel(name = "箱量明细")
    @Column(ignore = true)
    private List<BookingCntrNumSplit> bookingCntrNumSplitList;
}