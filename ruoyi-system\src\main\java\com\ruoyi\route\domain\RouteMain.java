package com.ruoyi.route.domain;

import java.util.Date;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 航线主线对象 route_main
 */
@Data
@Table("ROUTE_MAIN")
@EqualsAndHashCode(callSuper = true)
public class RouteMain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主线ID */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String mainId;

    /** 主线名称 */
    @Excel(name = "主线名称")
    private String mainName;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 乐观锁版本号 */
    private Long version;
} 