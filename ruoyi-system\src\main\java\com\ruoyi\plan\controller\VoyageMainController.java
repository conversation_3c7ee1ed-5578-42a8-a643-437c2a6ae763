package com.ruoyi.plan.controller;

import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.plan.domain.VoyageMain;
import com.ruoyi.plan.domain.VoyageMainVO;
import com.ruoyi.plan.service.IVoyageMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/plan/voyage")
public class VoyageMainController extends BaseController {
    
    @Autowired
    private IVoyageMainService voyageMainService;

    /**
     * 查询航次列表
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:list')")
    @GetMapping("/list")
    public TableDataInfo list(VoyageMain voyageMain) {
        var pageDomain = TableSupport.buildPageRequest();
        Page<VoyageMain> page = voyageMainService.selectVoyageMainPage(
                pageDomain.getPageNum(), 
                pageDomain.getPageSize(), 
                voyageMain);
        return getDataTable(page.getRecords());
    }

    /**
     * 查询航次列表（包含船名）
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:list')")
    @GetMapping("/listWithShipName")
    public TableDataInfo listWithShipName(VoyageMain voyageMain) {
        var pageDomain = TableSupport.buildPageRequest();
        Page<VoyageMainVO> page = voyageMainService.selectVoyageMainVOPage(
                pageDomain.getPageNum(), 
                pageDomain.getPageSize(), 
                voyageMain);
        return getDataTable(page.getRecords());
    }

    /**
     * 导出航次列表
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:export')")
    @Log(title = "航次", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VoyageMain voyageMain) {
        List<VoyageMain> list = voyageMainService.selectVoyageMainList(voyageMain);
        ExcelUtil<VoyageMain> util = new ExcelUtil<>(VoyageMain.class);
        util.exportExcel(response, list, "航次数据");
    }

    /**
     * 根据航次编号获取详细信息
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(voyageMainService.selectVoyageMainById(id));
    }

    /**
     * 新增航次
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:add')")
    @Log(title = "航次", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VoyageMain voyageMain) {
        return toAjax(voyageMainService.insertVoyageMain(voyageMain));
    }

    /**
     * 修改航次
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:edit')")
    @Log(title = "航次", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VoyageMain voyageMain) {
        return toAjax(voyageMainService.updateVoyageMain(voyageMain));
    }

    /**
     * 删除航次
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:remove')")
    @Log(title = "航次", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(voyageMainService.deleteVoyageMainByIds(ids));
    }

    /**
     * 生成航次号
     */
//    @PreAuthorize("@ss.hasPermi('plan:voyage:add')")
    @GetMapping("/generateVoyageNo")
    public AjaxResult generateVoyageNo() {
        String voyageNo = voyageMainService.generateVoyageNo();
        return success(voyageNo);
    }
}
