package com.ruoyi.basic.service;

import com.mybatisflex.core.service.IService;
import com.ruoyi.basic.domain.BasicCntrIso;

import java.util.List;

public interface IBasicCntrIsoService extends IService<BasicCntrIso> {

    /**
     * 查询箱ISO
     *
     * @param id 箱ISO主键
     * @return 箱ISO
     */
    public BasicCntrIso selectBasicCntrIsoById(String id);

    /**
     * 查询箱ISO列表
     *
     * @param basicCntrIso 箱ISO
     * @return 箱ISO集合
     */
    public List<BasicCntrIso> selectBasicCntrIsoList(BasicCntrIso basicCntrIso);

    /**
     * 新增箱ISO
     *
     * @param basicCntrIso 箱ISO
     * @return 结果
     */
    public int insertBasicCntrIso(BasicCntrIso basicCntrIso);

    /**
     * 修改箱ISO
     *
     * @param basicCntrIso 箱ISO
     * @return 结果
     */
    public int updateBasicCntrIso(BasicCntrIso basicCntrIso);

    /**
     * 批量删除箱ISO
     *
     * @param ids 需要删除的箱ISO主键集合
     * @return 结果
     */
    public int deleteBasicCntrIsoByIds(List<String> ids);

    /**
     * 删除箱ISO信息
     *
     * @param id 箱ISO主键
     * @return 结果
     */
    public int deleteBasicCntrIsoById(String id);

}
