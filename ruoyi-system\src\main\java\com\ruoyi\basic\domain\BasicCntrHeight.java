package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_cntr_height")
public class BasicCntrHeight extends BaseEntity {

    /** 箱高ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 箱高度代码 */
    @Excel(name = "箱高度代码")
    private String heightCode;

    /** 箱高度名称 */
    @Excel(name = "箱高度名称")
    private String heightName;

    /** ISO代码 */
    @Excel(name = "ISO代码")
    private String heightIso;

    /** 高箱标志 */
    @Excel(name = "高箱标志")
    private String heightSign;

    /** 描述 */
    @Excel(name = "描述")
    private String heightDescription;

    /** 乐观锁 */
    @Excel(name = "乐观锁")
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

}
