package com.ruoyi.booking.domain;

import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.core.domain.BaseEntity;
import com.mybatisflex.core.keygen.KeyGenerators;

/**
 * 中转港表
 * 严格按项目经理中转港表字段设计，记录运输中转港信息
 */
@Data
@Table("BOOKING_TRANSIT_PORT")
@EqualsAndHashCode(callSuper = false)
public class BookingTransitPort extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键，雪花ID全局唯一标识符 */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private String id;

    /** 关联订舱主表ID，建立订舱与中转港的关系 */
    @Column("BOOKING_ID")
    private String bookingId;

    /** 序号，中转港的顺序编号（按中转先后顺序） */
    @Column("SEQUENCE_NO")
    private Integer sequenceNo;

    /** 中转港ID，关联港口基础资料表 */
    @Column("TRANSIT_PORT_ID")
    private String transitPortId;

    /** 中转港名称（冗余存储便于查询） */
    @Column("TRANSIT_PORT_NAME")
    private String transitPortName;

    /** 代理公司ID，关联代理公司基础资料表 */
    @Column("AGENT_ID")
    private String agentId;

    /** 代理公司名称（冗余存储便于查询） */
    @Column("AGENT_NAME")
    private String agentName;

    /** 逻辑删除标志：0正常 2删除 */
    @Column(value = "DEL_FLAG", isLogicDelete = true)
    private String delFlag;

    /** 乐观锁版本号，防止并发修改 */
    @Column(value = "VERSION", version = true)
    private Integer version;
}