package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("basic_danger")
public class BasicDanger extends BaseEntity {

    /** 危险品等级ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 危险品等级 */
    @Excel(name = "危险品等级")
    private String dangerLevel;

    /** 危险品等级名称 */
    @Excel(name = "危险品等级名称")
    private String dangerName;

    /** 危险品等级英文名称 */
    @Excel(name = "危险品等级英文名称")
    private String dangerEnName;

    /** 危险品大类 */
    @Excel(name = "危险品大类")
    private String dangerClass;

    /** 注意事项 */
    @Excel(name = "注意事项")
    private String attention;

    /** 隔离要求 */
    @Excel(name = "隔离要求")
    private String isolationRequest;

    /** 乐观锁 */
    @Column(version = true)
    private Integer version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;

    /** 危险品联合国编号信息 */
    @Column(ignore = true)
    private List<BasicDangerUnNumber> basicDangerUnNumberList;

}
