package com.ruoyi.contract.controller;

import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.FeeCategory;
import com.ruoyi.contract.service.FeeCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 费目分类 控制层。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@RestController
@RequestMapping("/feeCategory")
public class FeeCategoryController extends BaseController {

    @Autowired
    private FeeCategoryService feeCategoryService;

    /**
     * 添加 费目分类
     *
     * @param feeCategory 费目分类
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('billing:feeCategory:add')")
    @Log(title = "费目分类", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@RequestBody @Validated FeeCategory feeCategory) {
        return toAjax(feeCategoryService.save(feeCategory));
    }


    /**
     * 根据主键删除费目分类
     *
     * @param id 主键
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('billing:feeCategory:remove')")
    @Log(title = "费目分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Serializable id) {
        return toAjax(feeCategoryService.removeById(id));
    }


    /**
     * 根据主键更新费目分类
     *
     * @param feeCategory 费目分类
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('billing:feeCategory:edit')")
    @Log(title = "费目分类", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody @Validated FeeCategory feeCategory) {
        return toAjax(feeCategoryService.updateById(feeCategory));
    }


    /**
     * 查询所有费目分类
     *
     * @return 所有数据
     */
    @GetMapping("/list")
    public AjaxResult list() {
        List<FeeCategory> list = feeCategoryService.list();
        return success(list);
    }


    /**
     * 根据费目分类主键获取详细信息。
     *
     * @param id feeCategory主键
     * @return 费目分类详情
     */
    @GetMapping("/getInfo/{id}")
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return success(feeCategoryService.getById(id));
    }


    /**
     * 分页查询费目分类
     *
     * @param feeCategory 查询条件
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页对象
     */
    @GetMapping("/page")
    public Page<FeeCategory> page(FeeCategory feeCategory, Integer pageNum, Integer pageSize) {
        return feeCategoryService.selectPage(feeCategory, pageNum, pageSize);
    }
}