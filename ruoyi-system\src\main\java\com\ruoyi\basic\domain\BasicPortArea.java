package com.ruoyi.basic.domain;

import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Table("basic_port_area")
@Data
public class BasicPortArea extends BaseEntity {
    /** 港区ID */
    @Id(keyType = KeyType.Generator,value = KeyGenerators.snowFlakeId)
    private String id;

    /** 港区代码 */
    @Excel(name = "港区代码")
    private String portAreaCode;

    /** 港区名称 */
    @Excel(name = "港区名称")
    private String portAreaName;

    /** 港区英文名称 */
    @Excel(name = "港区英文名称")
    private String portAeraEnName;

    /** 港口ID */
    @Excel(name = "港口ID")
    private String portId;

    /** 所属海关 */
    @Excel(name = "所属海关")
    private String belongCustoms;

    /** 乐观锁 */
    @Column(version = true)
    private Long version;

    /** 逻辑删除 */
    @Column(isLogicDelete = true)
    private String delFlag;
}
