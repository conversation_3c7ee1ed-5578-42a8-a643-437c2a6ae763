package com.ruoyi.basic.controller;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.basic.domain.BasicCntrMainOwn;
import com.ruoyi.basic.service.IBasicCntrMainOwnService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;



/**
 * 自有箱基础信息控制器
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/system/cntrMainOwn")
public class BasicCntrMainOwnController extends BaseController {

    @Autowired
    private IBasicCntrMainOwnService basicCntrMainOwnService;

    /**
     * 新增自有箱信息
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BasicCntrMainOwn basicCntrMainOwn) {
        return basicCntrMainOwnService.addBasicCntrMainOwn(basicCntrMainOwn);
    }

    /**
     * 分页查询自有箱列表
     */
    @GetMapping("/list")
    public Page<BasicCntrMainOwn> list(BasicCntrMainOwn basicCntrMainOwn) {
        QueryWrapper queryWrapper = QueryWrapper.create();

        if (StringUtils.isNotEmpty(basicCntrMainOwn.getCntrNo())) {
            queryWrapper.like("cntr_no", basicCntrMainOwn.getCntrNo());
        }
        if (StringUtils.isNotEmpty(basicCntrMainOwn.getCntrSize())) {
            queryWrapper.and("cntr_size like ?", "%" + basicCntrMainOwn.getCntrSize() + "%");
        }
        if (StringUtils.isNotEmpty(basicCntrMainOwn.getCntrType())) {
            queryWrapper.and("cntr_type like ?", "%" + basicCntrMainOwn.getCntrType() + "%");
        }
        if (StringUtils.isNotEmpty(basicCntrMainOwn.getManufacturer())) {
            queryWrapper.and("manufacturer like ?", "%" + basicCntrMainOwn.getManufacturer() + "%");
        }
        queryWrapper.orderBy("create_time desc");

        var pageDomain = TableSupport.buildPageRequest();
        return basicCntrMainOwnService.page(new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
    }

    /**
     * 标签查询（用于下拉选择等）
     */
    @PostMapping("/label")
    public List<BasicCntrMainOwn> label(@RequestBody BasicCntrMainOwn basicCntrMainOwn) {
        QueryWrapper queryWrapper = QueryWrapper.create();

        if (StringUtils.isNotEmpty(basicCntrMainOwn.getCntrNo())) {
            queryWrapper.like("cntr_no", basicCntrMainOwn.getCntrNo());
        }
        if (StringUtils.isNotEmpty(basicCntrMainOwn.getCntrSize())) {
            queryWrapper.and("cntr_size like ?", "%" + basicCntrMainOwn.getCntrSize() + "%");
        }
        if (StringUtils.isNotEmpty(basicCntrMainOwn.getCntrType())) {
            queryWrapper.and("cntr_type like ?", "%" + basicCntrMainOwn.getCntrType() + "%");
        }
        queryWrapper.orderBy("create_time desc");
        return basicCntrMainOwnService.list(queryWrapper);
    }

    /**
     * 根据ID查询自有箱信息
     */
    @GetMapping("/getById/{id}")
    public AjaxResult getById(@PathVariable("id") String id) {
        BasicCntrMainOwn basicCntrMainOwn = basicCntrMainOwnService.getById(id);
        return AjaxResult.success(basicCntrMainOwn);
    }

    /**
     * 修改自有箱信息
     */
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody BasicCntrMainOwn basicCntrMainOwn) {
        return basicCntrMainOwnService.editBasicCntrMainOwn(basicCntrMainOwn);
    }

    /**
     * 删除自有箱信息
     */
    @DeleteMapping("/deleteByIds")
    public AjaxResult remove(@RequestBody List<String> ids) {
        return AjaxResult.success(basicCntrMainOwnService.removeByIds(ids));
    }
}
