//package com.ruoyi.framework.config;
//
//import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
//import com.ruoyi.common.utils.SecurityUtils;
//import org.apache.ibatis.reflection.MetaObject;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
//@Component
//public class BaseMetaObjectHandler implements MetaObjectHandler {
//    @Override
//    public void insertFill(MetaObject metaObject) {
//        System.out.println("start insert fill ....");
//        if(metaObject.hasSetter("createBy")){
//            try {
//                metaObject.setValue("createBy",null);
//                this.strictInsertFill(metaObject, "createBy", String.class, SecurityUtils.getUsername());
//            }catch (Exception e){
//                this.strictInsertFill(metaObject, "createBy", String.class, "");
//            }
//        }
//
//        if(metaObject.hasSetter("createTime")){
//            metaObject.setValue("createTime",null);
//            this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
//        }
//
//        if(metaObject.hasSetter("createName")){
////            System.out.println("=======================");
//            try {
//                metaObject.setValue("createName",null);
//                this.strictInsertFill(metaObject, "createName", String.class, SecurityUtils.getUsername());
//            }catch (Exception e){
//                this.strictInsertFill(metaObject, "createName", String.class, "");
//            }
//        }
//    }
//
//    @Override
//    public void updateFill(MetaObject metaObject) {
//        System.out.println("start update fill ....");
//        if(metaObject.hasSetter("updateBy")){
//            try {
//                metaObject.setValue("updateBy",null);
//                this.strictUpdateFill(metaObject,"updateBy",String.class,SecurityUtils.getUsername());
//            }catch (Exception e){
//                this.strictUpdateFill(metaObject,"updateBy",String.class,"");
//            }
//        }
//
//        if(metaObject.hasSetter("updateTime")){
//            metaObject.setValue("updateTime",null);
//            this.strictUpdateFill(metaObject,"updateTime",Date.class,new Date());
//        }
//
//
//        if(metaObject.hasSetter("updateName")){
//            try {
//                metaObject.setValue("updateName",null);
//                this.strictUpdateFill(metaObject,"updateName",String.class,SecurityUtils.getUsername());
//            }catch (Exception e){
//                this.strictUpdateFill(metaObject,"updateName",String.class,"");
//            }
//        }
//    }
//}
